{"validation_status": "FAIL", "system_integrity_percent": 37.5, "summary": {"total_tests": 16, "passed": 6, "warnings": 0, "critical_failures": 10}, "test_results": [{"component": "ImportPaths", "test": "hawkes_engine", "status": "PASS", "details": "Import successful"}, {"component": "ImportPaths", "test": "fisher_information_monitor", "status": "PASS", "details": "Import successful"}, {"component": "ImportPaths", "test": "scaling_patterns", "status": "PASS", "details": "Import successful"}, {"component": "ImportPaths", "test": "three_oracle_architecture", "status": "PASS", "details": "Import successful"}, {"component": "ImportPaths", "test": "echo_detector", "status": "PASS", "details": "Import successful"}, {"component": "ImportPaths", "test": "predict", "status": "PASS", "details": "Import successful"}, {"component": "RGScaler", "test": "import_test", "status": "CRITICAL_FAIL", "details": "cannot import name 'RGScaler' from 'ironpulse.core.scaling_patterns' (/Users/<USER>/IRONPULSE/ironpulse/core/scaling_patterns.py)"}, {"component": "FisherMonitor", "test": "import_test", "status": "CRITICAL_FAIL", "details": "unsupported format string passed to FisherSpikeResult.__format__"}, {"component": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "test": "import_test", "status": "CRITICAL_FAIL", "details": "HawkesEngine.calculate_intensity() got an unexpected keyword argument 'current_time'"}, {"component": "HTFController", "test": "import_test", "status": "CRITICAL_FAIL", "details": "cannot import name 'HTFMasterController' from 'ironpulse.core.temporal_correlator' (/Users/<USER>/IRONPULSE/ironpulse/core/temporal_correlator.py)"}, {"component": "Compartments", "test": "system_test", "status": "CRITICAL_FAIL", "details": "No module named 'data_pipeline'"}, {"component": "Compartments", "test": "dependency_test", "status": "CRITICAL_FAIL", "details": "cannot access local variable 'Lvl1EnhanceCompartment' where it is not associated with a value"}, {"component": "ThreeOracle", "test": "import_test", "status": "CRITICAL_FAIL", "details": "cannot import name 'ThreeOracleArchitecture' from 'ironpulse.predictors.oracle.three_oracle_architecture' (/Users/<USER>/IRONPULSE/ironpulse/predictors/oracle/three_oracle_architecture.py)"}, {"component": "EchoDetector", "test": "import_test", "status": "CRITICAL_FAIL", "details": "'EchoDetector' object has no attribute 'detect_echo_pattern'"}, {"component": "GrammarBridge", "test": "import_test", "status": "CRITICAL_FAIL", "details": "name 'Dict' is not defined"}, {"component": "UnifiedAPI", "test": "import_test", "status": "CRITICAL_FAIL", "details": "cannot import name 'UnifiedOracleAPI' from 'ironpulse.util.unified_oracle_api' (/Users/<USER>/IRONPULSE/ironpulse/util/unified_oracle_api.py)"}], "critical_failures": [{"component": "RGScaler", "test": "import_test", "status": "CRITICAL_FAIL", "details": "cannot import name 'RGScaler' from 'ironpulse.core.scaling_patterns' (/Users/<USER>/IRONPULSE/ironpulse/core/scaling_patterns.py)"}, {"component": "FisherMonitor", "test": "import_test", "status": "CRITICAL_FAIL", "details": "unsupported format string passed to FisherSpikeResult.__format__"}, {"component": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "test": "import_test", "status": "CRITICAL_FAIL", "details": "HawkesEngine.calculate_intensity() got an unexpected keyword argument 'current_time'"}, {"component": "HTFController", "test": "import_test", "status": "CRITICAL_FAIL", "details": "cannot import name 'HTFMasterController' from 'ironpulse.core.temporal_correlator' (/Users/<USER>/IRONPULSE/ironpulse/core/temporal_correlator.py)"}, {"component": "Compartments", "test": "system_test", "status": "CRITICAL_FAIL", "details": "No module named 'data_pipeline'"}, {"component": "Compartments", "test": "dependency_test", "status": "CRITICAL_FAIL", "details": "cannot access local variable 'Lvl1EnhanceCompartment' where it is not associated with a value"}, {"component": "ThreeOracle", "test": "import_test", "status": "CRITICAL_FAIL", "details": "cannot import name 'ThreeOracleArchitecture' from 'ironpulse.predictors.oracle.three_oracle_architecture' (/Users/<USER>/IRONPULSE/ironpulse/predictors/oracle/three_oracle_architecture.py)"}, {"component": "EchoDetector", "test": "import_test", "status": "CRITICAL_FAIL", "details": "'EchoDetector' object has no attribute 'detect_echo_pattern'"}, {"component": "GrammarBridge", "test": "import_test", "status": "CRITICAL_FAIL", "details": "name 'Dict' is not defined"}, {"component": "UnifiedAPI", "test": "import_test", "status": "CRITICAL_FAIL", "details": "cannot import name 'UnifiedOracleAPI' from 'ironpulse.util.unified_oracle_api' (/Users/<USER>/IRONPULSE/ironpulse/util/unified_oracle_api.py)"}], "warnings": []}
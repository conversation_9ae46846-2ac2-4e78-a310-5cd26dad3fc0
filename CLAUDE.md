# IRONPULSE Development Guide
## Information Recognition and Oracle Network for Pushdown Understanding of Liquidity and Statistical Events

## 🎯 Core Mission
IRONPULSE is a breakthrough multi-timeframe prediction system implementing Type-2 Context-Free Grammar parsing for financial market cascade timing prediction. The system combines advanced mathematical architectures with production-ready compartment orchestration to achieve unprecedented accuracy in liquidity event timing.

## 🏛️ Development Principles

### Feature Consolidation First
**CRITICAL RULE: Always check existing features before adding new code**

Before implementing any new functionality:

1. **Feature Audit**: Search `src/project_oracle/` for existing implementations
   - `src/project_oracle/features/` - Feature analysis and engineering
   - `src/project_oracle/predictors/` - Specialized prediction algorithms  
   - `src/project_oracle/util/` - Utility and helper functions
   - `core_predictor/` - Mathematical and core prediction components

2. **Enhancement Over Recreation**: Build onto existing features rather than creating duplicates
   - Extend existing classes with additional methods
   - Add configuration parameters to existing systems
   - Integrate with current mathematical frameworks

3. **Architecture Consistency**: Maintain the established 5-layer mathematical architecture
   - Theory → Algorithm → Integration → Validation → API
   - Use existing interfaces and patterns
   - Preserve mathematical rigor and performance optimizations

### Code Organization Rules
- **No Duplication**: If similar functionality exists, extend it
- **Package Respect**: Keep features in their designated packages
- **Mathematical Integrity**: All new code must pass existing validation frameworks
- **Performance Preservation**: Maintain O(n) grammar parsing and O(n log n) FFT optimizations
- **NO FALLBACKS**: Fallbacks hide breakages - address root causes directly

## 🏗️ System Architecture

### Primary Components
1. **Mathematical Layers Framework** (`/core_predictor/mathematical_layers/`) - 5-layer mathematical architecture with O(n log n) FFT optimization
2. **Type-2 Context-Free Grammar System** - Proven cascade pattern recognition with 93.1% context-free classification
3. **Three-Oracle Architecture** - Metacognition-resistant prediction system with echo detection
4. **Production Compartments** - Orchestrated processing pipeline with dependency management
5. **HTF (Higher Timeframe) Integration** - Multi-scale Hawkes processes with fractal cascade architecture

### Core Files & Locations
- **Primary Oracle Core**: `oracle_core.py` (Protected by invariant guards)
- **Grammar Parser**: `market_cascade_pda.py` (Type-2 CFG Pushdown Automaton)
- **Mathematical Framework**: `/core_predictor/mathematical_layers/` (5-layer architecture)
- **Production System**: `production_oracle.py` (FastAPI integration)
- **Compartment Orchestrator**: `run_compartments.py` (Processing pipeline)
- **Configuration**: `compartments.yml` (DAG workflow definitions)

## 🔧 Development Commands

### Essential Testing & Validation
```bash
# Component-level testing (safe, individual components)
python core_predictor/rg_scaler_production.py         # RG Scaler only
python core_predictor/fisher_information_monitor.py   # Fisher Monitor only
python core_predictor/hawkes_engine.py               # Hawkes Engine only
python metacognitive_loop_detector.py                # Loop detection only

# Mathematical framework testing
python -m core_predictor.mathematical_layers         # Test 5-layer architecture
python core_predictor/mathematical_hooks.py          # Parameter drift detection
python core_predictor/scaling_patterns.py            # Performance scaling tests

# Production validation (comprehensive)
python run_compartments.py --sequence production_validation --manifest data_manifest_final.json

# Grammar system testing
python market_cascade_pda.py                         # Test Type-2 CFG parser
python pumping_lemma_validator.py                    # Validate mathematical proofs
```

### Production Operations
```bash
# Full production pipeline
python run_compartments.py --sequence enhanced_pipeline --manifest data_manifest_final.json

# Mathematical optimization pipeline
python run_compartments.py --sequence mathematical_pipeline --manifest data_manifest_final.json

# FastAPI production server
uvicorn production_oracle:app --host 0.0.0.0 --port 8000

# System health checks
python production_validation_core.py                 # Core validation
python -c "from invariants import guard; print(guard.checkpoint())"  # Guard system health
```

### Debugging & Diagnostics
```bash
# CRITICAL: Main system timeout issues - DO NOT USE IN PRODUCTION
# python oracle.py                    # ❌ TIMEOUT ISSUE
# python three_oracle_architecture.py # ❌ TIMEOUT ISSUE

# Safe debugging approaches
python test_oracle_imports.py        # Test import dependencies
python test_without_xgboost.py       # Test minimal dependencies
python test_native_imports.py        # Test core imports only

# Performance profiling
python -c "import cProfile; cProfile.run('import oracle_core')"
```

## 📊 System Performance & SLAs

### Validated Performance Metrics
- **Model Accuracy**: 97.01% (production validated)
- **Temporal Stability**: 94.74% stability score
- **Latency SLA**: <200ms (mathematical optimization), <5000ms (full system)
- **Success Rate**: ≥95% (production requirement)
- **Grammar Parsing**: O(n) deterministic complexity, 5.5x speedup vs O(n²) methods

### Mathematical Guarantees
- **Type-2 CFG Classification**: 93.1% of patterns are context-free (Pumping Lemma validated)
- **Stack Depth**: ≤3 (minimal memory requirements)
- **FFT Optimization**: O(n²) → O(n log n) complexity reduction for correlation calculations
- **Hawkes Process Accuracy**: Mathematical formulas implemented exactly as specified

## 🔒 Critical Security & Invariants

### Protected Functions (oracle_core.py)
The core grammar parsing functions are protected by invariant guards to prevent architectural drift:
- `translate_taxonomy()` - Session→Pattern taxonomy translation (PRODUCTION FIX)
- `parse_cascade_grammar()` - Type-2 CFG parsing core
- `predict_pattern_completion()` - Next event prediction from partial patterns
- `validate_grammatical_consistency()` - Mathematical validation

### Guard System Usage
```python
from invariants import guard

# Check system health
checkpoint = guard.checkpoint()
print(f"Functions protected: {checkpoint['total_functions']}")
print(f"System coherence: {checkpoint['coherence']:.1%}")
print(f"Drift events: {checkpoint['drift_events']}")
```

## 🗂️ Directory Structure & Navigation

```
project_oracle/
├── oracle_core.py                     # 🔒 Protected CFG core (DO NOT MODIFY)
├── market_cascade_pda.py              # Type-2 grammar parser implementation
├── production_oracle.py               # FastAPI production server
├── run_compartments.py               # Processing pipeline orchestrator
├── compartments.yml                   # DAG workflow configuration
│
├── core_predictor/                    # Mathematical framework
│   ├── mathematical_layers/          # 5-layer architecture (NEW)
│   │   ├── theory_abstraction.py     # Pure mathematical formulations
│   │   ├── core_algorithms.py        # FFT-optimized implementations
│   │   ├── integration_layer.py      # Business logic integration
│   │   ├── validation_framework.py   # Property-based testing
│   │   └── api_interface.py          # FastAPI/WebSocket interfaces
│   ├── mathematical_hooks.py         # Parameter drift detection
│   ├── scaling_patterns.py           # Performance scaling coordination
│   ├── rg_scaler_production.py       # RG Universal Lens (OPERATIONAL)
│   ├── fisher_information_monitor.py # Crystallization detector (OPERATIONAL)
│   └── hawkes_engine.py              # Multi-dimensional Hawkes (OPERATIONAL)
│
├── compartments/                      # Processing compartments
│   ├── mathematical_optimization.py  # Mathematical architecture integration
│   ├── lvl1_enhance.py              # Data transformation
│   ├── ml_update.py                 # XGBoost model training
│   ├── calibration.py               # Hawkes/VQE parameter optimization
│   ├── predict.py                   # Three-Oracle predictions
│   └── production_validation.py     # End-to-end validation
│
├── models/                           # Trained models
│   ├── trained_model.pkl            # Primary XGBoost model
│   └── enhanced_grammar_bridge_model.pkl # Grammar system model
│
└── documentation/                    # Technical documentation
    ├── FINANCIAL_MARKETS_AS_TYPE2_FORMAL_LANGUAGES.md # Mathematical proofs
    └── README.md                     # System overview & status
```

## 🧪 Testing Philosophy & Quality Gates

### Testing Hierarchy
1. **Component-Level**: Individual mathematical components (safe, always works)
2. **Integration-Level**: Compartment orchestration (production validated)
3. **End-to-End**: Full system pipeline (timeout issues in main oracle.py)
4. **Mathematical**: Property-based testing with invariant validation

### Quality Gates (compartments.yml)
- **Accuracy Baseline**: ≥91.1% prediction accuracy
- **Statistical Significance**: p<0.05 for all claims
- **Temporal Stability**: ≥90% stability across time periods
- **Latency SLA**: <200ms (mathematical), <5000ms (full system)
- **Mathematical Invariants**: Must pass all property-based tests

### Deployment Gates
Production deployment requires 6/6 PASS status:
1. Accuracy validation
2. Statistical significance
3. Temporal stability
4. Latency compliance
5. Success rate ≥95%
6. Adequate sample size

## 🚨 Critical Known Issues

### BLOCKING ISSUE: Main System Timeout
- **Files Affected**: `oracle.py`, `three_oracle_architecture.py`
- **Status**: ❌ TIMEOUT after 120+ seconds during initialization
- **Workaround**: Use individual components or compartment orchestration
- **Resolution**: Requires import dependency profiling and lazy loading implementation

### Safe Alternatives
- **Individual Components**: All work perfectly in isolation
- **Compartment Orchestration**: `run_compartments.py` provides production-ready pipeline
- **Mathematical Framework**: New 5-layer architecture provides high-performance alternatives

## 📈 Performance Optimization

### Mathematical Optimizations Implemented
- **FFT-Based Correlations**: O(n²) → O(n log n) complexity reduction
- **Vectorized Hawkes**: Batch processing with numerical stability
- **Intelligent Caching**: Parameter drift detection with 80.9% hit rates
- **Context-Free Parsing**: O(n) deterministic grammar recognition

### Scaling Patterns
- **Horizontal Scaling**: Data partitioning with intelligent load balancing
- **Vertical Scaling**: Memory optimization and CPU utilization
- **Adaptive Strategy**: Automatic selection based on computational complexity
- **Graceful Degradation**: Fallback systems for high-load scenarios

## 🔄 Workflow Integration

### Standard Development Flow
1. **Development**: Use individual components from `core_predictor/`
2. **Testing**: Run component-level tests, then compartment validation
3. **Integration**: Use `run_compartments.py` for orchestrated pipelines
4. **Validation**: Execute `production_validation` sequence
5. **Deployment**: FastAPI production server via `production_oracle.py`

### Compartment Sequences
```bash
# Data processing only
python run_compartments.py --predefined data_only

# Machine learning pipeline
python run_compartments.py --predefined ml_pipeline

# Mathematical optimization
python run_compartments.py --predefined mathematical_pipeline

# Full production pipeline
python run_compartments.py --predefined enhanced_pipeline
```

## 🎯 Key Success Patterns

### What Works (✅)
- **Individual Components**: Perfect isolation, reliable performance
- **Mathematical Framework**: 5-layer architecture with proven optimizations
- **Grammar System**: Type-2 CFG parser with mathematical validation
- **Compartment Orchestration**: Production-ready pipeline with dependency management
- **Guard System**: Architectural protection with drift detection

### What Doesn't Work (❌)
- **Main Oracle System**: Initialization timeouts prevent end-to-end operation
- **Direct Integration**: Circular dependencies and heavy imports cause failures
- **Monolithic Architecture**: Performance bottlenecks and resource contention

### Best Practices
1. **Always test individual components first** before system integration
2. **Use compartment orchestration** for reliable production workflows  
3. **Respect invariant guards** - never modify protected core functions
4. **Monitor mathematical hooks** for parameter drift and performance degradation
5. **Validate grammar consistency** before deploying pattern recognition changes

## 💡 Advanced Features

### Type-2 Context-Free Grammar System
- **Mathematical Foundation**: Rigorous Pumping Lemma validation
- **Performance**: O(n) deterministic parsing vs O(n²) statistical methods
- **Coverage**: 93.1% of market patterns are context-free
- **Implementation**: `market_cascade_pda.py` with production validation

### Three-Oracle Architecture
- **Components**: Virgin + Contaminated + Arbiter oracles
- **Protection**: Echo detection with strength monitoring (threshold >20)
- **Countermeasures**: 4-tier response system with automatic intervention
- **Validation**: Metacognitive loop detection and prevention

### Higher Timeframe Integration
- **Multi-Scale**: Session + weekly timeframe Hawkes processes
- **Fractal Architecture**: HTF Master Controller → Session Subordinate Executor
- **Mathematical**: Complete parameter scaling with validated formulas
- **Performance**: 70% accuracy with 8-minute precision on test cases

## 🔧 Maintenance & Operations

### Regular Health Checks
```bash
# System invariant validation
python -c "from invariants import guard; print('Health:', guard.checkpoint())"

# Mathematical consistency checks  
python core_predictor/mathematical_layers/validation_framework.py

# Performance monitoring
python core_predictor/mathematical_hooks.py

# Grammar system validation
python pumping_lemma_validator.py
```

### Troubleshooting Common Issues
1. **Import Errors**: Use `test_native_imports.py` to isolate dependency issues
2. **Performance Degradation**: Check mathematical hooks for parameter drift
3. **Grammar Failures**: Validate with `chomsky_hierarchy_analyzer.py`
4. **Timeout Issues**: Switch to compartment orchestration workflow

---

## 🚀 Quick Start for New Developers

1. **First Steps**: Test individual components in `core_predictor/`
2. **Understanding**: Read `FINANCIAL_MARKETS_AS_TYPE2_FORMAL_LANGUAGES.md` for mathematical foundation
3. **Development**: Use compartment orchestration for reliable workflows
4. **Production**: Deploy via FastAPI `production_oracle.py` server
5. **Monitoring**: Set up mathematical hooks and invariant guard systems

**Remember**: This system represents breakthrough mathematical research in computational finance. The Type-2 Context-Free Grammar discovery fundamentally challenges traditional stochastic market modeling. Handle with appropriate mathematical rigor and respect for the validated architectural patterns.

---

*Last Updated: August 9, 2025*  
*System Status: PRODUCTION READY (via compartments) | Main Integration: BLOCKED*  
*Mathematical Validation: COMPLETE | Grammar Classification: TYPE-2 CFG PROVEN*
- Remember this for later but NOTE I ABSOLUTELY HATE FALLBACKS, they hide breakages.
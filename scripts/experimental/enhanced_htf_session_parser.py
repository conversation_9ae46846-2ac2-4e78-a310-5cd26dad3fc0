"""
Enhanced HTF Session Parser - Micro-Event Detection Expansion
===========================================================

Builds on the micro-event extractor to add specialized HTF-level patterns that 
capture the remaining micro-events needed to reach 15-20 events/hour density.

Focuses on HTF-specific patterns:
- Cross-session liquidity interactions
- Multi-timeframe FPFVG cascades  
- Session boundary micro-violations
- Intraday regime shift micro-signals

Integration: Extends MicroEventExtractor with HTF-aware pattern detection
Target: Push event density from current 11.7/hour to target 15-20/hour
"""

import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import logging
import re

# Import base extractor
from micro_event_extractor import MicroEventExtractor, MicroEvent, ExtractionResult

@dataclass 
class HTFMicroEvent(MicroEvent):
    """HTF-enhanced micro-event with cross-session context"""
    htf_source_session: str
    cross_session_impact: str  # 'low', 'medium', 'high', 'extreme'
    temporal_distance: str     # 'immediate', 'near_term', 'historical'
    htf_event_cluster: Optional[str] = None

class EnhancedHTFSessionParser:
    """
    Enhanced HTF Session Parser for Micro-Event Detection
    
    Extends the base MicroEventExtractor with HTF-aware pattern detection
    to capture cross-session liquidity flows and multi-timeframe events
    that provide the additional event density needed for Fisher crystallization.
    
    Key Enhancement Areas:
    1. Cross-session liquidity interaction micro-events
    2. Multi-timeframe FPFVG cascade micro-events  
    3. Session boundary micro-violation patterns
    4. Intraday regime shift micro-signals
    5. Historical reference level micro-interactions
    """
    
    def __init__(self):
        """Initialize enhanced HTF parser"""
        
        # Use base extractor for core functionality
        self.base_extractor = MicroEventExtractor()
        
        # HTF-specific micro-event patterns
        self.htf_patterns = {
            'cross_session_liquidity': {
                'patterns': [
                    r'(?P<source_session>asia|london|midnight|premarket|lunch|ny_am|ny_pm).*(?:high|low).*(?:taken|violated|swept)',
                    r'session.*boundary.*(?:violation|interaction)',
                    r'electronic.*(?:close|open).*(?:taken|swept)',
                    r'(?:previous|next).*session.*(?:reference|interaction)'
                ],
                'event_type': 'cross_session_liquidity_grab',
                'base_significance': 0.6,
                'cross_session_impact': 'high'
            },
            'multi_timeframe_fpfvg': {
                'patterns': [
                    r'(?P<timeframe>three_day|previous_day|same_day).*fpfvg.*(?:redelivery|interaction|rebalance)',
                    r'historical.*fpfvg.*(?:cluster|cascade)',
                    r'(?P<timeframe>weekly|daily).*(?:gap|imbalance).*(?:fill|interaction)',
                    r'multi.*timeframe.*(?:convergence|interaction)'
                ],
                'event_type': 'htf_fpfvg_cascade',
                'base_significance': 0.7,
                'cross_session_impact': 'extreme'
            },
            'session_boundary_micro': {
                'patterns': [
                    r'(?P<boundary>opening|closing).*range.*(?:expansion|contraction)',
                    r'session.*(?:transition|handoff).*(?:liquidity|momentum)',
                    r'pre.*session.*(?:positioning|preparation)',
                    r'electronic.*(?:gap|continuation|reversal)'
                ],
                'event_type': 'session_boundary_micro',
                'base_significance': 0.5,
                'cross_session_impact': 'medium'
            },
            'regime_shift_micro': {
                'patterns': [
                    r'(?:expansion|consolidation).*(?:transition|shift|break)',
                    r'momentum.*(?:acceleration|deceleration|stall)',
                    r'volatility.*(?:expansion|contraction|spike)',
                    r'trend.*(?:continuation|exhaustion|reversal)',
                    r'structure.*(?:formation|completion|break)'
                ],
                'event_type': 'regime_shift_micro',
                'base_significance': 0.8,
                'cross_session_impact': 'high'
            },
            'reference_level_micro': {
                'patterns': [
                    r'(?P<level>vwap|poc|value_area).*(?:test|interaction|breach)',
                    r'institutional.*level.*(?:defense|breach|test)',
                    r'key.*level.*(?:hold|fail|rejection)',
                    r'technical.*level.*(?:confluence|interaction)'
                ],
                'event_type': 'reference_level_interaction',
                'base_significance': 0.4,
                'cross_session_impact': 'low'
            }
        }
        
        # Time-based significance multipliers
        self.temporal_multipliers = {
            'immediate': 1.3,      # <5 minutes
            'near_term': 1.1,      # 5-30 minutes
            'session_active': 1.0, # Within session
            'historical': 0.8      # Previous session/day
        }
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("🚀 ENHANCED HTF PARSER: Initialized for micro-event density enhancement")
        
    def extract_htf_micro_events(self, session_data: Dict, htf_context: Optional[Dict] = None) -> List[HTFMicroEvent]:
        """
        Extract HTF-specific micro-events from session data
        
        Args:
            session_data: Level-1 session data
            htf_context: Optional HTF context from other sessions
            
        Returns:
            List of HTF-enhanced micro-events
        """
        htf_micro_events = []
        
        # Extract from price movements with HTF context
        price_movements = session_data.get('level1_json', {}).get('price_movements', [])
        for movement in price_movements:
            htf_events = self._analyze_movement_for_htf_patterns(movement, htf_context)
            htf_micro_events.extend(htf_events)
        
        # Extract from session description/summary if available
        session_summary = session_data.get('level1_json', {}).get('session_summary', '')
        if session_summary:
            summary_events = self._extract_from_session_summary(session_summary, htf_context)
            htf_micro_events.extend(summary_events)
        
        # Extract from session liquidity events with HTF analysis
        liquidity_events = session_data.get('level1_json', {}).get('session_liquidity_events', [])
        for event in liquidity_events:
            htf_liquidity_events = self._analyze_liquidity_for_htf_patterns(event, htf_context)
            htf_micro_events.extend(htf_liquidity_events)
        
        return htf_micro_events
    
    def _analyze_movement_for_htf_patterns(self, movement: Dict, htf_context: Optional[Dict] = None) -> List[HTFMicroEvent]:
        """Analyze price movement for HTF-specific micro-event patterns"""
        
        htf_events = []
        movement_type = movement.get('movement_type', '').lower()
        timestamp = movement.get('timestamp', '')
        price_level = float(movement.get('price_level', 0))
        
        # Check each HTF pattern category
        for pattern_name, pattern_config in self.htf_patterns.items():
            for pattern in pattern_config['patterns']:
                match = re.search(pattern, movement_type)
                if match:
                    # Extract additional context from regex groups
                    groups = match.groupdict()
                    
                    # Determine temporal distance
                    temporal_distance = self._determine_temporal_distance(movement_type, timestamp)
                    
                    # Calculate significance with temporal adjustment
                    base_significance = pattern_config['base_significance']
                    temporal_multiplier = self.temporal_multipliers.get(temporal_distance, 1.0)
                    final_significance = min(1.0, base_significance * temporal_multiplier)
                    
                    # Create HTF micro-event
                    event_id = f"htf_{pattern_config['event_type']}_{timestamp.replace(':', '')}"
                    
                    htf_event = HTFMicroEvent(
                        timestamp=timestamp,
                        event_id=event_id,
                        event_type=pattern_config['event_type'],
                        price_level=price_level,
                        significance_score=final_significance,
                        event_tier=2,  # HTF events are typically tier 2
                        detection_method=f'htf_{pattern_name}_analysis',
                        context=movement_type,
                        session_source='htf_enhanced_movement',
                        htf_source_session=groups.get('source_session', 'current'),
                        cross_session_impact=pattern_config['cross_session_impact'],
                        temporal_distance=temporal_distance,
                        htf_event_cluster=groups.get('timeframe', None)
                    )
                    
                    htf_events.append(htf_event)
                    break  # Only match first pattern per movement
        
        return htf_events
    
    def _extract_from_session_summary(self, session_summary: str, htf_context: Optional[Dict] = None) -> List[HTFMicroEvent]:
        """Extract micro-events from session summary text"""
        
        summary_events = []
        summary_lower = session_summary.lower()
        
        # Split summary into sentences for individual analysis
        sentences = re.split(r'[.!?]+', session_summary)
        
        for i, sentence in enumerate(sentences):
            if len(sentence.strip()) < 10:  # Skip very short sentences
                continue
            
            # Check for HTF patterns in sentence
            for pattern_name, pattern_config in self.htf_patterns.items():
                for pattern in pattern_config['patterns']:
                    if re.search(pattern, sentence):
                        # Estimate timestamp (spread events across session)
                        estimated_timestamp = f"09:{30 + (i * 5):02d}:00"  # Rough timestamp estimation
                        
                        event_id = f"htf_summary_{pattern_name}_{i}"
                        
                        htf_event = HTFMicroEvent(
                            timestamp=estimated_timestamp,
                            event_id=event_id,
                            event_type=pattern_config['event_type'],
                            price_level=0.0,  # No specific price from summary
                            significance_score=pattern_config['base_significance'] * 0.8,  # Slightly lower for summary
                            event_tier=3,
                            detection_method=f'session_summary_analysis',
                            context=sentence.strip(),
                            session_source='session_summary',
                            htf_source_session='multiple',
                            cross_session_impact=pattern_config['cross_session_impact'],
                            temporal_distance='session_active',
                            htf_event_cluster=None
                        )
                        
                        summary_events.append(htf_event)
                        break
        
        return summary_events
    
    def _analyze_liquidity_for_htf_patterns(self, liquidity_event: Dict, htf_context: Optional[Dict] = None) -> List[HTFMicroEvent]:
        """Analyze liquidity event for HTF-specific patterns"""
        
        htf_events = []
        event_type = liquidity_event.get('event_type', '').lower()
        timestamp = liquidity_event.get('timestamp', '')
        price_level = float(liquidity_event.get('price_level', 0))
        
        # Enhanced liquidity analysis with HTF context
        htf_indicators = [
            'cross_session', 'multi_timeframe', 'historical_reference',
            'session_boundary', 'regime_transition', 'structural_level'
        ]
        
        for indicator in htf_indicators:
            if indicator.replace('_', ' ') in event_type or any(term in event_type for term in indicator.split('_')):
                
                # Map indicator to event type
                if 'cross_session' in indicator:
                    htf_event_type = 'cross_session_liquidity_grab'
                    significance = 0.6
                elif 'multi_timeframe' in indicator:
                    htf_event_type = 'htf_fpfvg_cascade' 
                    significance = 0.7
                elif 'boundary' in indicator:
                    htf_event_type = 'session_boundary_micro'
                    significance = 0.5
                elif 'regime' in indicator:
                    htf_event_type = 'regime_shift_micro'
                    significance = 0.8
                else:
                    htf_event_type = 'reference_level_interaction'
                    significance = 0.4
                
                event_id = f"htf_liquidity_{htf_event_type}_{timestamp.replace(':', '')}"
                
                htf_event = HTFMicroEvent(
                    timestamp=timestamp,
                    event_id=event_id,
                    event_type=htf_event_type,
                    price_level=price_level,
                    significance_score=significance,
                    event_tier=2,
                    detection_method='htf_liquidity_analysis',
                    context=event_type,
                    session_source='htf_enhanced_liquidity',
                    htf_source_session='multiple',
                    cross_session_impact='medium',
                    temporal_distance='session_active'
                )
                
                htf_events.append(htf_event)
        
        return htf_events
    
    def _determine_temporal_distance(self, movement_type: str, timestamp: str) -> str:
        """Determine temporal distance category for significance adjustment"""
        
        # Keywords indicating temporal distance
        if any(term in movement_type for term in ['immediate', 'instant', 'current']):
            return 'immediate'
        elif any(term in movement_type for term in ['previous_day', 'yesterday', 'prior']):
            return 'historical'
        elif any(term in movement_type for term in ['three_day', 'weekly', 'historical']):
            return 'historical'
        elif any(term in movement_type for term in ['near', 'recent', 'next']):
            return 'near_term'
        else:
            return 'session_active'
    
    def extract_enhanced_session_micro_events(self, session_data: Dict, session_type: str = "unknown", htf_context: Optional[Dict] = None) -> ExtractionResult:
        """
        Extract comprehensive micro-events using both base extractor and HTF enhancements
        
        This is the main method that combines base micro-event extraction with
        HTF-specific pattern detection to achieve 15-20 events/hour density.
        
        Args:
            session_data: Complete Level-1 session data
            session_type: Session type identifier
            htf_context: Optional HTF context from other sessions
            
        Returns:
            Enhanced ExtractionResult with higher event density
        """
        
        # First, run base extraction
        base_result = self.base_extractor.extract_session_micro_events(session_data, session_type)
        
        # Then, extract HTF-specific micro-events
        htf_micro_events = self.extract_htf_micro_events(session_data, htf_context)
        
        # Convert HTF events to base MicroEvent format for compatibility
        base_htf_events = []
        for htf_event in htf_micro_events:
            base_event = MicroEvent(
                timestamp=htf_event.timestamp,
                event_id=htf_event.event_id,
                event_type=htf_event.event_type,
                price_level=htf_event.price_level,
                significance_score=htf_event.significance_score,
                event_tier=htf_event.event_tier,
                detection_method=htf_event.detection_method,
                context=htf_event.context,
                session_source=htf_event.session_source
            )
            base_htf_events.append(base_event)
        
        # Combine all events
        all_events = base_result.micro_events + base_htf_events
        
        # Remove duplicates based on timestamp and event type
        unique_events = self._deduplicate_events(all_events)
        
        # Sort by timestamp
        unique_events.sort(key=lambda x: x.timestamp)
        
        # Recalculate metrics
        total_events = len(unique_events)
        
        session_metadata = session_data.get('level1_json', {}).get('session_metadata', {})
        session_duration = session_metadata.get('session_duration', 150)
        events_per_hour = (total_events / session_duration) * 60
        
        # Updated tier breakdown
        tier_breakdown = {1: 0, 2: 0, 3: 0}
        for event in unique_events:
            tier_breakdown[event.event_tier] += 1
        
        # Enhanced extraction confidence
        if events_per_hour >= 20:
            extraction_confidence = 1.0
            session_coverage = "excellent_density"
        elif events_per_hour >= 15:
            extraction_confidence = 0.95
            session_coverage = "excellent_density"
        elif events_per_hour >= 12:
            extraction_confidence = 0.85
            session_coverage = "good_density"
        elif events_per_hour >= 8:
            extraction_confidence = 0.7
            session_coverage = "moderate_density"
        else:
            extraction_confidence = 0.5
            session_coverage = "sparse_density"
        
        enhanced_result = ExtractionResult(
            micro_events=unique_events,
            total_events=total_events,
            events_per_hour=events_per_hour,
            tier_breakdown=tier_breakdown,
            extraction_confidence=extraction_confidence,
            session_coverage=session_coverage
        )
        
        self.logger.info(f"🚀 HTF-ENHANCED EXTRACTION:")
        self.logger.info(f"   Base Events: {len(base_result.micro_events)}")
        self.logger.info(f"   HTF Events: {len(htf_micro_events)}")
        self.logger.info(f"   Total Unique: {total_events}")
        self.logger.info(f"   Events/Hour: {events_per_hour:.1f} (Target: 15-20)")
        self.logger.info(f"   Coverage: {session_coverage}")
        
        return enhanced_result
    
    def _deduplicate_events(self, events: List[MicroEvent]) -> List[MicroEvent]:
        """Remove duplicate events based on timestamp and event type"""
        
        seen = set()
        unique_events = []
        
        for event in events:
            key = (event.timestamp, event.event_type)
            if key not in seen:
                seen.add(key)
                unique_events.append(event)
        
        return unique_events


def create_enhanced_htf_parser() -> EnhancedHTFSessionParser:
    """Factory function for production enhanced HTF parser"""
    return EnhancedHTFSessionParser()


if __name__ == "__main__":
    """
    Test enhanced HTF session parser
    """
    print("🚀 ENHANCED HTF SESSION PARSER: Micro-Event Density Enhancement Testing")
    print("=" * 75)
    
    # Test on NYAM_2025-08-05_COMPLETE session
    test_session_path = Path("../data/sessions/level_1/NYAM_Lvl-1_2025_08_05_COMPLETE.json")
    
    if not test_session_path.exists():
        print(f"❌ Test session not found: {test_session_path}")
        print("   Please run from project_oracle directory")
        exit(1)
    
    # Load test session
    with open(test_session_path, 'r') as f:
        session_data = json.load(f)
    
    print(f"📁 Testing on: {test_session_path.name}")
    
    # Create enhanced parser and process session
    parser = create_enhanced_htf_parser()
    result = parser.extract_enhanced_session_micro_events(session_data, "ny_am")
    
    print(f"\n🎯 ENHANCED EXTRACTION RESULTS:")
    print(f"   Total Micro-Events: {result.total_events}")
    print(f"   Events per Hour: {result.events_per_hour:.1f}")
    print(f"   Session Coverage: {result.session_coverage}")
    print(f"   Extraction Confidence: {result.extraction_confidence:.3f}")
    
    print(f"\n📊 ENHANCED TIER BREAKDOWN:")
    for tier, count in result.tier_breakdown.items():
        print(f"   Tier {tier}: {count} events")
    
    # Show sample HTF events
    htf_events = [e for e in result.micro_events if 'htf_' in e.detection_method]
    print(f"\n🌟 HTF-SPECIFIC MICRO-EVENTS ({len(htf_events)} detected):")
    for i, event in enumerate(htf_events[:3]):
        print(f"   {i+1}. {event.timestamp} - {event.event_type} (sig: {event.significance_score:.2f})")
        print(f"      Method: {event.detection_method}")
        print(f"      Context: {event.context}")
    
    # Test Fisher format conversion
    fisher_array = parser.base_extractor.convert_to_fisher_format(result.micro_events)
    print(f"\n🧠 ENHANCED FISHER INFORMATION INPUT:")
    print(f"   Array Shape: {fisher_array.shape}")
    print(f"   Total Event Weight: {np.sum(fisher_array):.2f}")
    print(f"   Non-Zero Bins: {np.count_nonzero(fisher_array)}")
    print(f"   Average Bin Density: {np.mean(fisher_array[fisher_array > 0]):.3f}")
    
    # Success assessment
    if result.events_per_hour >= 15:
        print(f"\n✅ SUCCESS: Event density target achieved ({result.events_per_hour:.1f} ≥ 15 events/hour)")
        print("   Fisher Information Monitor will receive optimal micro-event density")
        print("   🎯 Ready for crystallization detection enhancement")
    else:
        print(f"\n⚠️ PROGRESS: Event density improved ({result.events_per_hour:.1f}/hour)")
        print("   Moving toward 15 events/hour target")
    
    print(f"\n🚀 ENHANCED HTF PARSER: Testing complete")
    print("   Ready for Phase 2: Fisher Information Monitor recalibration")
#!/usr/bin/env python3
"""
Class Balance Revolution - SMOTE Implementation & Balanced Retraining
===================================================================

Addresses the root cause of the 17.6% training-validation gap: 88.9% class imbalance
leading to memorization rather than genuine pattern learning.

Mathematical Framework:
- Transform P(cascade) = 0.889 → P(cascade) = 0.5 via SMOTE
- Force genuine pattern discrimination instead of majority voting
- Target: 79% balanced accuracy with σ² < 5% variance

Strategy:
1. Borderline-SMOTE for high-quality synthetic non-cascade examples
2. Conservative hyperparameters to prevent re-memorization
3. Stratified validation to confirm balanced learning
4. Calibration validation to ensure genuine probability estimates
"""

import json
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
import pickle
from collections import Counter
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.metrics import precision_score, recall_score, f1_score, balanced_accuracy_score
from sklearn.preprocessing import StandardScaler
from sklearn.calibration import calibration_curve
import matplotlib.pyplot as plt

# SMOTE imports (using imblearn)
try:
    from imblearn.over_sampling import SMOTE, BorderlineSMOTE, ADASYN
    from imblearn.pipeline import Pipeline as ImbPipeline
    SMOTE_AVAILABLE = True
except ImportError:
    print("⚠️ imblearn not available, implementing manual balancing")
    SMOTE_AVAILABLE = False

# Import our components
from xgboost_real_trainer import XGBoostRealTrainer, TrainingExample
from calibration_analysis import CalibrationAnalyzer, CalibrationMetrics

@dataclass
class BalancedTrainingMetrics:
    """Comprehensive metrics for balanced model"""
    balanced_accuracy: float
    regular_accuracy: float
    precision: float
    recall: float
    f1_score: float
    
    # Stratified performance
    rare_pattern_accuracy: float
    medium_pattern_accuracy: float
    common_pattern_accuracy: float
    memorization_gap: float
    
    # Calibration metrics
    brier_score: float
    reliability: float
    calibration_slope: float
    
    # Cross-validation stability
    cv_mean: float
    cv_std: float
    
    # Class balance validation
    training_cascade_rate: float
    validation_cascade_rate: float

class ClassBalanceRevolution:
    """
    Implements the complete Class Balance Revolution strategy
    
    Core Philosophy: Address root cause (class imbalance) before symptoms (regularization)
    Expected: Drop to ~79% accuracy but with genuine learning and <5% variance
    """
    
    def __init__(self):
        # Initialize components
        self.trainer = XGBoostRealTrainer()
        self.analyzer = CalibrationAnalyzer()
        
        # Model components
        self.balanced_model = None
        self.balanced_scaler = StandardScaler()
        self.synthetic_data = None
        
        # Metrics tracking
        self.original_metrics = None
        self.balanced_metrics = None
        
        print("🌊 CLASS BALANCE REVOLUTION")
        print("=" * 35)
        print("Strategy: SMOTE + Conservative hyperparameters")
        print("Goal: Transform 88.9% → 50% cascade rate")
        print("Expected: ~79% balanced accuracy with genuine learning")
        print("Mathematical: Maximize I(features; outcome), minimize H(model)")
        print()
        
    def load_original_data(self) -> Tuple[List[TrainingExample], np.ndarray, np.ndarray]:
        """Load the original imbalanced training data"""
        
        print("📁 Loading original imbalanced training data...")
        
        sessions = self.trainer.load_enhanced_sessions()
        examples = self.trainer.extract_training_examples(sessions)
        X, y = self.trainer.prepare_training_data(examples)
        
        print(f"✅ Original data loaded:")
        print(f"   Total examples: {len(examples)}")
        print(f"   Cascade rate: {np.mean(y):.1%}")
        print(f"   Class imbalance ratio: {np.sum(y==1)}/{np.sum(y==0)} (cascade/non-cascade)")
        
        return examples, X, y
    
    def apply_smote_balancing(self, X: np.ndarray, y: np.ndarray, 
                             method: str = "borderline") -> Tuple[np.ndarray, np.ndarray]:
        """Apply SMOTE or manual balancing to achieve 50:50 class ratio"""
        
        print(f"⚖️ Applying {method} balancing...")
        
        if not SMOTE_AVAILABLE:
            return self._manual_balancing(X, y)
        
        # Choose SMOTE variant
        if method == "borderline":
            sampler = BorderlineSMOTE(
                sampling_strategy='minority',  # Only oversample minority class
                k_neighbors=min(5, np.sum(y==0)-1),  # Adjust for small minority class
                random_state=42
            )
        elif method == "adasyn":
            sampler = ADASYN(
                sampling_strategy='minority',
                n_neighbors=min(5, np.sum(y==0)-1),
                random_state=42
            )
        else:  # regular SMOTE
            sampler = SMOTE(
                sampling_strategy='minority',
                k_neighbors=min(5, np.sum(y==0)-1),
                random_state=42
            )
        
        try:
            X_balanced, y_balanced = sampler.fit_resample(X, y)
            
            print(f"✅ {method.title()} balancing successful:")
            print(f"   Original: {len(X)} samples ({np.mean(y):.1%} cascade)")
            print(f"   Balanced: {len(X_balanced)} samples ({np.mean(y_balanced):.1%} cascade)")
            print(f"   Synthetic samples generated: {len(X_balanced) - len(X)}")
            
            return X_balanced, y_balanced
            
        except Exception as e:
            print(f"❌ SMOTE failed: {e}")
            print("🔄 Falling back to manual balancing...")
            return self._manual_balancing(X, y)
    
    def _manual_balancing(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Manual balancing via downsampling majority class"""
        
        print("📊 Applying manual downsampling balancing...")
        
        # Separate classes
        cascade_indices = np.where(y == 1)[0]
        non_cascade_indices = np.where(y == 0)[0]
        
        # Determine target size (minimum of both classes)
        minority_size = len(non_cascade_indices)
        majority_size = len(cascade_indices)
        target_size = minority_size
        
        print(f"   Cascade examples: {majority_size}")
        print(f"   Non-cascade examples: {minority_size}")
        print(f"   Downsampling to: {target_size} each")
        
        # Randomly sample from majority class
        np.random.seed(42)
        sampled_cascade_indices = np.random.choice(
            cascade_indices, size=target_size, replace=False
        )
        
        # Combine balanced indices
        balanced_indices = np.concatenate([sampled_cascade_indices, non_cascade_indices])
        np.random.shuffle(balanced_indices)
        
        X_balanced = X[balanced_indices]
        y_balanced = y[balanced_indices]
        
        print(f"✅ Manual balancing successful:")
        print(f"   Balanced: {len(X_balanced)} samples ({np.mean(y_balanced):.1%} cascade)")
        
        return X_balanced, y_balanced
    
    def train_balanced_model(self, X_balanced: np.ndarray, y_balanced: np.ndarray) -> GradientBoostingClassifier:
        """Train XGBoost with conservative hyperparameters on balanced data"""
        
        print("🎯 Training balanced model with conservative hyperparameters...")
        
        # Scale features
        X_scaled = self.balanced_scaler.fit_transform(X_balanced)
        
        # Conservative hyperparameters to prevent re-memorization
        self.balanced_model = GradientBoostingClassifier(
            n_estimators=100,        # Reduced from 150
            max_depth=3,            # Reduced from 6 (key change)
            learning_rate=0.05,     # Reduced from 0.1
            subsample=0.8,          # Add stochasticity
            max_features=0.8,       # Add stochasticity  
            min_samples_split=10,   # Prevent overfitting small patterns
            min_samples_leaf=5,     # Prevent overfitting small patterns
            random_state=42,
            validation_fraction=0.2,  # Early stopping
            n_iter_no_change=10       # Early stopping
        )
        
        print(f"   Training on {len(X_balanced)} balanced samples...")
        print(f"   Conservative hyperparameters to prevent re-memorization")
        
        # Train model
        self.balanced_model.fit(X_scaled, y_balanced)
        
        # Basic training metrics
        train_accuracy = self.balanced_model.score(X_scaled, y_balanced)
        print(f"   Training accuracy: {train_accuracy:.1%}")
        
        if train_accuracy > 0.95:
            print("⚠️ Warning: High training accuracy may indicate re-memorization")
        
        return self.balanced_model
    
    def comprehensive_balanced_evaluation(self, examples: List[TrainingExample], 
                                        X_original: np.ndarray, y_original: np.ndarray,
                                        X_balanced: np.ndarray, y_balanced: np.ndarray) -> BalancedTrainingMetrics:
        """Comprehensive evaluation of balanced model vs original data"""
        
        print("📊 Comprehensive balanced model evaluation...")
        
        if self.balanced_model is None:
            raise ValueError("No balanced model to evaluate")
        
        # Use original test data for evaluation (not synthetic)
        X_test_scaled = self.balanced_scaler.transform(X_original)
        y_pred = self.balanced_model.predict(X_test_scaled)
        y_proba = self.balanced_model.predict_proba(X_test_scaled)[:, 1]
        
        # Basic metrics
        regular_accuracy = accuracy_score(y_original, y_pred)
        balanced_accuracy = balanced_accuracy_score(y_original, y_pred)
        precision = precision_score(y_original, y_pred, zero_division=0)
        recall = recall_score(y_original, y_pred, zero_division=0)
        f1 = f1_score(y_original, y_pred, zero_division=0)
        
        print(f"✅ Basic metrics on original data:")
        print(f"   Regular accuracy: {regular_accuracy:.1%}")
        print(f"   Balanced accuracy: {balanced_accuracy:.1%}")
        print(f"   Precision: {precision:.3f}")
        print(f"   Recall: {recall:.3f}")
        print(f"   F1 Score: {f1:.3f}")
        
        # Stratified analysis (reuse pattern frequency strata)
        pattern_strata = self.analyzer.analyze_pattern_frequency_distribution(examples)
        rare_acc, medium_acc, common_acc, memo_gap = self._stratified_evaluation(
            X_original, y_original, examples, pattern_strata
        )
        
        print(f"   Rare pattern accuracy: {rare_acc:.1%}")
        print(f"   Medium pattern accuracy: {medium_acc:.1%}")
        print(f"   Common pattern accuracy: {common_acc:.1%}")
        print(f"   Memorization gap: {memo_gap:+.1%}")
        
        # Calibration analysis
        brier, reliability, cal_slope = self._calibration_evaluation(y_original, y_proba)
        print(f"   Brier score: {brier:.4f}")
        print(f"   Reliability (ECE): {reliability:.4f}")
        print(f"   Calibration slope: {cal_slope:.3f}")
        
        # Cross-validation stability
        cv_scores = cross_val_score(
            self.balanced_model, X_test_scaled, y_original, 
            cv=min(5, len(X_original)//10), scoring='balanced_accuracy'
        )
        cv_mean = np.mean(cv_scores)
        cv_std = np.std(cv_scores)
        
        print(f"   CV balanced accuracy: {cv_mean:.1%} ± {cv_std:.1%}")
        
        # Class balance validation
        train_cascade_rate = np.mean(y_balanced)
        val_cascade_rate = np.mean(y_original)
        
        print(f"   Training cascade rate: {train_cascade_rate:.1%}")
        print(f"   Validation cascade rate: {val_cascade_rate:.1%}")
        
        # Create comprehensive metrics
        metrics = BalancedTrainingMetrics(
            balanced_accuracy=balanced_accuracy,
            regular_accuracy=regular_accuracy,
            precision=precision,
            recall=recall,
            f1_score=f1,
            rare_pattern_accuracy=rare_acc,
            medium_pattern_accuracy=medium_acc,
            common_pattern_accuracy=common_acc,
            memorization_gap=memo_gap,
            brier_score=brier,
            reliability=reliability,
            calibration_slope=cal_slope,
            cv_mean=cv_mean,
            cv_std=cv_std,
            training_cascade_rate=train_cascade_rate,
            validation_cascade_rate=val_cascade_rate
        )
        
        return metrics
    
    def _stratified_evaluation(self, X: np.ndarray, y: np.ndarray, 
                              examples: List[TrainingExample],
                              pattern_strata: Dict[str, List[str]]) -> Tuple[float, float, float, float]:
        """Evaluate performance across pattern frequency strata"""
        
        X_scaled = self.balanced_scaler.transform(X)
        y_pred = self.balanced_model.predict(X_scaled)
        
        strata_accuracies = {}
        
        for stratum_name, pattern_list in pattern_strata.items():
            if not pattern_list:
                strata_accuracies[stratum_name] = 0.0
                continue
                
            # Find indices for this stratum
            stratum_indices = []
            for i, example in enumerate(examples):
                pattern_name = None
                for name, encoded in self.trainer.enhancer.pattern_encoder.items():
                    if encoded == example.context_features.pattern_id_encoded:
                        pattern_name = name
                        break
                        
                if pattern_name in pattern_list:
                    stratum_indices.append(i)
            
            if stratum_indices:
                stratum_y_true = y[stratum_indices]
                stratum_y_pred = y_pred[stratum_indices]
                accuracy = np.mean(stratum_y_true == stratum_y_pred)
                strata_accuracies[stratum_name] = accuracy
            else:
                strata_accuracies[stratum_name] = 0.0
        
        rare_acc = strata_accuracies.get('rare', 0.0)
        medium_acc = strata_accuracies.get('medium', 0.0)
        common_acc = strata_accuracies.get('common', 0.0)
        memo_gap = common_acc - rare_acc
        
        return rare_acc, medium_acc, common_acc, memo_gap
    
    def _calibration_evaluation(self, y_true: np.ndarray, y_proba: np.ndarray) -> Tuple[float, float, float]:
        """Evaluate model calibration"""
        
        from sklearn.metrics import brier_score_loss
        from scipy import stats
        
        # Brier score
        brier = brier_score_loss(y_true, y_proba)
        
        # Reliability (Expected Calibration Error)
        try:
            fraction_of_positives, mean_predicted_value = calibration_curve(
                y_true, y_proba, n_bins=min(10, len(y_true)//5)
            )
            reliability = np.mean(np.abs(fraction_of_positives - mean_predicted_value))
            
            # Calibration slope
            if len(mean_predicted_value) > 1:
                slope, intercept, r_value, p_value, std_err = stats.linregress(
                    mean_predicted_value, fraction_of_positives
                )
            else:
                slope = 1.0
        except:
            reliability = 999.0  # Error indicator
            slope = 0.0
            
        return brier, reliability, slope
    
    def generate_before_after_comparison(self, original_results: dict, 
                                       balanced_metrics: BalancedTrainingMetrics):
        """Generate comprehensive before/after comparison"""
        
        print(f"\n🔄 BEFORE vs AFTER COMPARISON")
        print("=" * 35)
        
        print("📊 ACCURACY COMPARISON:")
        if 'training_metrics' in original_results:
            orig_val_acc = original_results['training_metrics']['validation_accuracy']
            print(f"   Original validation: {orig_val_acc:.1%}")
        print(f"   Balanced validation: {balanced_metrics.balanced_accuracy:.1%}")
        print(f"   Balanced regular: {balanced_metrics.regular_accuracy:.1%}")
        
        print("🎯 MEMORIZATION GAP COMPARISON:")
        if 'stratified_results' in original_results:
            orig_gap = original_results['stratified_results'].memorization_gap
            print(f"   Original gap: {orig_gap:+.1%}")
        print(f"   Balanced gap: {balanced_metrics.memorization_gap:+.1%}")
        
        print("📈 CALIBRATION COMPARISON:")
        if 'calibration_metrics' in original_results:
            orig_reliability = original_results['calibration_metrics'].reliability
            orig_slope = original_results['calibration_metrics'].calibration_slope
            print(f"   Original reliability: {orig_reliability:.4f}")
            print(f"   Original slope: {orig_slope:.3f}")
        print(f"   Balanced reliability: {balanced_metrics.reliability:.4f}")
        print(f"   Balanced slope: {balanced_metrics.calibration_slope:.3f}")
        
        print("🔄 STABILITY COMPARISON:")
        print(f"   Balanced CV std: ±{balanced_metrics.cv_std:.1%}")
        
        # Success criteria evaluation
        print(f"\n🏆 CLASS BALANCE REVOLUTION SUCCESS CRITERIA:")
        
        success_criteria = []
        
        # 1. Reduced memorization gap
        if abs(balanced_metrics.memorization_gap) < 0.15:
            success_criteria.append("✅ Memorization gap <15%")
        else:
            success_criteria.append("❌ Memorization gap still high")
            
        # 2. Improved calibration
        if balanced_metrics.reliability < 0.2:
            success_criteria.append("✅ Acceptable calibration reliability")
        else:
            success_criteria.append("❌ Poor calibration reliability")
            
        # 3. Stable performance
        if balanced_metrics.cv_std < 0.05:
            success_criteria.append("✅ Low cross-validation variance")
        else:
            success_criteria.append("❌ High cross-validation variance")
            
        # 4. Genuine learning (not just majority voting)
        if balanced_metrics.precision > 0.6 and balanced_metrics.recall > 0.6:
            success_criteria.append("✅ Balanced precision and recall")
        else:
            success_criteria.append("❌ Imbalanced precision/recall")
            
        for criterion in success_criteria:
            print(f"   {criterion}")
            
        # Overall success assessment
        success_count = sum(1 for c in success_criteria if c.startswith("✅"))
        if success_count >= 3:
            print(f"\n🎉 CLASS BALANCE REVOLUTION SUCCESSFUL ({success_count}/4 criteria)")
            recommendation = "Deploy balanced model to production"
        else:
            print(f"\n⚠️ CLASS BALANCE REVOLUTION PARTIAL ({success_count}/4 criteria)")
            recommendation = "Further tuning required"
            
        print(f"   Recommendation: {recommendation}")
        
        return success_count >= 3
    
    def save_balanced_model(self, filename: str = "balanced_xgboost_model.pkl"):
        """Save the balanced model for production use"""
        
        if self.balanced_model is None:
            print("❌ No balanced model to save")
            return False
            
        model_data = {
            'model': self.balanced_model,
            'scaler': self.balanced_scaler,
            'pattern_encoder': self.trainer.enhancer.pattern_encoder,
            'balanced_metrics': self.balanced_metrics,
            'training_date': pd.Timestamp.now().isoformat(),
            'model_type': 'balanced_xgboost',
            'balancing_method': 'SMOTE_borderline',
            'hyperparameters': {
                'n_estimators': 100,
                'max_depth': 3,
                'learning_rate': 0.05,
                'subsample': 0.8,
                'max_features': 0.8
            }
        }
        
        with open(filename, 'wb') as f:
            pickle.dump(model_data, f)
            
        print(f"✅ Balanced model saved: {filename}")
        return True
    
    def execute_complete_revolution(self) -> Dict[str, Any]:
        """Execute the complete Class Balance Revolution workflow"""
        
        print("🌊 EXECUTING COMPLETE CLASS BALANCE REVOLUTION")
        print("=" * 50)
        
        # Step 1: Load original imbalanced data
        examples, X_original, y_original = self.load_original_data()
        
        # Get original results for comparison
        try:
            original_analysis = self.analyzer.comprehensive_analysis()
            self.original_metrics = original_analysis
        except:
            print("⚠️ Could not load original analysis for comparison")
            self.original_metrics = {}
        
        # Step 2: Apply SMOTE balancing
        X_balanced, y_balanced = self.apply_smote_balancing(X_original, y_original)
        
        # Step 3: Train balanced model
        balanced_model = self.train_balanced_model(X_balanced, y_balanced)
        
        # Step 4: Comprehensive evaluation
        self.balanced_metrics = self.comprehensive_balanced_evaluation(
            examples, X_original, y_original, X_balanced, y_balanced
        )
        
        # Step 5: Before/after comparison
        revolution_success = self.generate_before_after_comparison(
            self.original_metrics, self.balanced_metrics
        )
        
        # Step 6: Save balanced model
        if revolution_success:
            self.save_balanced_model()
        
        # Final results
        results = {
            'revolution_success': revolution_success,
            'balanced_metrics': self.balanced_metrics,
            'original_metrics': self.original_metrics,
            'original_cascade_rate': np.mean(y_original),
            'balanced_cascade_rate': np.mean(y_balanced),
            'model_saved': revolution_success
        }
        
        print(f"\n🎯 CLASS BALANCE REVOLUTION COMPLETE")
        print(f"Success: {'YES' if revolution_success else 'PARTIAL'}")
        print(f"Balanced Accuracy: {self.balanced_metrics.balanced_accuracy:.1%}")
        print(f"Memorization Gap: {self.balanced_metrics.memorization_gap:+.1%}")
        print(f"CV Stability: ±{self.balanced_metrics.cv_std:.1%}")
        
        return results

def main():
    """Execute the Class Balance Revolution"""
    
    revolution = ClassBalanceRevolution()
    results = revolution.execute_complete_revolution()
    
    return results

if __name__ == "__main__":
    revolution_results = main()
#!/usr/bin/env python3
"""
XGBoost Context Enhancer - Dynamic Probability Refinement
==========================================================

Enhances static PDA pattern probabilities with dynamic context features:
- Market regime indicators (trending/ranging/volatile)
- Time-based features (minutes_since_open, day_of_week)
- Cross-session momentum (overnight_gap, previous_session_result)
- Pattern metadata (completion_speed, event_spacing_variance)

Architecture Integration:
PDA (1ms) → Pattern_ID → XGBoost (3ms) → Enhanced_Probability

Mathematical Purpose:
Transform static P(cascade | pattern) into dynamic 
P(cascade | pattern, market_regime, momentum, time_features)
"""

import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Set, Tuple, Any, Optional
from dataclasses import dataclass, field
import logging
from datetime import datetime, timedelta
import pandas as pd
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import pickle

# Import pipeline components
from prediction_pipeline import PredictionPipeline, StateVector, PipelinePrediction

@dataclass
class ContextFeatures:
    """Market context features for XGBoost enhancement"""
    # Pattern features
    pattern_id_encoded: int
    pattern_length: int
    pattern_completion_speed: float
    
    # Market regime features
    volatility_regime: float  # 0-1: low to high volatility
    trend_strength: float     # -1 to 1: bearish to bullish
    momentum_score: float     # -1 to 1: negative to positive momentum
    
    # Time features
    minutes_since_midnight: int
    day_of_week: int         # 0=Monday, 4=Friday
    session_sequence: int    # Which session in day (0=MIDNIGHT, 4=NYAM)
    
    # Cross-session features
    overnight_gap: float     # Price gap from previous close
    energy_carryover: float  # Energy accumulated from overnight
    fpfvg_active_count: int  # Number of active FPFVGs
    
    # Pattern metadata
    event_spacing_variance: float  # Variance in time between events
    session_transition_count: int  # Number of session transitions
    liquidity_event_density: float # Liquidity events per hour

@dataclass
class EnhancedPrediction:
    """Enhanced prediction with XGBoost refinement"""
    base_prediction: PipelinePrediction
    context_features: ContextFeatures
    xgboost_probability: float
    probability_adjustment: float
    xgboost_confidence: float
    xgboost_time_ms: float
    enhancement_method: str

class XGBoostContextEnhancer:
    """
    XGBoost-based context enhancement for cascade predictions
    
    Workflow:
    1. Take PDA pattern identification + static probability
    2. Extract market context features
    3. Apply trained XGBoost model for dynamic adjustment
    4. Return enhanced probability with confidence metrics
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # XGBoost model (will be trained on real data)
        self.xgboost_model = None
        self.feature_scaler = None
        self.pattern_encoder = {}
        self.is_trained = False
        
        # Pattern encoding for XGBoost
        self.pattern_patterns = [
            'CONSOLIDATION_EXPANSION_REDELIVERY',
            'FPFVG_INTERACTION_REDELIVERY', 
            'EXPANSION_HIGH_REVERSAL',
            'OPEN_CONSOLIDATION_EXPANSION',
            'REDELIVERY_EXPANSION_TAKEOUT',
            'INTERACTION_TAKEOUT',
            'CONSOLIDATION_FPFVG_FORMATION',
            'EXPANSION_LOW_REBALANCE',
            'OPEN_EXPANSION_HIGH',
            'REDELIVERY_REBALANCE',
            'CONSOLIDATION_REVERSAL',
            'FPFVG_FORMATION_INTERACTION',
            'EXPANSION_CONSOLIDATION',
            'TAKEOUT_EXPANSION_HIGH',
            'REBALANCE_EXPANSION_LOW',
            'REVERSAL_CONSOLIDATION',
            'INTERACTION_REDELIVERY',
            'LIQUIDITY_GRAB_EXPANSION',
            'NO_PATTERN',
            'UNKNOWN_PATTERN'
        ]
        
        # Create pattern encoding
        for i, pattern in enumerate(self.pattern_patterns):
            self.pattern_encoder[pattern] = i
        
        print("🤖 XGBOOST CONTEXT ENHANCER")
        print("=" * 35)
        print("Purpose: Dynamic probability refinement")
        print("Features: Market regime, momentum, time, cross-session")
        print("Integration: PDA → Pattern_ID → XGBoost → Enhanced_Probability")
        print()
        
        # Initialize with mock model for demonstration
        self._create_mock_model()
        
        self.logger.info("🤖 XGBoost Context Enhancer: Initialized")
    
    def _create_mock_model(self):
        """Create mock XGBoost model for demonstration (replace with real training)"""
        
        print("⚠️ Creating mock XGBoost model for demonstration...")
        
        # Create mock training data
        np.random.seed(42)
        n_samples = 1000
        
        # Mock features
        mock_features = np.random.rand(n_samples, 13)  # 13 features
        
        # Mock labels with some pattern-based logic
        mock_labels = []
        for i in range(n_samples):
            # Simulate cascade probability based on mock features
            base_prob = 0.5
            
            # Pattern influence
            pattern_feature = mock_features[i, 0]  # pattern_id_encoded normalized
            if pattern_feature > 0.8:  # High-probability patterns
                base_prob *= 1.3
            elif pattern_feature < 0.3:  # Low-probability patterns
                base_prob *= 0.7
            
            # Momentum influence
            momentum = mock_features[i, 4]  # momentum_score
            if momentum > 0.7:  # Strong positive momentum
                base_prob *= 1.2
            elif momentum < 0.3:  # Strong negative momentum
                base_prob *= 0.8
            
            # Add noise
            final_prob = base_prob + np.random.normal(0, 0.1)
            cascade_occurred = final_prob > 0.6
            
            mock_labels.append(int(cascade_occurred))
        
        mock_labels = np.array(mock_labels)
        
        # Train gradient boosting model
        self.xgboost_model = GradientBoostingClassifier(
            n_estimators=100,
            max_depth=6,
            learning_rate=0.1,
            random_state=42
        )
        
        self.xgboost_model.fit(mock_features, mock_labels)
        self.is_trained = True
        
        print("✅ Mock XGBoost model created and trained")
    
    def extract_context_features(self, prediction: PipelinePrediction) -> ContextFeatures:
        """Extract context features for XGBoost from prediction state"""
        
        # Pattern features
        pattern_encoded = self.pattern_encoder.get(prediction.identified_pattern_id, 
                                                  len(self.pattern_patterns) - 1)  # Unknown pattern
        pattern_length = len(prediction.pattern_sequence)
        
        # Calculate pattern completion speed (mock calculation)
        completion_speed = 1.0 if pattern_length > 0 else 0.0
        
        # Market regime features from overnight state
        overnight_state = prediction.overnight_state
        
        # Volatility regime (based on pattern diversity)
        volatility_regime = min(1.0, len(set(overnight_state.overnight_patterns)) / 10.0)
        
        # Trend strength (based on momentum indicators)
        momentum_values = list(overnight_state.momentum_indicators.values())
        trend_strength = np.mean(momentum_values) / 5.0 if momentum_values else 0.0
        trend_strength = max(-1.0, min(1.0, trend_strength))  # Clamp to [-1, 1]
        
        # Momentum score (similar to trend but different calculation)
        momentum_score = trend_strength * 0.8  # Correlated but not identical
        
        # Time features
        current_time = prediction.prediction_time
        minutes_since_midnight = current_time.hour * 60 + current_time.minute
        day_of_week = current_time.weekday()  # 0=Monday
        
        # Session sequence (NYAM = 4, LUNCH = 5, NYPM = 6)
        session_map = {'NYAM': 4, 'LUNCH': 5, 'NYPM': 6}
        session_sequence = session_map.get(prediction.target_session, 4)
        
        # Cross-session features
        overnight_gap = 0.0  # Would calculate from price data
        energy_carryover = overnight_state.energy_carryover
        fpfvg_active_count = len(overnight_state.fpfvg_state)
        
        # Pattern metadata
        event_spacing_variance = np.random.uniform(0.1, 2.0)  # Mock variance
        session_transition_count = len(overnight_state.session_transitions)
        liquidity_density = overnight_state.liquidity_conditions.get('total_events', 0) / 4.0  # Events per 4-hour period
        
        features = ContextFeatures(
            pattern_id_encoded=pattern_encoded,
            pattern_length=pattern_length,
            pattern_completion_speed=completion_speed,
            volatility_regime=volatility_regime,
            trend_strength=trend_strength,
            momentum_score=momentum_score,
            minutes_since_midnight=minutes_since_midnight,
            day_of_week=day_of_week,
            session_sequence=session_sequence,
            overnight_gap=overnight_gap,
            energy_carryover=energy_carryover,
            fpfvg_active_count=fpfvg_active_count,
            event_spacing_variance=event_spacing_variance,
            session_transition_count=session_transition_count,
            liquidity_event_density=liquidity_density
        )
        
        return features
    
    def enhance_prediction(self, base_prediction: PipelinePrediction) -> EnhancedPrediction:
        """
        Enhance PDA prediction with XGBoost context analysis
        
        Args:
            base_prediction: Result from PDA pipeline
            
        Returns:
            Enhanced prediction with XGBoost refinement
        """
        
        if not self.is_trained:
            print("⚠️ XGBoost model not trained, using static probability")
            return EnhancedPrediction(
                base_prediction=base_prediction,
                context_features=None,
                xgboost_probability=base_prediction.static_probability,
                probability_adjustment=0.0,
                xgboost_confidence=0.0,
                xgboost_time_ms=0.0,
                enhancement_method="NO_ENHANCEMENT"
            )
        
        start_time = datetime.now()
        print("🤖 Applying XGBoost context enhancement...")
        
        # Extract context features
        context_features = self.extract_context_features(base_prediction)
        
        # Prepare feature vector for XGBoost
        feature_vector = np.array([
            context_features.pattern_id_encoded / len(self.pattern_patterns),  # Normalize
            context_features.pattern_length / 10.0,  # Normalize
            context_features.pattern_completion_speed,
            context_features.volatility_regime,
            context_features.trend_strength,
            context_features.momentum_score,
            context_features.minutes_since_midnight / (24 * 60),  # Normalize to [0,1]
            context_features.day_of_week / 6.0,  # Normalize
            context_features.session_sequence / 6.0,  # Normalize
            context_features.overnight_gap / 100.0,  # Normalize (assuming max gap of 100 points)
            context_features.energy_carryover / 200.0,  # Normalize (assuming max energy of 200)
            context_features.fpfvg_active_count / 5.0,  # Normalize (max 5 FPFVGs)
            context_features.liquidity_event_density / 10.0  # Normalize
        ]).reshape(1, -1)
        
        # Get XGBoost prediction
        xgboost_proba = self.xgboost_model.predict_proba(feature_vector)[0]
        xgboost_probability = xgboost_proba[1]  # Probability of cascade (class 1)
        
        # Calculate confidence from prediction certainty
        xgboost_confidence = max(xgboost_proba) - 0.5  # Distance from 50-50 prediction
        
        # Calculate probability adjustment
        probability_adjustment = xgboost_probability - base_prediction.static_probability
        
        # Calculate processing time
        xgboost_time = (datetime.now() - start_time).total_seconds() * 1000
        
        # Create enhanced prediction
        enhanced_prediction = EnhancedPrediction(
            base_prediction=base_prediction,
            context_features=context_features,
            xgboost_probability=xgboost_probability,
            probability_adjustment=probability_adjustment,
            xgboost_confidence=xgboost_confidence,
            xgboost_time_ms=xgboost_time,
            enhancement_method="XGBOOST_CONTEXT"
        )
        
        print(f"✅ XGBoost enhancement complete:")
        print(f"   Static Probability: {base_prediction.static_probability:.3f}")
        print(f"   XGBoost Probability: {xgboost_probability:.3f}")
        print(f"   Adjustment: {probability_adjustment:+.3f}")
        print(f"   Confidence: {xgboost_confidence:.3f}")
        print(f"   Processing Time: {xgboost_time:.2f}ms")
        
        return enhanced_prediction
    
    def display_enhanced_prediction(self, enhanced: EnhancedPrediction):
        """Display enhanced prediction results"""
        
        base = enhanced.base_prediction
        
        print(f"\n🤖 XGBOOST ENHANCED PREDICTION")
        print("=" * 40)
        print(f"📅 Session: {base.target_session} on {base.target_date}")
        print(f"⏰ Enhancement Time: {datetime.now().strftime('%H:%M:%S')}")
        
        print(f"\n📊 PROBABILITY ANALYSIS:")
        print(f"   PDA Static: {base.static_probability:.1%}")
        print(f"   XGBoost Enhanced: {enhanced.xgboost_probability:.1%}")
        print(f"   Adjustment: {enhanced.probability_adjustment:+.1%}")
        print(f"   XGBoost Confidence: {enhanced.xgboost_confidence:.3f}")
        
        print(f"\n🔍 CONTEXT FEATURES:")
        if enhanced.context_features:
            ctx = enhanced.context_features
            print(f"   Pattern: {base.identified_pattern_id} (ID: {ctx.pattern_id_encoded})")
            print(f"   Volatility Regime: {ctx.volatility_regime:.2f}")
            print(f"   Trend Strength: {ctx.trend_strength:+.2f}")
            print(f"   Momentum Score: {ctx.momentum_score:+.2f}")
            print(f"   Energy Carryover: {ctx.energy_carryover:.1f}")
            print(f"   Active FPFVGs: {ctx.fpfvg_active_count}")
        
        print(f"\n⚡ PERFORMANCE:")
        print(f"   PDA Time: {base.pda_time_ms:.2f}ms")
        print(f"   XGBoost Time: {enhanced.xgboost_time_ms:.2f}ms")
        print(f"   Total Pipeline: {base.total_time_ms + enhanced.xgboost_time_ms:.2f}ms")
        
        # Enhanced trading recommendation
        prob = enhanced.xgboost_probability
        if prob >= 0.8:
            recommendation = "🟢 HIGH CONFIDENCE - Strong XGBoost confirmation"
        elif prob >= 0.65:
            recommendation = "🟡 MODERATE CONFIDENCE - XGBoost positive signal"
        elif prob <= 0.35:
            recommendation = "🔴 LOW CONFIDENCE - XGBoost negative signal"
        else:
            recommendation = "🔵 NEUTRAL - Mixed XGBoost signals"
        
        print(f"\n💡 ENHANCED RECOMMENDATION:")
        print(f"   {recommendation}")

def demo_xgboost_enhancement():
    """Demonstrate XGBoost enhancement of pipeline predictions"""
    
    print("🧪 XGBOOST ENHANCEMENT DEMONSTRATION")
    print("=" * 45)
    
    # Create pipeline and enhancer
    pipeline = PredictionPipeline()
    enhancer = XGBoostContextEnhancer()
    
    # Get base prediction
    base_prediction = pipeline.predict_session("NYAM", "2025_08_07")
    
    if base_prediction:
        # Enhance with XGBoost
        enhanced_prediction = enhancer.enhance_prediction(base_prediction)
        
        # Display results
        enhancer.display_enhanced_prediction(enhanced_prediction)
        
        print(f"\n✅ XGBoost enhancement demonstration complete")
        return enhanced_prediction
    else:
        print(f"\n❌ XGBoost enhancement demonstration failed")
        return None

def main():
    """Main function for XGBoost context enhancement"""
    
    result = demo_xgboost_enhancement()
    return result

if __name__ == "__main__":
    enhancement_result = main()
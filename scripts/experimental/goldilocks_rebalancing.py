#!/usr/bin/env python3
"""
Goldilocks Rebalancing - Escape the Degenerate Equilibrium
=========================================================

Correcting the catastrophic 50% constant function that achieved perfect stability
through complete ignorance. 

Mathematical Objective: Find optimal balance ratio r* where:
- ∂Accuracy/∂r = 0 (maximum accuracy)  
- Variance < 0.1 (acceptable stability)
- I(features; predictions) > 0.3 bits (genuine learning)
- H(predictions) < 0.9 bits (some confidence, not maximum entropy)

Strategy: Progressive Rebalancing
- Test ratios: 70:30, 75:25, 80:20 cascade:non-cascade
- SMOTE non-cascades only to maintain genuine cascade diversity
- Information-theoretic validation at each step
- Target: 75% accuracy ± 5% variance with genuine pattern recognition

Escape the quantum coin flip, build genuine intelligence.
"""

import json
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.metrics import accuracy_score, balanced_accuracy_score, log_loss
from sklearn.preprocessing import StandardScaler
from sklearn.calibration import calibration_curve
import pickle
from scipy import stats
import matplotlib.pyplot as plt

# SMOTE for controlled minority oversampling
try:
    from imblearn.over_sampling import SMOTE, BorderlineSMOTE
    SMOTE_AVAILABLE = True
except ImportError:
    SMOTE_AVAILABLE = False

from xgboost_real_trainer import XGBoostRealTrainer, TrainingExample

@dataclass
class InformationMetrics:
    """Information-theoretic metrics to validate genuine learning"""
    mutual_information: float        # I(features; predictions) - should be > 0.3 bits
    prediction_entropy: float       # H(predictions) - should be < 0.9 bits  
    cross_entropy: float            # Cross entropy loss
    kl_divergence: float            # KL divergence from uniform distribution
    fisher_information: float       # Fisher information (approximate)
    learning_signal_ratio: float    # Signal/noise ratio in learning

@dataclass
class GoldilocksMetrics:
    """Comprehensive metrics for optimal balance point"""
    balance_ratio: float
    cascade_percentage: float
    
    # Accuracy metrics
    balanced_accuracy: float
    regular_accuracy: float
    precision: float
    recall: float
    
    # Stability metrics  
    cv_mean: float
    cv_std: float
    overfitting_ratio: float
    
    # Information-theoretic metrics
    information_metrics: InformationMetrics
    
    # Composite scores
    goldilocks_score: float         # Overall optimality score
    production_readiness: float     # 0-1 score for deployment readiness

class GoldilocksRebalancer:
    """
    Find the optimal balance point that maximizes information while maintaining stability
    
    Mathematical principle: Minimize KL divergence between training and test distributions
    while maximizing Fisher information above the learning threshold.
    """
    
    def __init__(self):
        self.trainer = XGBoostRealTrainer()
        self.original_data = None
        self.candidate_models = {}  # Store models at different balance ratios
        self.information_diagnostics = {}
        
        print("🎯 GOLDILOCKS REBALANCER")
        print("=" * 30)
        print("Objective: Escape degenerate equilibrium")  
        print("Method: Progressive ratio testing with information theory")
        print("Target: I(features; predictions) > 0.3 bits")
        print("Goal: 75% accuracy ± 5% variance with genuine learning")
        print()
    
    def calculate_information_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, 
                                     y_proba: np.ndarray, X: np.ndarray) -> InformationMetrics:
        """Calculate information-theoretic metrics to validate genuine learning"""
        
        # 1. Mutual Information (approximate via correlation)
        # True MI requires continuous estimation, use correlation as proxy
        feature_correlations = []
        for i in range(X.shape[1]):
            corr = abs(np.corrcoef(X[:, i], y_proba)[0, 1])
            if not np.isnan(corr):
                feature_correlations.append(corr)
        
        # Convert max correlation to mutual information estimate  
        max_corr = max(feature_correlations) if feature_correlations else 0.0
        mutual_information = -0.5 * np.log(1 - max_corr**2) if max_corr < 0.99 else 2.0
        
        # 2. Prediction Entropy
        # H(predictions) = -Σ p(y) log p(y) 
        pred_prob_dist = np.bincount(y_pred, minlength=2) / len(y_pred)
        pred_prob_dist = pred_prob_dist[pred_prob_dist > 0]  # Remove zeros
        prediction_entropy = -np.sum(pred_prob_dist * np.log2(pred_prob_dist))
        
        # 3. Cross Entropy Loss
        cross_entropy = log_loss(y_true, y_proba)
        
        # 4. KL Divergence from uniform (0.5, 0.5)
        uniform_dist = np.array([0.5, 0.5])
        kl_divergence = np.sum(pred_prob_dist * np.log(pred_prob_dist / uniform_dist))
        
        # 5. Fisher Information (approximate)
        # Based on curvature of log-likelihood
        prob_variance = np.var(y_proba)
        fisher_information = 1.0 / (prob_variance + 1e-8)  # Inverse variance
        
        # 6. Learning Signal Ratio
        # Ratio of explained variance to noise
        signal_var = np.var(y_proba)  
        noise_var = cross_entropy
        learning_signal_ratio = signal_var / (noise_var + 1e-8)
        
        metrics = InformationMetrics(
            mutual_information=mutual_information,
            prediction_entropy=prediction_entropy,
            cross_entropy=cross_entropy,
            kl_divergence=abs(kl_divergence),  # Absolute value
            fisher_information=fisher_information,
            learning_signal_ratio=learning_signal_ratio
        )
        
        return metrics
    
    def create_balanced_dataset(self, X: np.ndarray, y: np.ndarray, 
                               target_cascade_ratio: float = 0.7) -> Tuple[np.ndarray, np.ndarray]:
        """Create balanced dataset with specified cascade ratio using SMOTE"""
        
        print(f"⚖️ Creating {target_cascade_ratio:.0%}:{1-target_cascade_ratio:.0%} balanced dataset...")
        
        # Current class distribution
        cascade_indices = np.where(y == 1)[0]
        non_cascade_indices = np.where(y == 0)[0]
        
        n_cascades = len(cascade_indices)
        n_non_cascades = len(non_cascade_indices)
        
        print(f"   Original: {n_cascades} cascades, {n_non_cascades} non-cascades")
        
        # Calculate target sizes
        if target_cascade_ratio >= n_cascades / (n_cascades + n_non_cascades):
            # Need to reduce cascades or increase non-cascades
            target_total = int(n_non_cascades / (1 - target_cascade_ratio))
            target_n_cascades = int(target_total * target_cascade_ratio)
            target_n_non_cascades = target_total - target_n_cascades
            
            # Sample down cascades if needed
            if target_n_cascades < n_cascades:
                np.random.seed(42)
                selected_cascade_indices = np.random.choice(
                    cascade_indices, size=target_n_cascades, replace=False
                )
            else:
                selected_cascade_indices = cascade_indices
                
            # Use all non-cascades and SMOTE to target
            selected_non_cascade_indices = non_cascade_indices
            
        else:
            # Need to increase cascades (unlikely with our data)
            target_total = int(n_cascades / target_cascade_ratio)
            target_n_cascades = n_cascades
            target_n_non_cascades = target_total - target_n_cascades
            
            selected_cascade_indices = cascade_indices
            
            # Sample down non-cascades if needed
            if target_n_non_cascades < n_non_cascades:
                np.random.seed(42)
                selected_non_cascade_indices = np.random.choice(
                    non_cascade_indices, size=target_n_non_cascades, replace=False
                )
            else:
                selected_non_cascade_indices = non_cascade_indices
        
        # Combine selected samples
        balanced_indices = np.concatenate([selected_cascade_indices, selected_non_cascade_indices])
        X_pre_smote = X[balanced_indices]
        y_pre_smote = y[balanced_indices]
        
        # Apply SMOTE to minority class if available
        if SMOTE_AVAILABLE and len(np.unique(y_pre_smote)) > 1:
            try:
                # Calculate how many samples we need for target ratio
                current_cascade_count = np.sum(y_pre_smote == 1)
                current_non_cascade_count = np.sum(y_pre_smote == 0)
                
                # Determine target counts for exact ratio
                if target_cascade_ratio > 0.5:
                    # More cascades needed - oversample non-cascades  
                    target_non_cascade_count = int(current_cascade_count * (1 - target_cascade_ratio) / target_cascade_ratio)
                    smote_strategy = {0: target_non_cascade_count, 1: current_cascade_count}
                else:
                    # More non-cascades needed - oversample cascades
                    target_cascade_count = int(current_non_cascade_count * target_cascade_ratio / (1 - target_cascade_ratio))
                    smote_strategy = {0: current_non_cascade_count, 1: target_cascade_count}
                
                # Apply SMOTE with specific strategy
                smote = BorderlineSMOTE(
                    sampling_strategy=smote_strategy,
                    k_neighbors=min(3, min(current_cascade_count, current_non_cascade_count) - 1),
                    random_state=42
                )
                
                X_balanced, y_balanced = smote.fit_resample(X_pre_smote, y_pre_smote)
                
                print(f"   SMOTE applied: {len(X_pre_smote)} → {len(X_balanced)} samples")
                
            except Exception as e:
                print(f"   SMOTE failed: {e}, using pre-SMOTE data")
                X_balanced, y_balanced = X_pre_smote, y_pre_smote
        else:
            print(f"   SMOTE not available, using manual balancing")
            X_balanced, y_balanced = X_pre_smote, y_pre_smote
        
        final_cascade_ratio = np.mean(y_balanced)
        print(f"✅ Balanced dataset created:")
        print(f"   Final samples: {len(X_balanced)}")
        print(f"   Final cascade ratio: {final_cascade_ratio:.1%}")
        
        return X_balanced, y_balanced
    
    def train_goldilocks_model(self, X_balanced: np.ndarray, y_balanced: np.ndarray,
                              balance_ratio: float) -> GradientBoostingClassifier:
        """Train model with moderate regularization - not too hot, not too cold"""
        
        print(f"🎯 Training Goldilocks model for {balance_ratio:.0%} cascade ratio...")
        
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_balanced)
        
        # Goldilocks hyperparameters - moderate regularization
        model = GradientBoostingClassifier(
            n_estimators=100,         # Moderate complexity
            max_depth=4,              # Not too deep, not too shallow  
            learning_rate=0.05,       # Moderate learning rate
            subsample=0.8,            # Some stochasticity
            max_features=0.7,         # Use most features
            min_samples_split=8,      # Moderate split threshold
            min_samples_leaf=4,       # Moderate leaf threshold
            random_state=42,
            validation_fraction=0.2,  # Validation for early stopping
            n_iter_no_change=15       # Moderate patience
        )
        
        # Train model
        model.fit(X_scaled, y_balanced)
        
        # Store scaler with model
        model.scaler = scaler
        
        training_accuracy = model.score(X_scaled, y_balanced)
        print(f"   Training accuracy: {training_accuracy:.1%}")
        
        return model
    
    def evaluate_goldilocks_candidate(self, model: GradientBoostingClassifier,
                                     X_original: np.ndarray, y_original: np.ndarray,
                                     X_balanced: np.ndarray, y_balanced: np.ndarray,
                                     balance_ratio: float) -> GoldilocksMetrics:
        """Comprehensive evaluation of candidate model"""
        
        print(f"📊 Evaluating {balance_ratio:.0%} cascade ratio candidate...")
        
        # Scale original data with model's scaler
        X_scaled = model.scaler.transform(X_original)
        
        # Predictions
        y_pred = model.predict(X_scaled)
        y_proba = model.predict_proba(X_scaled)[:, 1]
        
        # Basic accuracy metrics
        balanced_accuracy = balanced_accuracy_score(y_original, y_pred)
        regular_accuracy = accuracy_score(y_original, y_pred)
        
        # Precision/Recall with zero-division handling
        from sklearn.metrics import precision_score, recall_score
        precision = precision_score(y_original, y_pred, zero_division=0.0)
        recall = recall_score(y_original, y_pred, zero_division=0.0)
        
        # Cross-validation stability
        cv = StratifiedKFold(n_splits=min(5, len(X_original)//10), shuffle=True, random_state=42)
        cv_scores = cross_val_score(model, X_scaled, y_original, cv=cv, scoring='balanced_accuracy')
        cv_mean = np.mean(cv_scores)
        cv_std = np.std(cv_scores)
        
        # Overfitting ratio
        training_accuracy = model.score(model.scaler.transform(X_balanced), y_balanced)
        overfitting_ratio = training_accuracy / balanced_accuracy if balanced_accuracy > 0 else 999
        
        # Information-theoretic metrics
        info_metrics = self.calculate_information_metrics(y_original, y_pred, y_proba, X_original)
        
        # Composite scores
        # Goldilocks score: Balance of accuracy, stability, and information
        accuracy_component = balanced_accuracy  # 0-1
        stability_component = max(0, 1 - cv_std*10)  # Penalize high variance heavily
        information_component = min(1, info_metrics.mutual_information / 0.5)  # Target 0.5+ bits
        
        goldilocks_score = (accuracy_component * stability_component * information_component) ** (1/3)
        
        # Production readiness score
        # Penalize low accuracy, high variance, or low information
        prod_accuracy = 1.0 if balanced_accuracy > 0.75 else balanced_accuracy / 0.75
        prod_stability = 1.0 if cv_std < 0.05 else max(0, 1 - (cv_std - 0.05) * 10)
        prod_information = 1.0 if info_metrics.mutual_information > 0.3 else info_metrics.mutual_information / 0.3
        
        production_readiness = (prod_accuracy * prod_stability * prod_information) ** (1/3)
        
        metrics = GoldilocksMetrics(
            balance_ratio=balance_ratio,
            cascade_percentage=np.mean(y_balanced) * 100,
            balanced_accuracy=balanced_accuracy,
            regular_accuracy=regular_accuracy,
            precision=precision,
            recall=recall,
            cv_mean=cv_mean,
            cv_std=cv_std,
            overfitting_ratio=overfitting_ratio,
            information_metrics=info_metrics,
            goldilocks_score=goldilocks_score,
            production_readiness=production_readiness
        )
        
        print(f"✅ Evaluation complete:")
        print(f"   Balanced Accuracy: {balanced_accuracy:.1%}")
        print(f"   CV Stability: ±{cv_std:.1%}")
        print(f"   Mutual Information: {info_metrics.mutual_information:.3f} bits")
        print(f"   Prediction Entropy: {info_metrics.prediction_entropy:.3f} bits")
        print(f"   Goldilocks Score: {goldilocks_score:.3f}")
        print(f"   Production Readiness: {production_readiness:.3f}")
        
        return metrics
    
    def progressive_ratio_search(self, X_original: np.ndarray, y_original: np.ndarray) -> Dict[float, GoldilocksMetrics]:
        """Test multiple balance ratios to find optimal point"""
        
        print("🔍 PROGRESSIVE RATIO SEARCH")
        print("=" * 35)
        
        # Test ratios from conservative to aggressive
        test_ratios = [0.60, 0.65, 0.70, 0.75, 0.80]  # 60% to 80% cascade ratios
        
        ratio_results = {}
        
        for ratio in test_ratios:
            print(f"\n🧪 Testing {ratio:.0%} cascade ratio...")
            
            try:
                # Create balanced dataset
                X_balanced, y_balanced = self.create_balanced_dataset(X_original, y_original, ratio)
                
                # Train candidate model
                model = self.train_goldilocks_model(X_balanced, y_balanced, ratio)
                
                # Evaluate candidate
                metrics = self.evaluate_goldilocks_candidate(
                    model, X_original, y_original, X_balanced, y_balanced, ratio
                )
                
                # Store results
                ratio_results[ratio] = metrics
                self.candidate_models[ratio] = model
                
                print(f"✅ Ratio {ratio:.0%} complete - Goldilocks Score: {metrics.goldilocks_score:.3f}")
                
            except Exception as e:
                print(f"❌ Ratio {ratio:.0%} failed: {e}")
                continue
        
        return ratio_results
    
    def find_optimal_ratio(self, ratio_results: Dict[float, GoldilocksMetrics]) -> Tuple[float, GoldilocksMetrics]:
        """Find the optimal balance ratio based on composite scoring"""
        
        print(f"\n🎯 OPTIMAL RATIO SELECTION")
        print("=" * 30)
        
        # Sort by Goldilocks score
        sorted_results = sorted(ratio_results.items(), key=lambda x: x[1].goldilocks_score, reverse=True)
        
        print("📊 Ranking by Goldilocks Score:")
        for i, (ratio, metrics) in enumerate(sorted_results, 1):
            print(f"   {i}. {ratio:.0%} cascade ratio:")
            print(f"      Goldilocks Score: {metrics.goldilocks_score:.3f}")
            print(f"      Balanced Accuracy: {metrics.balanced_accuracy:.1%}")
            print(f"      CV Stability: ±{metrics.cv_std:.1%}")
            print(f"      Mutual Information: {metrics.information_metrics.mutual_information:.3f} bits")
            print(f"      Production Ready: {metrics.production_readiness:.3f}")
            print()
        
        # Select optimal ratio
        optimal_ratio, optimal_metrics = sorted_results[0]
        
        print(f"🏆 OPTIMAL RATIO SELECTED: {optimal_ratio:.0%}")
        print(f"   Goldilocks Score: {optimal_metrics.goldilocks_score:.3f}")
        print(f"   Production Readiness: {optimal_metrics.production_readiness:.3f}")
        
        return optimal_ratio, optimal_metrics
    
    def validate_information_recovery(self, optimal_metrics: GoldilocksMetrics) -> bool:
        """Validate that we've escaped the degenerate equilibrium"""
        
        print(f"\n🔬 INFORMATION RECOVERY VALIDATION")
        print("=" * 40)
        
        info = optimal_metrics.information_metrics
        
        recovery_tests = []
        
        # Test 1: Mutual Information > threshold
        if info.mutual_information > 0.3:
            recovery_tests.append("✅ Mutual Information recovered (>0.3 bits)")
        else:
            recovery_tests.append(f"❌ Mutual Information still low ({info.mutual_information:.3f} bits)")
        
        # Test 2: Prediction Entropy < maximum
        if info.prediction_entropy < 0.95:  # Less than near-maximum entropy
            recovery_tests.append("✅ Prediction Entropy below maximum")
        else:
            recovery_tests.append(f"❌ Prediction Entropy too high ({info.prediction_entropy:.3f} bits)")
        
        # Test 3: KL divergence from uniform
        if info.kl_divergence > 0.1:  # Significant divergence from uniform
            recovery_tests.append("✅ Predictions deviate from uniform distribution")
        else:
            recovery_tests.append(f"❌ Predictions too uniform (KL={info.kl_divergence:.3f})")
        
        # Test 4: Learning signal ratio
        if info.learning_signal_ratio > 1.0:
            recovery_tests.append("✅ Learning signal exceeds noise")
        else:
            recovery_tests.append(f"❌ Insufficient learning signal ({info.learning_signal_ratio:.3f})")
        
        for test in recovery_tests:
            print(f"   {test}")
        
        # Overall recovery assessment
        passed_tests = sum(1 for test in recovery_tests if test.startswith("✅"))
        recovery_success = passed_tests >= 3
        
        if recovery_success:
            print(f"\n🎉 INFORMATION RECOVERY SUCCESSFUL ({passed_tests}/4 tests passed)")
            print("   Escaped degenerate equilibrium - genuine learning detected!")
        else:
            print(f"\n⚠️ PARTIAL INFORMATION RECOVERY ({passed_tests}/4 tests passed)")
            print("   Still showing signs of degenerate behavior")
        
        return recovery_success
    
    def save_optimal_model(self, optimal_ratio: float, filename: str = "goldilocks_xgboost_model.pkl"):
        """Save the optimal Goldilocks model"""
        
        if optimal_ratio not in self.candidate_models:
            print("❌ No optimal model to save")
            return False
        
        optimal_model = self.candidate_models[optimal_ratio]
        
        model_data = {
            'model': optimal_model,
            'scaler': optimal_model.scaler,
            'pattern_encoder': self.trainer.enhancer.pattern_encoder,
            'optimal_ratio': optimal_ratio,
            'model_type': 'goldilocks_xgboost',
            'balancing_method': f'progressive_ratio_{optimal_ratio:.0%}',
            'hyperparameters': {
                'n_estimators': 100,
                'max_depth': 4,
                'learning_rate': 0.05,
                'subsample': 0.8,
                'max_features': 0.7
            },
            'training_date': pd.Timestamp.now().isoformat(),
            'philosophy': 'Information-theoretic optimization'
        }
        
        with open(filename, 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"✅ Optimal Goldilocks model saved: {filename}")
        return True
    
    def execute_goldilocks_search(self) -> Dict[str, Any]:
        """Execute complete Goldilocks rebalancing search"""
        
        print("🎯 EXECUTING GOLDILOCKS REBALANCING SEARCH")
        print("=" * 50)
        print("Mission: Escape degenerate equilibrium via information optimization")
        print()
        
        # Step 1: Load original data
        sessions = self.trainer.load_enhanced_sessions()
        examples = self.trainer.extract_training_examples(sessions)
        X_original, y_original = self.trainer.prepare_training_data(examples)
        
        print(f"📁 Original data: {len(examples)} examples, {np.mean(y_original):.1%} cascade rate")
        
        # Step 2: Progressive ratio search
        ratio_results = self.progressive_ratio_search(X_original, y_original)
        
        if not ratio_results:
            print("❌ All ratio tests failed")
            return {'success': False, 'error': 'No viable ratios found'}
        
        # Step 3: Find optimal ratio
        optimal_ratio, optimal_metrics = self.find_optimal_ratio(ratio_results)
        
        # Step 4: Validate information recovery
        recovery_success = self.validate_information_recovery(optimal_metrics)
        
        # Step 5: Save optimal model
        model_saved = self.save_optimal_model(optimal_ratio)
        
        # Final results
        results = {
            'success': recovery_success,
            'optimal_ratio': optimal_ratio,
            'optimal_metrics': optimal_metrics,
            'all_ratio_results': ratio_results,
            'information_recovery': recovery_success,
            'model_saved': model_saved,
            'goldilocks_score': optimal_metrics.goldilocks_score,
            'production_readiness': optimal_metrics.production_readiness
        }
        
        print(f"\n🎯 GOLDILOCKS SEARCH COMPLETE")
        print(f"Success: {'YES' if recovery_success else 'PARTIAL'}")
        print(f"Optimal Ratio: {optimal_ratio:.0%} cascade")
        print(f"Balanced Accuracy: {optimal_metrics.balanced_accuracy:.1%}")
        print(f"Mutual Information: {optimal_metrics.information_metrics.mutual_information:.3f} bits")
        print(f"CV Stability: ±{optimal_metrics.cv_std:.1%}")
        print(f"Goldilocks Score: {optimal_metrics.goldilocks_score:.3f}")
        
        if recovery_success:
            print("🎉 Successfully escaped degenerate equilibrium!")
            print("   Ready for production deployment")
        else:
            print("⚠️ Partial recovery - further optimization needed")
        
        return results

def main():
    """Execute Goldilocks rebalancing search"""
    
    rebalancer = GoldilocksRebalancer()
    results = rebalancer.execute_goldilocks_search()
    
    return results

if __name__ == "__main__":
    goldilocks_results = main()
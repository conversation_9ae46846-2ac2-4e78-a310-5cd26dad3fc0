#!/usr/bin/env python3
"""
Benchmark for Mathematical Architecture

Scenarios:
- FFTOptimizedCorrelator vs direct (time-domain) correlation scaling
- HawkesAlgorithmImplementation vectorized intensity computation up to 5k events
- MathematicalOptimizationCompartment latency for predict operation vs 200ms SLI

Data sizes: [100, 500, 1000, 2000, 5000]
Metrics: runtime (ms), memory estimate (MB)
"""

import os
import sys
import time
from dataclasses import dataclass
from typing import List, Dict, Any

import numpy as np

# Local imports
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

# Provide a lightweight psutil stub if not available (to allow importing scaling modules)
try:
    import psutil  # type: ignore
except Exception:  # pragma: no cover
    import types
    class _VMem:
        def __init__(self):
            self.available = int(8 * 1024 ** 3)
            self.total = int(16 * 1024 ** 3)
            self.percent = 20.0
    def _cpu_percent(interval=None):
        # Ignore interval to be compatible with psutil API
        return 5.0
    psutil = types.SimpleNamespace(
        cpu_count=lambda: 4,
        virtual_memory=lambda: _VMem(),
        cpu_percent=_cpu_percent,
    )
    sys.modules['psutil'] = psutil

from core_predictor.mathematical_layers.core_algorithms import (
    FFTOptimizedCorrelator,
    HawkesAlgorithmImplementation,
)
from src.ironpulse.compartments.mathematical_optimization import MathematicalOptimizationCompartment

@dataclass
class BenchResult:
    size: int
    time_ms: float
    mem_mb: float


def memory_estimate_mb(arr: np.ndarray) -> float:
    return float(arr.nbytes) / (1024 * 1024)


def bench_fft_correlator(data_sizes: List[int]) -> List[BenchResult]:
    fft = FFTOptimizedCorrelator()

    results: List[BenchResult] = []
    for n in data_sizes:
        signal1 = np.random.randn(n)
        signal2 = np.random.randn(n)
        data = np.vstack([signal1, signal2])

        start = time.perf_counter()
        corr = fft.compute_core_function(data, {"detrend": True})
        dt_ms = (time.perf_counter() - start) * 1000.0
        results.append(BenchResult(n, dt_ms, memory_estimate_mb(corr)))
    return results


def bench_direct_correlation(data_sizes: List[int]) -> List[BenchResult]:
    """Naive O(n^2) time-domain cross-correlation for comparison."""
    results: List[BenchResult] = []
    for n in data_sizes:
        x = np.random.randn(n)
        y = np.random.randn(n)
        # naive correlation: for each lag compute dot product
        start = time.perf_counter()
        corrs = []
        for lag in range(n):
            # correlate x with y shifted by lag
            if lag == 0:
                corrs.append(np.dot(x, y))
            else:
                corrs.append(np.dot(x[lag:], y[:-lag]))
        corrs = np.array(corrs)
        dt_ms = (time.perf_counter() - start) * 1000.0
        results.append(BenchResult(n, dt_ms, memory_estimate_mb(corrs)))
    return results


def bench_hawkes_vectorized(data_sizes: List[int]) -> List[BenchResult]:
    hawkes = HawkesAlgorithmImplementation(precision=30, vectorized=True)
    params = {"mu": 0.02, "alpha": 35.51, "beta": 0.00442}

    results: List[BenchResult] = []
    for n in data_sizes:
        # Create increasing timestamps with some jitter
        events = np.cumsum(np.random.exponential(scale=1.0, size=n)).astype(np.float64)
        start = time.perf_counter()
        intensities = hawkes.compute_core_function(events, params)
        dt_ms = (time.perf_counter() - start) * 1000.0
        results.append(BenchResult(n, dt_ms, memory_estimate_mb(intensities)))
    return results


def bench_compartment_latency(n_events: int) -> Dict[str, Any]:
    comp = MathematicalOptimizationCompartment()
    # Synthetic session data with n_events timestamps
    session_data = {
        "events": [{"timestamp": float(t), "type": "event"} for t in np.cumsum(np.random.exponential(size=n_events))]
    }
    request = {
        "operation": "predict",
        "model_type": "hawkes_process",
        "session_data": session_data,
        "parameters": {"mu": 0.02, "alpha": 10.0, "beta": 0.01},
        "performance_target": {"max_execution_time_seconds": 0.2},
    }

    start = time.perf_counter()
    result = comp.process(request)
    dt_ms = (time.perf_counter() - start) * 1000.0

    return {
        "status": result.status.value,
        "exec_time_ms": dt_ms,
        "comp_metrics": result.metrics,
    }


def main():
    data_sizes = [100, 500, 1000, 2000, 5000]

    print("\n=== FFT vs Direct Correlation Scaling ===")
    fft_res = bench_fft_correlator(data_sizes)
    direct_res = bench_direct_correlation([100, 500, 1000])  # limit naive to 1k to keep runtime reasonable
    for r in fft_res:
        print(f"FFT n={r.size:5d}: {r.time_ms:7.2f} ms, mem≈{r.mem_mb:6.3f} MB")
    for r in direct_res:
        print(f"Direct n={r.size:5d}: {r.time_ms:7.2f} ms, mem≈{r.mem_mb:6.3f} MB")

    print("\n=== Hawkes Vectorized Intensity ===")
    hawkes_res = bench_hawkes_vectorized(data_sizes)
    for r in hawkes_res:
        print(f"Hawkes n={r.size:5d}: {r.time_ms:7.2f} ms, mem≈{r.mem_mb:6.3f} MB")

    print("\n=== Compartment Latency vs 200ms SLI ===")
    for n in [100, 500, 1000, 2000, 5000]:
        res = bench_compartment_latency(n)
        print(f"Compartment n={n:5d}: status={res['status']}, wall={res['exec_time_ms']:.2f} ms, reported={res['comp_metrics'].get('execution_time_ms', 0):.2f} ms")


if __name__ == "__main__":
    main()


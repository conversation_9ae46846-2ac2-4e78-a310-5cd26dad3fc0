#!/usr/bin/env python3
"""
Generate Non-Cascade Scenarios - Solve the Real Bottleneck
==========================================================

Generate 1000+ synthetic non-cascade patterns to balance the dataset.
This solves your actual problem: 10 non-cascades vs 70+ cascades.

Mathematical approach: Invert cascade grammar rules to create valid non-cascades.
"""

import json
import random
import time
from typing import List, Dict, Any
from pathlib import Path

def generate_non_cascade_scenarios(count: int = 1000) -> List[Dict[str, Any]]:
    """
    Generate synthetic non-cascade patterns that respect market logic
    but break the cascade CFG rules
    """
    
    print(f"🔄 Generating {count} synthetic non-cascade scenarios...")
    
    # Base non-cascade pattern templates
    non_cascade_templates = [
        # Sideways chop - expansion without follow-through
        {
            'pattern_name': 'sideways_chop',
            'events': [
                {'type': 'expansion_high', 'magnitude': 0.3},
                {'type': 'consolidation', 'magnitude': 0.1}, 
                {'type': 'expansion_high', 'magnitude': 0.2},
                {'type': 'consolidation', 'magnitude': 0.1}
            ],
            'description': 'Range-bound movement without cascade'
        },
        
        # Failed breakout - expansion that reverses
        {
            'pattern_name': 'failed_breakout',
            'events': [
                {'type': 'expansion_high', 'magnitude': 0.4},
                {'type': 'momentum_shift', 'magnitude': -0.3},
                {'type': 'retracement_low', 'magnitude': -0.2}
            ],
            'description': 'Breakout attempt that fails and reverses'
        },
        
        # Range bound - pure consolidation
        {
            'pattern_name': 'range_bound',
            'events': [
                {'type': 'consolidation', 'magnitude': 0.1},
                {'type': 'range_formation', 'magnitude': 0.05},
                {'type': 'consolidation', 'magnitude': 0.08}
            ],
            'description': 'Tight range without directional movement'
        },
        
        # Fakeout reversal - liquidity grab without cascade
        {
            'pattern_name': 'fakeout_reversal',
            'events': [
                {'type': 'liquidity_sweep', 'magnitude': 0.2},
                {'type': 'momentum_shift', 'magnitude': -0.4},
                {'type': 'consolidation', 'magnitude': 0.1}
            ],
            'description': 'Liquidity sweep followed by reversal, no cascade'
        },
        
        # Weak momentum - momentum break without follow-through
        {
            'pattern_name': 'weak_momentum',
            'events': [
                {'type': 'momentum_shift', 'magnitude': 0.2},
                {'type': 'consolidation', 'magnitude': 0.1},
                {'type': 'range_formation', 'magnitude': 0.05}
            ],
            'description': 'Momentum shift that loses steam'
        },
        
        # Double top/bottom - expansion that fails twice
        {
            'pattern_name': 'double_rejection',
            'events': [
                {'type': 'expansion_high', 'magnitude': 0.3},
                {'type': 'retracement_low', 'magnitude': -0.2},
                {'type': 'expansion_high', 'magnitude': 0.28},
                {'type': 'consolidation', 'magnitude': 0.05}
            ],
            'description': 'Double top/bottom pattern with rejection'
        }
    ]
    
    # Session contexts for variety
    session_contexts = ['PREMARKET', 'ASIA', 'LONDON', 'LUNCH', 'NYAM', 'NYPM']
    
    synthetic_scenarios = []
    
    for i in range(count):
        # Select random template
        template = random.choice(non_cascade_templates)
        
        # Add variation to magnitudes (±20%)
        events = []
        for event in template['events']:
            varied_magnitude = event['magnitude'] * random.uniform(0.8, 1.2)
            
            # Ensure non-cascade magnitudes stay small
            if varied_magnitude > 0:
                varied_magnitude = min(varied_magnitude, 0.5)  # Cap positive movements
            else:
                varied_magnitude = max(varied_magnitude, -0.4)  # Cap negative movements
            
            events.append({
                'type': event['type'],
                'magnitude': round(varied_magnitude, 3),
                'timestamp': time.time() + random.uniform(0, 3600),  # Within 1 hour
                'price': 23000 + random.uniform(-500, 500),  # Realistic price range
                'volume': random.uniform(50, 200),
                'significance': random.uniform(0.3, 0.7)  # Lower significance for non-cascades
            })
        
        # Create synthetic scenario
        scenario = {
            'scenario_id': f"non_cascade_{i+1:04d}",
            'pattern_name': template['pattern_name'],
            'description': template['description'],
            'events': events,
            'session_context': random.choice(session_contexts),
            'cascade_occurred': False,  # CRITICAL: This is a non-cascade
            'pattern_category': 'synthetic_non_cascade',
            'generation_timestamp': time.time(),
            'expected_cgg_pattern': 'no_pattern',  # CFG should not match cascade rules
            'confidence_threshold': random.uniform(0.1, 0.4)  # Low confidence expected
        }
        
        synthetic_scenarios.append(scenario)
    
    print(f"✅ Generated {len(synthetic_scenarios)} non-cascade scenarios")
    print(f"   Pattern distribution:")
    
    # Show pattern distribution
    pattern_counts = {}
    for scenario in synthetic_scenarios:
        pattern = scenario['pattern_name']
        pattern_counts[pattern] = pattern_counts.get(pattern, 0) + 1
    
    for pattern, count in pattern_counts.items():
        print(f"     {pattern}: {count} scenarios")
    
    return synthetic_scenarios

def save_synthetic_data(scenarios: List[Dict], filename: str = "synthetic_non_cascades.json"):
    """Save synthetic scenarios to file"""
    
    output_path = Path(filename)
    
    with open(output_path, 'w') as f:
        json.dump(scenarios, f, indent=2)
    
    print(f"💾 Saved {len(scenarios)} scenarios to {output_path}")
    return output_path

def validate_non_cascade_grammar(scenarios: List[Dict]) -> Dict[str, Any]:
    """
    Validate that synthetic scenarios break cascade CFG rules
    (ensuring they are genuine non-cascades)
    """
    
    print("🔍 Validating non-cascade grammar compliance...")
    
    # Import your CFG functions to test
    from production_oracle import parse_cascade_grammar, translate_taxonomy
    
    validation_results = {
        'total_scenarios': len(scenarios),
        'valid_non_cascades': 0,
        'accidental_cascades': 0,
        'pattern_distribution': {},
        'confidence_distribution': {}
    }
    
    accidental_cascades = []
    
    for scenario in scenarios:
        # Test CFG parsing on synthetic events
        events = scenario['events']
        pattern_id, confidence = parse_cascade_grammar(events)
        
        # Record pattern distribution
        pattern = pattern_id
        validation_results['pattern_distribution'][pattern] = \
            validation_results['pattern_distribution'].get(pattern, 0) + 1
        
        # Record confidence distribution
        conf_bucket = f"{int(confidence*10)*10}%-{int(confidence*10)*10+9}%"
        validation_results['confidence_distribution'][conf_bucket] = \
            validation_results['confidence_distribution'].get(conf_bucket, 0) + 1
        
        # Check if it accidentally created a cascade pattern
        if pattern_id in ['basic_cascade', 'complex_cascade', 'momentum_cascade'] and confidence > 0.7:
            validation_results['accidental_cascades'] += 1
            accidental_cascades.append(scenario['scenario_id'])
        else:
            validation_results['valid_non_cascades'] += 1
    
    success_rate = validation_results['valid_non_cascades'] / validation_results['total_scenarios']
    
    print(f"✅ Validation complete:")
    print(f"   Valid non-cascades: {validation_results['valid_non_cascades']} ({success_rate:.1%})")
    print(f"   Accidental cascades: {validation_results['accidental_cascades']}")
    
    if accidental_cascades:
        print(f"   Accidental cascade IDs: {accidental_cascades[:5]}...")  # Show first 5
    
    print(f"   Pattern distribution: {validation_results['pattern_distribution']}")
    
    return validation_results

def integrate_with_training_data(synthetic_scenarios: List[Dict]) -> str:
    """
    Convert synthetic scenarios to training data format compatible with existing models
    """
    
    print("🔗 Converting synthetic scenarios to training data format...")
    
    training_examples = []
    
    for scenario in synthetic_scenarios:
        # Convert to training example format
        training_example = {
            'session_id': scenario['scenario_id'],
            'session_type': scenario['session_context'],
            'events': scenario['events'],
            'cascade_occurred': scenario['cascade_occurred'],  # Always False
            'pattern_confidence': scenario['confidence_threshold'],
            'data_source': 'synthetic_generation',
            'validation_status': 'generated'
        }
        
        training_examples.append(training_example)
    
    # Save in training format
    training_file = "synthetic_training_data.json"
    with open(training_file, 'w') as f:
        json.dump(training_examples, f, indent=2)
    
    print(f"💾 Training data saved: {training_file}")
    print(f"   Ready for integration with existing XGBoost models")
    
    return training_file

def main():
    """Generate complete synthetic non-cascade dataset"""
    
    print("🎯 SYNTHETIC NON-CASCADE GENERATION")
    print("=" * 40)
    print("Objective: Balance dataset with 1000 non-cascade examples")
    print("Method: Grammar-aware synthetic pattern generation")
    print()
    
    # Generate synthetic scenarios
    scenarios = generate_non_cascade_scenarios(count=1000)
    
    # Save raw scenarios
    scenario_file = save_synthetic_data(scenarios)
    
    # Validate CFG compliance
    validation = validate_non_cascade_grammar(scenarios)
    
    # Convert to training format
    training_file = integrate_with_training_data(scenarios)
    
    print(f"\n🎉 SYNTHETIC DATA GENERATION COMPLETE")
    print(f"Files created:")
    print(f"  📄 {scenario_file} - Raw synthetic scenarios")
    print(f"  📄 {training_file} - Training data format")
    print(f"  📊 {validation['valid_non_cascades']} valid non-cascades generated")
    print(f"  🎯 Ready to retrain models with balanced dataset")
    print(f"  ⚖️ New ratio: ~1000 non-cascades vs ~70 cascades")
    
    return scenarios, validation

if __name__ == "__main__":
    synthetic_data, validation_results = main()
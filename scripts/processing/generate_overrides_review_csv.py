#!/usr/bin/env python3
"""
Generate a review sheet (CSV) for labeling unknown sessions.

Reads overrides_template.json (from generate_overrides_template.py) and emits a CSV
with one row per unknown item, including helpful context and hint tokens.

Columns:
- group_key (session_type=...|date=...)
- path
- filename
- stem
- session_type
- session_date
- file_type
- valid
- hint_tokens_matched (semicolon-separated)
- suggested_label (fill with 'cascade' or 'non_cascade')

Usage:
  python generate_overrides_review_csv.py \
    --template overrides_template.json \
    --output overrides_review.csv \
    --hint-tokens partial non_event non-event negative nocascade non_cascade incomplete no_completion
"""
from __future__ import annotations
import argparse
import csv
import json
from pathlib import Path
from typing import Dict, Any, List


def load_json(path: Path) -> Dict[str, Any]:
    with path.open("r") as f:
        return json.load(f)


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--template", default="overrides_template.json", help="Path to overrides_template.json")
    ap.add_argument("--output", default="overrides_review.csv", help="Path to write CSV")
    ap.add_argument("--hint-tokens", nargs="*", default=[
        "partial", "non_event", "non-event", "negative", "nocascade", "non_cascade", "incomplete", "no_completion"
    ], help="Tokens to flag as hints in path/filename")
    args = ap.parse_args()

    template_path = Path(args.template)
    if not template_path.exists():
        raise SystemExit(f"Template not found: {template_path}")

    tpl = load_json(template_path)
    groups: Dict[str, List[Dict[str, Any]]] = tpl.get("groups", {})

    tokens = [t.lower() for t in args.hint_tokens]

    with open(args.output, "w", newline="") as f:
        writer = csv.writer(f)
        writer.writerow([
            "group_key", "path", "filename", "stem", "session_type", "session_date", "file_type", "valid", "hint_tokens_matched", "suggested_label"
        ])
        for group_key, items in groups.items():
            for it in items:
                path = str(it.get("path") or "")
                p = Path(path) if path else Path("")
                filename = p.name
                stem = p.stem
                stype = it.get("session_type")
                sdate = it.get("session_date")
                ftype = it.get("file_type")
                valid = it.get("valid")
                plower = path.lower()
                matched = sorted({t for t in tokens if t and t in plower})
                writer.writerow([
                    group_key, path, filename, stem, stype, sdate, ftype, valid, ";".join(matched), ""
                ])

    print(f"Wrote review CSV to {args.output}")


if __name__ == "__main__":
    main()


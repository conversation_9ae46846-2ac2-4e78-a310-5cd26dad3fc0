"""Project Oracle Base System Integration

CRITICAL DEPENDENCY RESOLVER:
- Integrates Project Oracle with existing proven base system in /src
- Provides fallback mechanisms when base system unavailable  
- Ensures mathematical accuracy preservation from proven components
- Enables true hybrid operation (enhanced + proven base)

Architecture: Project Oracle Enhancement Layer + Proven Base System Foundation
Mathematical Foundation: Preserves all proven formulas while adding Gemini enhancements
"""

import sys
import os
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
import importlib.util
from dataclasses import dataclass

# Add base system to path
base_system_path = '/Users/<USER>/grok-claude-automation/src'
if base_system_path not in sys.path:
    sys.path.insert(0, base_system_path)

@dataclass
class BaseSystemStatus:
    """Status of base system integration"""
    available: bool
    components_found: List[str]
    missing_components: List[str]
    integration_level: str  # "full", "partial", "fallback"
    accuracy_preservation: bool

@dataclass
class IntegrationResult:
    """Result of base system integration attempt"""
    success: bool
    base_system_status: BaseSystemStatus
    integrated_components: Dict[str, Any]
    fallback_active: bool
    performance_impact: str

class BaseSystemIntegrator:
    """
    Integrates Project Oracle with existing proven base system
    
    Provides seamless integration with fallback capabilities:
    - Full integration when base system available
    - Partial integration with selective component use
    - Fallback to enhanced-only mode when necessary
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.base_system_available = False
        self.integrated_components = {}
        self.fallback_active = False
        
        self.logger.info("🔗 BASE SYSTEM INTEGRATOR: Initializing")
        
        # Attempt integration
        self.integration_result = self._attempt_base_system_integration()
        
        if self.integration_result.success:
            self.logger.info("✅ Base system integration successful")
            self.logger.info(f"   Integration Level: {self.integration_result.base_system_status.integration_level}")
            self.logger.info(f"   Components Available: {len(self.integration_result.integrated_components)}")
        else:
            self.logger.warning("⚠️ Base system integration failed - using fallback mode")
    
    def _attempt_base_system_integration(self) -> IntegrationResult:
        """Attempt to integrate with base system components"""
        
        # Critical base system components to integrate
        # Focus on components that don't have complex import dependencies
        target_components = {
            'theoretical_framework_integration': 'theoretical_framework_integration',  # Multi-theory (high priority)
            'utils': 'utils',  # Utility functions (standalone)
            'config': 'config',  # Configuration (standalone)
            'schemas': 'schemas',  # Data schemas (standalone)
            'rg_graphs_component': 'rg_graphs_component',  # RG Graphs (proven)
            'fractal_hawkes_component': 'fractal_hawkes_component',  # Fractal Hawkes (proven)
            'catastrophe_theory_component': 'catastrophe_theory_component'  # Catastrophe theory (proven)
        }
        
        found_components = []
        missing_components = []
        integrated_components = {}
        
        self.logger.info(f"🔍 Scanning for {len(target_components)} critical base components...")
        
        for component_name, module_name in target_components.items():
            try:
                # Check if module file exists
                module_path = Path(base_system_path) / f"{module_name}.py"
                
                if module_path.exists():
                    # Attempt to import
                    spec = importlib.util.spec_from_file_location(module_name, module_path)
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)
                    
                    integrated_components[component_name] = module
                    found_components.append(component_name)
                    
                    self.logger.info(f"   ✅ {component_name}: Available")
                else:
                    missing_components.append(component_name)
                    self.logger.warning(f"   ❌ {component_name}: Not found")
                    
            except Exception as e:
                missing_components.append(component_name)
                self.logger.warning(f"   ❌ {component_name}: Import failed - {e}")
        
        # Determine integration level
        component_coverage = len(found_components) / len(target_components)
        
        if component_coverage >= 0.8:  # 80%+ components available
            integration_level = "full"
            accuracy_preservation = True
        elif component_coverage >= 0.5:  # 50%+ components available
            integration_level = "partial"
            accuracy_preservation = True
        else:
            integration_level = "fallback"
            accuracy_preservation = False
        
        base_system_status = BaseSystemStatus(
            available=len(found_components) > 0,
            components_found=found_components,
            missing_components=missing_components,
            integration_level=integration_level,
            accuracy_preservation=accuracy_preservation
        )
        
        success = len(found_components) > 0
        fallback_active = integration_level == "fallback"
        
        performance_impact = {
            "full": "optimal_performance",
            "partial": "good_performance_some_fallbacks",
            "fallback": "reduced_performance_enhanced_only"
        }[integration_level]
        
        return IntegrationResult(
            success=success,
            base_system_status=base_system_status,
            integrated_components=integrated_components,
            fallback_active=fallback_active,
            performance_impact=performance_impact
        )
    
    def get_fractal_hawkes_component(self) -> Optional[Any]:
        """Get proven fractal Hawkes component if available"""
        
        hawkes_component = self.integrated_components.get('fractal_hawkes_component')
        if hawkes_component:
            try:
                if hasattr(hawkes_component, 'FractalHawkesComponent'):
                    component = hawkes_component.FractalHawkesComponent()
                    self.logger.info("✅ Fractal Hawkes component available")
                    return component
            except Exception as e:
                self.logger.error(f"❌ Fractal Hawkes component creation failed: {e}")
        
        return None
    
    def get_rg_graphs_component(self) -> Optional[Any]:
        """Get proven RG graphs component if available"""
        
        rg_component = self.integrated_components.get('rg_graphs_component')
        if rg_component:
            try:
                if hasattr(rg_component, 'RGGraphsComponent'):
                    component = rg_component.RGGraphsComponent()
                    self.logger.info("✅ RG graphs component available")
                    return component
            except Exception as e:
                self.logger.error(f"❌ RG graphs component creation failed: {e}")
        
        return None
    
    def get_multi_theory_framework(self) -> Optional[Any]:
        """Get proven multi-theory integration framework if available"""
        
        theory_component = self.integrated_components.get('theoretical_framework_integration')
        if theory_component:
            try:
                if hasattr(theory_component, 'TheoreticalFrameworkIntegration'):
                    framework = theory_component.TheoreticalFrameworkIntegration()
                    self.logger.info("✅ Multi-theory framework available")
                    return framework
            except Exception as e:
                self.logger.error(f"❌ Multi-theory framework creation failed: {e}")
        
        return None
    
    def get_catastrophe_theory_component(self) -> Optional[Any]:
        """Get proven catastrophe theory component if available"""
        
        catastrophe_component = self.integrated_components.get('catastrophe_theory_component')
        if catastrophe_component:
            try:
                if hasattr(catastrophe_component, 'CatastropheTheoryComponent'):
                    component = catastrophe_component.CatastropheTheoryComponent()
                    self.logger.info("✅ Catastrophe theory component available")
                    return component
            except Exception as e:
                self.logger.error(f"❌ Catastrophe theory component creation failed: {e}")
        
        return None
    
    def get_utils_module(self) -> Optional[Any]:
        """Get proven utils module if available"""
        
        utils_component = self.integrated_components.get('utils')
        if utils_component:
            self.logger.info("✅ Utils module available")
            return utils_component
        
        return None
    
    def get_base_schemas(self) -> Optional[Any]:
        """Get proven schemas module if available"""
        
        schemas_component = self.integrated_components.get('schemas')
        if schemas_component:
            self.logger.info("✅ Schemas module available")
            return schemas_component
        
        return None
    
    def create_hybrid_prediction_system(self) -> Dict[str, Any]:
        """Create hybrid system combining Project Oracle enhancements with base system"""
        
        hybrid_system = {
            'integration_level': self.integration_result.base_system_status.integration_level,
            'accuracy_preservation': self.integration_result.base_system_status.accuracy_preservation,
            'components': {}
        }
        
        # Add base system components if available
        if self.integration_result.success:
            base_components = {
                'multi_theory_framework': self.get_multi_theory_framework(),
                'fractal_hawkes_component': self.get_fractal_hawkes_component(),
                'rg_graphs_component': self.get_rg_graphs_component(),
                'catastrophe_theory_component': self.get_catastrophe_theory_component(),
                'utils_module': self.get_utils_module(),
                'base_schemas': self.get_base_schemas()
            }
            
            # Only include successfully created components
            for name, component in base_components.items():
                if component is not None:
                    hybrid_system['components'][name] = component
        
        # Always include Project Oracle enhancements (they have fallbacks)
        hybrid_system['components']['oracle_enhancements'] = True
        
        self.logger.info(f"🔗 Hybrid system created:")
        self.logger.info(f"   Integration Level: {hybrid_system['integration_level']}")
        self.logger.info(f"   Base Components: {len([c for c in hybrid_system['components'].values() if c is not None and c is not True])}")
        self.logger.info(f"   Accuracy Preservation: {hybrid_system['accuracy_preservation']}")
        
        return hybrid_system
    
    def get_integration_status(self) -> Dict[str, Any]:
        """Get detailed integration status"""
        
        return {
            'base_system_integration': {
                'success': self.integration_result.success,
                'integration_level': self.integration_result.base_system_status.integration_level,
                'components_found': len(self.integration_result.base_system_status.components_found),
                'components_missing': len(self.integration_result.base_system_status.missing_components),
                'accuracy_preservation': self.integration_result.base_system_status.accuracy_preservation,
                'fallback_active': self.integration_result.fallback_active,
                'performance_impact': self.integration_result.performance_impact
            },
            'available_components': list(self.integrated_components.keys()),
            'missing_components': self.integration_result.base_system_status.missing_components,
            'recommendations': self._get_integration_recommendations()
        }
    
    def _get_integration_recommendations(self) -> List[str]:
        """Get recommendations for improving integration"""
        
        recommendations = []
        
        if not self.integration_result.success:
            recommendations.append("Install base system components in /src for optimal performance")
        
        if self.integration_result.base_system_status.integration_level == "partial":
            recommendations.append("Some base components missing - consider full base system installation")
        
        if self.integration_result.fallback_active:
            recommendations.append("Operating in fallback mode - performance may be reduced")
        
        missing_critical = [
            comp for comp in self.integration_result.base_system_status.missing_components
            if comp in ['theoretical_framework_integration', 'fractal_hawkes_component', 'rg_graphs_component']
        ]
        
        if missing_critical:
            recommendations.append(f"Critical components missing: {', '.join(missing_critical)}")
        
        if not recommendations:
            recommendations.append("Integration optimal - no improvements needed")
        
        return recommendations


def create_base_system_integrator() -> BaseSystemIntegrator:
    """Factory function to create base system integrator"""
    return BaseSystemIntegrator()


if __name__ == "__main__":
    """Test base system integration"""
    
    print("🔗 BASE SYSTEM INTEGRATION: Testing & Validation")
    print("=" * 60)
    
    # Create integrator
    integrator = create_base_system_integrator()
    
    # Get integration status
    status = integrator.get_integration_status()
    
    print(f"\n📊 Integration Status:")
    print(f"   Success: {status['base_system_integration']['success']}")
    print(f"   Integration Level: {status['base_system_integration']['integration_level']}")
    print(f"   Components Found: {status['base_system_integration']['components_found']}")
    print(f"   Components Missing: {status['base_system_integration']['components_missing']}")
    print(f"   Accuracy Preservation: {status['base_system_integration']['accuracy_preservation']}")
    
    print(f"\n🔧 Available Components:")
    for component in status['available_components']:
        print(f"   ✅ {component}")
    
    if status['missing_components']:
        print(f"\n❌ Missing Components:")
        for component in status['missing_components']:
            print(f"   - {component}")
    
    print(f"\n💡 Recommendations:")
    for rec in status['recommendations']:
        print(f"   • {rec}")
    
    # Test hybrid system creation
    print(f"\n🔗 Testing Hybrid System Creation:")
    hybrid_system = integrator.create_hybrid_prediction_system()
    
    print(f"   Integration Level: {hybrid_system['integration_level']}")
    print(f"   Accuracy Preservation: {hybrid_system['accuracy_preservation']}")
    print(f"   Available Components: {len(hybrid_system['components'])}")
    
    for name, component in hybrid_system['components'].items():
        status_icon = "✅" if component is not None else "❌"
        print(f"   {status_icon} {name}")
    
    print(f"\n✅ Base System Integration validation complete")
    print(f"🎯 Ready for Project Oracle enhanced integration")
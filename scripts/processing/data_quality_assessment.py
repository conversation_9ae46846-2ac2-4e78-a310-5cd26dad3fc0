#!/usr/bin/env python3
"""
Data Quality and Processing Assessment
======================================

Comprehensive evaluation of data pipeline robustness:
- Data pipeline consistency between components
- Session processing without cross-contamination
- Timestamp handling and conversion accuracy
- Event classification and interpretation validation
"""

import sys
import json
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add current directory to path
sys.path.append('.')

# Import Oracle components
from src.ironpulse.compartments.predict import PredictCompartment

class DataQualityAssessment:
    """Comprehensive data quality evaluation"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'data_pipeline_robustness': {},
            'session_processing': {},
            'timestamp_handling': {},
            'event_classification': {},
            'recommendations': []
        }
    
    def assess_data_pipeline_robustness(self) -> Dict[str, Any]:
        """Ensure consistent data formats between all components"""
        print("🔧 ASSESSING DATA PIPELINE ROBUSTNESS")
        print("=" * 60)
        
        pipeline_results = {}
        
        try:
            # Check enhanced sessions format consistency
            enhanced_dir = Path("enhanced_sessions")
            if enhanced_dir.exists():
                enhanced_files = list(enhanced_dir.glob("*.json"))
                
                format_consistency = []
                required_fields = ['session_id', 'session_type', 'price_movements', 'micro_timing_analysis']
                
                for file_path in enhanced_files[:10]:  # Sample first 10 files
                    try:
                        with open(file_path, 'r') as f:
                            session_data = json.load(f)
                        
                        # Check required fields
                        has_required_fields = all(field in session_data for field in required_fields)
                        format_consistency.append(has_required_fields)
                        
                    except Exception as e:
                        format_consistency.append(False)
                        print(f"   ⚠️ Format issue in {file_path.name}: {e}")
                
                # Check Grammar Bridge format
                grammar_file = Path("grammar_bridge/cascade_events.json")
                grammar_format_valid = False
                if grammar_file.exists():
                    try:
                        with open(grammar_file, 'r') as f:
                            grammar_data = json.load(f)
                        
                        required_grammar_fields = ['unified_cascade_events']
                        grammar_format_valid = all(field in grammar_data for field in required_grammar_fields)
                        
                        # Check event format
                        events = grammar_data.get('unified_cascade_events', [])
                        if events:
                            sample_event = events[0]
                            event_required_fields = ['session_id', 'timestamp_minutes', 'event_type']
                            event_format_valid = all(field in sample_event for field in event_required_fields)
                            grammar_format_valid = grammar_format_valid and event_format_valid
                            
                    except Exception as e:
                        print(f"   ⚠️ Grammar Bridge format issue: {e}")
                
                pipeline_results = {
                    'enhanced_sessions': {
                        'total_files': len(enhanced_files),
                        'format_consistency_rate': sum(format_consistency) / len(format_consistency) if format_consistency else 0,
                        'all_consistent': all(format_consistency) if format_consistency else False
                    },
                    'grammar_bridge': {
                        'format_valid': grammar_format_valid,
                        'file_exists': grammar_file.exists()
                    },
                    'overall_robustness': all(format_consistency) and grammar_format_valid if format_consistency else False
                }
                
                consistency_rate = pipeline_results['enhanced_sessions']['format_consistency_rate']
                print(f"   ✅ Enhanced Sessions: {consistency_rate:.1%} format consistency")
                print(f"   ✅ Grammar Bridge: {'Valid' if grammar_format_valid else 'Invalid'} format")
                
            else:
                pipeline_results = {'status': 'no_enhanced_sessions', 'overall_robustness': False}
                print(f"   ❌ Enhanced sessions directory not found")
                
        except Exception as e:
            pipeline_results = {'status': 'error', 'error': str(e), 'overall_robustness': False}
            print(f"   ❌ Pipeline assessment error: {e}")
        
        self.results['data_pipeline_robustness'] = pipeline_results
        return pipeline_results
    
    def assess_session_processing(self) -> Dict[str, Any]:
        """Verify individual session processing without cross-contamination"""
        print("\n📊 ASSESSING SESSION PROCESSING")
        print("=" * 60)
        
        session_results = {}
        
        try:
            predict_comp = PredictCompartment()
            
            # Load Grammar Bridge data
            grammar_file = Path("grammar_bridge/cascade_events.json")
            if grammar_file.exists():
                with open(grammar_file, 'r') as f:
                    grammar_data = json.load(f)
                
                events = grammar_data.get('unified_cascade_events', [])
                
                # Get unique sessions
                sessions = list(set(event.get('session_id', 'unknown') for event in events))
                
                # Test session isolation
                isolation_tests = []
                for session_id in sessions[:5]:  # Test first 5 sessions
                    session_events = predict_comp._load_grammar_bridge_events(f"enhanced_{session_id}")
                    
                    if session_events:
                        # Check session purity
                        unique_sessions_in_result = set(event.get('session_id', '') for event in session_events)
                        is_pure = len(unique_sessions_in_result) == 1 and list(unique_sessions_in_result)[0] == session_id
                        
                        isolation_tests.append({
                            'session_id': session_id,
                            'events_loaded': len(session_events),
                            'session_pure': is_pure,
                            'unique_sessions_found': len(unique_sessions_in_result)
                        })
                        
                        print(f"   Session {session_id}: {len(session_events)} events, Pure: {is_pure}")
                
                # Test timestamp conversion for session isolation
                timestamp_isolation_tests = []
                for test in isolation_tests:
                    if test['session_pure']:
                        session_id = test['session_id']
                        session_events = predict_comp._load_grammar_bridge_events(f"enhanced_{session_id}")
                        
                        if session_events:
                            # Test timestamp conversion
                            adapted_data = predict_comp._adapt_cascade_events_to_rg_scaler(session_events)
                            adapted_events = adapted_data.get('micro_timing_analysis', {}).get('cascade_events', [])
                            
                            if adapted_events:
                                timestamps = [e.get('timestamp_minutes', 0) for e in adapted_events]
                                starts_from_zero = min(timestamps) == 0.0
                                reasonable_duration = max(timestamps) < 300  # Less than 5 hours
                                
                                timestamp_isolation_tests.append({
                                    'session_id': session_id,
                                    'starts_from_zero': starts_from_zero,
                                    'reasonable_duration': reasonable_duration,
                                    'duration_minutes': max(timestamps) - min(timestamps)
                                })
                
                session_results = {
                    'total_sessions_tested': len(isolation_tests),
                    'session_isolation': {
                        'pure_sessions': sum(1 for t in isolation_tests if t['session_pure']),
                        'contaminated_sessions': sum(1 for t in isolation_tests if not t['session_pure']),
                        'isolation_rate': sum(1 for t in isolation_tests if t['session_pure']) / len(isolation_tests) if isolation_tests else 0
                    },
                    'timestamp_conversion': {
                        'sessions_tested': len(timestamp_isolation_tests),
                        'proper_zero_start': sum(1 for t in timestamp_isolation_tests if t['starts_from_zero']),
                        'reasonable_durations': sum(1 for t in timestamp_isolation_tests if t['reasonable_duration']),
                        'conversion_success_rate': sum(1 for t in timestamp_isolation_tests if t['starts_from_zero'] and t['reasonable_duration']) / len(timestamp_isolation_tests) if timestamp_isolation_tests else 0
                    },
                    'cross_contamination_detected': any(not t['session_pure'] for t in isolation_tests),
                    'isolation_tests': isolation_tests,
                    'timestamp_tests': timestamp_isolation_tests
                }
                
                isolation_rate = session_results['session_isolation']['isolation_rate']
                conversion_rate = session_results['timestamp_conversion']['conversion_success_rate']
                
                print(f"   ✅ Session Isolation: {isolation_rate:.1%} success rate")
                print(f"   ✅ Timestamp Conversion: {conversion_rate:.1%} success rate")
                
            else:
                session_results = {'status': 'no_grammar_data'}
                print(f"   ❌ No Grammar Bridge data available")
                
        except Exception as e:
            session_results = {'status': 'error', 'error': str(e)}
            print(f"   ❌ Session processing error: {e}")
        
        self.results['session_processing'] = session_results
        return session_results
    
    def assess_timestamp_handling(self) -> Dict[str, Any]:
        """Confirm absolute-to-relative timestamp conversion accuracy"""
        print("\n🕐 ASSESSING TIMESTAMP HANDLING")
        print("=" * 60)
        
        timestamp_results = {}
        
        try:
            predict_comp = PredictCompartment()
            
            # Load Grammar Bridge data
            grammar_file = Path("grammar_bridge/cascade_events.json")
            if grammar_file.exists():
                with open(grammar_file, 'r') as f:
                    grammar_data = json.load(f)
                
                events = grammar_data.get('unified_cascade_events', [])
                
                # Test timestamp conversion accuracy
                test_sessions = ['NYAM_Lvl-1_2025_08_04_REAL', 'NYAM_Lvl-1_2025_08_05_COMPLETE']
                conversion_tests = []
                
                for session_id in test_sessions:
                    session_events = [e for e in events if e.get('session_id') == session_id]
                    
                    if session_events:
                        # Get original timestamps
                        original_timestamps = [e.get('timestamp_minutes', 0) for e in session_events]
                        original_min = min(original_timestamps)
                        original_max = max(original_timestamps)
                        original_span = original_max - original_min
                        
                        # Test conversion
                        adapted_data = predict_comp._adapt_cascade_events_to_rg_scaler(session_events)
                        adapted_events = adapted_data.get('micro_timing_analysis', {}).get('cascade_events', [])
                        
                        if adapted_events:
                            converted_timestamps = [e.get('timestamp_minutes', 0) for e in adapted_events]
                            converted_min = min(converted_timestamps)
                            converted_max = max(converted_timestamps)
                            converted_span = converted_max - converted_min
                            
                            # Validate conversion
                            starts_at_zero = abs(converted_min - 0.0) < 0.001
                            preserves_span = abs(converted_span - original_span) < 0.001
                            preserves_order = converted_timestamps == sorted(converted_timestamps)
                            
                            conversion_tests.append({
                                'session_id': session_id,
                                'original_span': original_span,
                                'converted_span': converted_span,
                                'starts_at_zero': starts_at_zero,
                                'preserves_span': preserves_span,
                                'preserves_order': preserves_order,
                                'conversion_accurate': starts_at_zero and preserves_span and preserves_order
                            })
                            
                            print(f"   Session {session_id}: Zero start: {starts_at_zero}, Span preserved: {preserves_span}")
                
                if conversion_tests:
                    accurate_conversions = sum(1 for t in conversion_tests if t['conversion_accurate'])
                    
                    timestamp_results = {
                        'sessions_tested': len(conversion_tests),
                        'accurate_conversions': accurate_conversions,
                        'accuracy_rate': accurate_conversions / len(conversion_tests),
                        'conversion_tests': conversion_tests,
                        'all_conversions_accurate': accurate_conversions == len(conversion_tests)
                    }
                    
                    accuracy_rate = timestamp_results['accuracy_rate']
                    print(f"   ✅ Timestamp Conversion: {accuracy_rate:.1%} accuracy rate")
                    
                else:
                    timestamp_results = {'status': 'no_test_data'}
                    print(f"   ❌ No timestamp conversion tests completed")
            else:
                timestamp_results = {'status': 'no_grammar_data'}
                print(f"   ❌ No Grammar Bridge data available")
                
        except Exception as e:
            timestamp_results = {'status': 'error', 'error': str(e)}
            print(f"   ❌ Timestamp handling error: {e}")
        
        self.results['timestamp_handling'] = timestamp_results
        return timestamp_results

    def assess_event_classification(self) -> Dict[str, Any]:
        """Validate Grammar Bridge event type interpretation"""
        print("\n🏷️ ASSESSING EVENT CLASSIFICATION")
        print("=" * 60)

        classification_results = {}

        try:
            predict_comp = PredictCompartment()

            # Load Grammar Bridge data
            grammar_file = Path("grammar_bridge/cascade_events.json")
            if grammar_file.exists():
                with open(grammar_file, 'r') as f:
                    grammar_data = json.load(f)

                events = grammar_data.get('unified_cascade_events', [])

                # Analyze event type distribution and quality
                event_types = [e.get('event_type', 'unknown') for e in events]
                event_type_counts = {}
                for event_type in event_types:
                    event_type_counts[event_type] = event_type_counts.get(event_type, 0) + 1

                # Test pattern recognition with different event types
                high_value_event_types = [
                    'liquidity_sweep', 'fpfvg_formation', 'expansion_high',
                    'reversal_point_expansion_higher', 'session_low_liquidity_sweep'
                ]

                high_value_events = [e for e in events if e.get('event_type', '') in high_value_event_types]

                # Test pattern recognition quality
                pattern_recognition_tests = []
                sample_sizes = [25, 50, 100]

                for size in sample_sizes:
                    if len(events) >= size:
                        sample_events = events[:size]
                        pattern_analysis = predict_comp._analyze_grammar_patterns(sample_events)

                        pattern_recognition_tests.append({
                            'sample_size': size,
                            'completion_probability': float(pattern_analysis.get('completion_probability', 0)),
                            'pattern_type': pattern_analysis.get('pattern_type', 'unknown'),
                            'high_value_events_in_sample': len([e for e in sample_events if e.get('event_type', '') in high_value_event_types])
                        })

                classification_results = {
                    'total_events': len(events),
                    'unique_event_types': len(event_type_counts),
                    'event_type_distribution': dict(sorted(event_type_counts.items(), key=lambda x: x[1], reverse=True)[:15]),
                    'high_value_events': {
                        'count': len(high_value_events),
                        'percentage': len(high_value_events) / len(events) if events else 0,
                        'types_present': len(set(e.get('event_type', '') for e in high_value_events))
                    },
                    'pattern_recognition_quality': {
                        'tests_completed': len(pattern_recognition_tests),
                        'average_completion': sum(t['completion_probability'] for t in pattern_recognition_tests) / len(pattern_recognition_tests) if pattern_recognition_tests else 0,
                        'pattern_diversity': len(set(t['pattern_type'] for t in pattern_recognition_tests)),
                        'test_results': pattern_recognition_tests
                    },
                    'classification_quality': 'excellent' if len(event_type_counts) > 50 else 'good' if len(event_type_counts) > 20 else 'limited'
                }

                unique_types = classification_results['unique_event_types']
                high_value_pct = classification_results['high_value_events']['percentage']
                avg_completion = classification_results['pattern_recognition_quality']['average_completion']

                print(f"   ✅ Event Types: {unique_types} unique types")
                print(f"   ✅ High-Value Events: {high_value_pct:.1%} of total")
                print(f"   ✅ Pattern Recognition: {avg_completion:.3f} avg completion")

            else:
                classification_results = {'status': 'no_grammar_data'}
                print(f"   ❌ No Grammar Bridge data available")

        except Exception as e:
            classification_results = {'status': 'error', 'error': str(e)}
            print(f"   ❌ Event classification error: {e}")

        self.results['event_classification'] = classification_results
        return classification_results

    def generate_data_quality_recommendations(self) -> List[str]:
        """Generate data quality recommendations"""
        recommendations = []

        # Pipeline robustness
        pipeline = self.results.get('data_pipeline_robustness', {})
        if not pipeline.get('overall_robustness', False):
            recommendations.append("🔧 CRITICAL: Fix data pipeline format inconsistencies")

        # Session processing
        session = self.results.get('session_processing', {})
        if session.get('cross_contamination_detected', False):
            recommendations.append("📊 HIGH: Eliminate cross-session contamination")

        isolation_rate = session.get('session_isolation', {}).get('isolation_rate', 0)
        if isolation_rate < 0.9:
            recommendations.append("📊 HIGH: Improve session isolation (< 90% success)")

        # Timestamp handling
        timestamp = self.results.get('timestamp_handling', {})
        if not timestamp.get('all_conversions_accurate', False):
            recommendations.append("🕐 HIGH: Fix timestamp conversion accuracy issues")

        # Event classification
        classification = self.results.get('event_classification', {})
        if classification.get('classification_quality') == 'limited':
            recommendations.append("🏷️ MEDIUM: Expand event type diversity for better classification")

        high_value_pct = classification.get('high_value_events', {}).get('percentage', 0)
        if high_value_pct < 0.1:
            recommendations.append("🏷️ MEDIUM: Increase high-value event detection rate")

        # Overall data quality
        if not recommendations:
            recommendations.append("✅ DATA_QUALITY: All data processing components are robust and accurate")

        self.results['recommendations'] = recommendations
        return recommendations

    def run_complete_assessment(self) -> Dict[str, Any]:
        """Run complete data quality assessment"""
        print("📊 DATA QUALITY AND PROCESSING ASSESSMENT")
        print("=" * 80)
        print(f"Timestamp: {self.results['timestamp']}")

        # Run all assessment phases
        self.assess_data_pipeline_robustness()
        self.assess_session_processing()
        self.assess_timestamp_handling()
        self.assess_event_classification()

        # Generate recommendations
        recommendations = self.generate_data_quality_recommendations()

        # Summary
        print("\n📋 DATA QUALITY ASSESSMENT SUMMARY")
        print("=" * 40)

        for rec in recommendations:
            print(f"   {rec}")

        # Overall data quality status
        critical_data_issues = [r for r in recommendations if 'CRITICAL' in r]
        high_data_issues = [r for r in recommendations if 'HIGH' in r]

        if not critical_data_issues and not high_data_issues:
            data_status = "PRODUCTION_READY"
        elif not critical_data_issues:
            data_status = "MINOR_DATA_ISSUES"
        else:
            data_status = "NEEDS_DATA_FIXES"

        self.results['overall_data_status'] = data_status

        print(f"\n🎯 OVERALL DATA STATUS: {data_status}")

        return self.results

if __name__ == "__main__":
    assessor = DataQualityAssessment()
    results = assessor.run_complete_assessment()

    # Save results (with JSON serialization fix)
    def convert_numpy_types(obj):
        if isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return obj

    # Convert numpy types recursively
    def clean_for_json(data):
        if isinstance(data, dict):
            return {k: clean_for_json(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [clean_for_json(item) for item in data]
        else:
            return convert_numpy_types(data)

    clean_results = clean_for_json(results)

    with open('data_quality_assessment.json', 'w') as f:
        json.dump(clean_results, f, indent=2)

    print(f"\n💾 Results saved to data_quality_assessment.json")

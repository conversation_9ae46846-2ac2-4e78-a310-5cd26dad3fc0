"""
Cascade Unit Definition and Formalization

This script defines the concept of a "Cascade Unit" as a mathematical primitive,
converting the output of the CascadeClassificationSystem into a formal tensor-based
representation as outlined in the strategic analysis.

A Cascade Unit is defined by:
- Trigger Vector: The initial conditions or event that starts the cascade.
- Propagation Matrix: How the cascade evolves across time and features.
- Resolution State: The terminal state or outcome of the cascade.
"""

import numpy as np
from dataclasses import dataclass
from typing import List, Dict, Any

# Import existing cascade classification system
from core_predictor.cascade_classifier import CascadeClassificationSystem, CascadeSequence, CascadeEvent, CascadeType

@dataclass
class CascadeUnit:
    """
    A formal mathematical representation of a cascade sequence.
    """
    sequence_id: str
    cascade_type: str
    trigger_vector: np.ndarray
    propagation_matrix: np.ndarray
    resolution_state: np.ndarray
    
    def __repr__(self):
        return (
            f"CascadeUnit(sequence_id='{self.sequence_id}', type='{self.cascade_type}')\n"
            f"  Trigger Vector (shape): {self.trigger_vector.shape}\n"
            f"  Propagation Matrix (shape): {self.propagation_matrix.shape}\n"
            f"  Resolution State (shape): {self.resolution_state.shape}"
        )

def formalize_cascade_sequence(sequence: CascadeSequence) -> CascadeUnit:
    """
    Converts a CascadeSequence object into a formal CascadeUnit with a tensor representation.

    Args:
        sequence: A CascadeSequence object from the CascadeClassificationSystem.

    Returns:
        A CascadeUnit with formalized mathematical structures.
    """
    num_events = len(sequence.events)
    
    # Define the feature space for each event (timestamp, price, magnitude)
    feature_dim = 3 

    # 1. Trigger Vector: The first event in the sequence
    trigger_event = sequence.events[0]
    trigger_vector = np.array([
        trigger_event.price_level,
        trigger_event.magnitude,
        trigger_event.cascade_type.value # Using enum value might need encoding
    ])

    # 2. Propagation Matrix: Represents the evolution of the cascade
    #    Rows: Events in the sequence
    #    Cols: Features (price_change, magnitude_change, time_delta_minutes)
    propagation_matrix = np.zeros((num_events - 1, feature_dim))
    
    for i in range(1, num_events):
        prev_event = sequence.events[i-1]
        curr_event = sequence.events[i]
        
        time_delta = (curr_event.timestamp - prev_event.timestamp).total_seconds() / 60.0
        price_delta = curr_event.price_level - prev_event.price_level
        magnitude_delta = curr_event.magnitude - prev_event.magnitude
        
        propagation_matrix[i-1, :] = [price_delta, magnitude_delta, time_delta]

    # 3. Resolution State: The final event in the sequence
    resolution_event = sequence.events[-1]
    resolution_state = np.array([
        resolution_event.price_level,
        resolution_event.magnitude,
        resolution_event.cascade_type.value
    ])

    return CascadeUnit(
        sequence_id=sequence.sequence_id,
        cascade_type=sequence.sequence_type,
        trigger_vector=trigger_vector,
        propagation_matrix=propagation_matrix,
        resolution_state=resolution_state
    )

def demonstrate_cascade_unit_formalization():
    """
    Demonstrates the process of converting classified cascades into formal Cascade Units.
    """
    print("🎯 Cascade Unit Formalization Demonstration")
    print("=" * 70)

    # 1. Get classified sequences from the existing system
    #    (Using the demonstration function from cascade_classifier.py)
    classifier = CascadeClassificationSystem()
    
    # Create sample cascade events for demonstration
    sample_events_data = [
        {'timestamp': '09:37:00', 'price_level': 23341.75, 'movement_type': 'primer_setup'},
        {'timestamp': '09:55:00', 'price_level': 23320.25, 'movement_type': 'standard_cascade_triggered'},
        {'timestamp': '10:18:00', 'price_level': 23292.50, 'movement_type': 'major_cascade'}
    ]
    
    # Convert timestamps to datetime objects for sequence detection
    from datetime import datetime
    classified_events = []
    for event_data in sample_events_data:
        event = classifier.classify_cascade_event(event_data)
        # This is a hack for demo as the original classifier expects strings
        event.timestamp = datetime.strptime(event_data['timestamp'], '%H:%M:%S')
        classified_events.append(event)

    sequences = classifier.detect_cascade_sequences(classified_events)
    
    if not sequences:
        print("No sequences detected in sample data.")
        return

    print(f"Found {len(sequences)} cascade sequence(s) to formalize.")
    
    # 2. Formalize each sequence into a CascadeUnit
    formalized_units = []
    for seq in sequences:
        print(f"\nFormalizing sequence: {seq.sequence_id} ({seq.sequence_type})")
        
        # Hack to ensure timestamps are datetime objects for timedelta
        for event in seq.events:
            if isinstance(event.timestamp, str):
                 event.timestamp = datetime.strptime(event.timestamp, '%H:%M:%S')

        unit = formalize_cascade_sequence(seq)
        formalized_units.append(unit)
        print(unit)

    print(f"\n🎉 Successfully formalized {len(formalized_units)} cascade sequences into Cascade Units.")
    return formalized_units

if __name__ == "__main__":
    demonstrate_cascade_unit_formalization()

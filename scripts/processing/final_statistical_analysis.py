#!/usr/bin/env python3
"""
Final Statistical Analysis: Enhanced Dataset with N=58 Sessions
Comprehensive pattern confidence analysis on dual-layer enhanced sessions.
"""

import json
import os
import glob
from pathlib import Path
from typing import Dict, List, Any, Tuple
import numpy as np
from collections import defaultdict, Counter
import math

class EnhancedStatisticalAnalyzer:
    """Comprehensive statistical analysis for N=58 enhanced sessions."""
    
    def __init__(self):
        """Initialize analyzer."""
        self.enhanced_sessions = []
        self.pattern_counts = Counter()
        self.session_type_counts = Counter()
        self.event_classifications = Counter()
        
    def load_enhanced_sessions(self, directory: str) -> int:
        """Load all enhanced sessions from batch processing."""
        enhanced_files = []
        
        # Load from both subdirectories
        for subdir in ['2025_07', '2025_08']:
            subpath = Path(directory) / subdir
            if subpath.exists():
                enhanced_files.extend(glob.glob(str(subpath / "enhanced_*.json")))
        
        print(f"📁 Found {len(enhanced_files)} enhanced session files")
        
        loaded_count = 0
        for file_path in enhanced_files:
            try:
                with open(file_path, 'r') as f:
                    session_data = json.load(f)
                    self.enhanced_sessions.append({
                        'file_path': file_path,
                        'data': session_data
                    })
                    loaded_count += 1
                    
                    # Extract session type from filename
                    filename = Path(file_path).name
                    session_type = self._extract_session_type(filename)
                    self.session_type_counts[session_type] += 1
                    
            except Exception as e:
                print(f"❌ Error loading {file_path}: {e}")
        
        print(f"✅ Successfully loaded {loaded_count} enhanced sessions")
        return loaded_count
    
    def _extract_session_type(self, filename: str) -> str:
        """Extract session type from filename."""
        filename = filename.lower().replace('enhanced_', '')
        
        if 'nyam' in filename or 'ny_am' in filename:
            return 'NYAM'
        elif 'nypm' in filename or 'ny_pm' in filename:
            return 'NYPM'
        elif 'asia' in filename:
            return 'ASIA'
        elif 'london' in filename:
            return 'LONDON'
        elif 'lunch' in filename:
            return 'LUNCH'
        elif 'midnight' in filename:
            return 'MIDNIGHT'
        elif 'premarket' in filename:
            return 'PREMARKET'
        else:
            return 'OTHER'
    
    def analyze_enhanced_patterns(self):
        """Analyze patterns in enhanced sessions."""
        print("\n🔍 ENHANCED PATTERN ANALYSIS")
        print("=" * 50)
        
        total_events = 0
        total_patterns = 0
        session_analysis = []
        
        for session in self.enhanced_sessions:
            data = session['data']
            filename = Path(session['file_path']).name
            
            # Extract grammatical intelligence if present
            if 'grammatical_intelligence' in data:
                gi = data['grammatical_intelligence']
                
                # Count events
                if 'event_classification' in gi:
                    events = gi['event_classification']
                    event_count = len(events)
                    total_events += event_count
                    
                    # Count event types
                    for event in events:
                        if isinstance(event, dict) and 'event_type' in event:
                            self.event_classifications[event['event_type']] += 1
                
                # Count patterns
                if 'pattern_analysis' in gi:
                    patterns = gi['pattern_analysis']
                    if 'detected_patterns' in patterns:
                        pattern_count = len(patterns['detected_patterns'])
                        total_patterns += pattern_count
                        
                        # Record individual patterns
                        for pattern in patterns['detected_patterns']:
                            if isinstance(pattern, dict) and 'pattern_name' in pattern:
                                self.pattern_counts[pattern['pattern_name']] += 1
            
            # Also check Level 1 data for movement types
            if 'level1_json' in data and 'price_movements' in data['level1_json']:
                movements = data['level1_json']['price_movements']
                for movement in movements:
                    if isinstance(movement, dict) and 'movement_type' in movement:
                        self.pattern_counts[movement['movement_type']] += 1
            
            session_type = self._extract_session_type(filename)
            session_analysis.append({
                'session_type': session_type,
                'filename': filename,
                'events': event_count if 'event_count' in locals() else 0,
                'patterns': pattern_count if 'pattern_count' in locals() else 0
            })
        
        print(f"📈 Total Events Classified: {total_events}")
        print(f"🔍 Total Patterns Detected: {total_patterns}")
        print(f"📊 Unique Pattern Types: {len(self.pattern_counts)}")
        
        return session_analysis
    
    def calculate_statistical_power(self, n_current: int, n_target: int = 80) -> Dict[str, float]:
        """Calculate statistical power metrics."""
        power_ratio = n_current / n_target
        ci_scaling = math.sqrt(n_target / n_current)
        
        # Statistical power calculation
        statistical_power = min(1.0, power_ratio)
        type_ii_error = max(0.0, 1.0 - statistical_power)
        
        return {
            'current_n': n_current,
            'target_n': n_target,
            'power_ratio': power_ratio,
            'statistical_power': statistical_power * 100,  # Convert to percentage
            'type_ii_error': type_ii_error * 100,
            'ci_scaling': ci_scaling,
            'production_ready': power_ratio >= 0.625  # 50/80 minimum threshold
        }
    
    def analyze_session_distribution(self):
        """Analyze session type distribution."""
        print("\n📊 SESSION TYPE DISTRIBUTION")
        print("=" * 50)
        
        total_sessions = sum(self.session_type_counts.values())
        
        for session_type, count in sorted(self.session_type_counts.items()):
            percentage = (count / total_sessions) * 100
            power_score = min(1.0, count / 7)  # Target: 7+ sessions per type
            status = "✅ Strong" if count >= 7 else "⚠️ Moderate" if count >= 5 else "❌ Weak"
            
            print(f"{session_type:12} | {count:2d} sessions | {percentage:5.1f}% | {power_score:.3f} power | {status}")
        
        print(f"\nTotal Sessions: {total_sessions}")
        return self.session_type_counts
    
    def top_patterns_analysis(self, top_n: int = 20):
        """Analyze top N patterns by frequency."""
        print(f"\n🔍 TOP {top_n} PATTERNS BY FREQUENCY")
        print("=" * 50)
        
        for pattern, count in self.pattern_counts.most_common(top_n):
            # Calculate statistical power for this pattern
            power = min(1.0, count / 15)  # Threshold: 15 occurrences for strong power
            confidence = self._calculate_pattern_confidence(count, len(self.enhanced_sessions))
            
            power_status = "🔥" if power >= 0.8 else "⚡" if power >= 0.6 else "⚠️" if power >= 0.4 else "❌"
            
            print(f"{pattern:35} | {count:3d} occurrences | {power:.3f} power | {confidence:.1f}% conf | {power_status}")
    
    def _calculate_pattern_confidence(self, pattern_count: int, total_sessions: int) -> float:
        """Calculate Wilson score confidence interval for pattern."""
        if total_sessions == 0:
            return 0.0
        
        p = pattern_count / total_sessions
        n = total_sessions
        z = 1.96  # 95% confidence
        
        # Wilson score interval
        numerator = p + (z**2)/(2*n) 
        denominator = 1 + (z**2)/n
        confidence_center = numerator / denominator
        
        return confidence_center * 100
    
    def production_readiness_assessment(self):
        """Comprehensive production readiness assessment."""
        print("\n🏆 PRODUCTION READINESS ASSESSMENT")
        print("=" * 50)
        
        n_sessions = len(self.enhanced_sessions)
        power_metrics = self.calculate_statistical_power(n_sessions)
        
        # Calculate component scores
        sample_size_score = min(40, (n_sessions / 80) * 40)  # Max 40 points
        pattern_power_score = min(30, (len([p for p, c in self.pattern_counts.items() if c >= 10]) / 50) * 30)
        overall_power_score = min(20, (power_metrics['statistical_power'] / 100) * 20)
        session_balance_score = min(10, (len([s for s, c in self.session_type_counts.items() if c >= 5]) / 7) * 10)
        
        total_score = sample_size_score + pattern_power_score + overall_power_score + session_balance_score
        
        print(f"📊 COMPONENT SCORES:")
        print(f"   Sample Size (N={n_sessions}):     {sample_size_score:5.1f}/40")
        print(f"   Pattern Power:              {pattern_power_score:5.1f}/30") 
        print(f"   Statistical Power:          {overall_power_score:5.1f}/20")
        print(f"   Session Balance:            {session_balance_score:5.1f}/10")
        print(f"   ────────────────────────────────────")
        print(f"   TOTAL SCORE:               {total_score:6.1f}/100")
        
        # Determine recommendation
        if total_score >= 80:
            recommendation = "✅ PRODUCTION READY"
            color = "🟢"
        elif total_score >= 60:
            recommendation = "⚠️ CONDITIONAL DEPLOYMENT"
            color = "🟡"
        elif total_score >= 40:
            recommendation = "🔄 DEVELOPMENT READY"
            color = "🟠"
        else:
            recommendation = "❌ NOT READY"
            color = "🔴"
        
        print(f"\n{color} RECOMMENDATION: {recommendation}")
        
        # Statistical summary
        print(f"\n📈 STATISTICAL SUMMARY:")
        print(f"   Current Dataset:           N = {n_sessions}")
        print(f"   Statistical Power:         {power_metrics['statistical_power']:.1f}%")
        print(f"   Type II Error Rate:        {power_metrics['type_ii_error']:.1f}%")
        print(f"   Confidence Scaling:        {power_metrics['ci_scaling']:.2f}x")
        print(f"   Production Threshold:      {'✅ Met' if power_metrics['production_ready'] else '❌ Not Met'}")
        
        return {
            'total_score': total_score,
            'recommendation': recommendation,
            'n_sessions': n_sessions,
            'power_metrics': power_metrics,
            'component_scores': {
                'sample_size': sample_size_score,
                'pattern_power': pattern_power_score, 
                'overall_power': overall_power_score,
                'session_balance': session_balance_score
            }
        }
    
    def generate_final_report(self) -> str:
        """Generate comprehensive final analysis report."""
        n_sessions = len(self.enhanced_sessions)
        power_metrics = self.calculate_statistical_power(n_sessions)
        
        report = f"""
# ENHANCED DUAL-LAYER SYSTEM: FINAL STATISTICAL ANALYSIS
## Dataset: N = {n_sessions} Enhanced Sessions

### 🎯 STATISTICAL POWER ACHIEVED
- **Current Sample Size**: N = {n_sessions} sessions
- **Statistical Power**: {power_metrics['statistical_power']:.1f}% (Target: ≥62.5% minimum)
- **Status**: {'✅ THRESHOLD MET' if power_metrics['production_ready'] else '❌ BELOW THRESHOLD'}

### 📊 PATTERN DETECTION RESULTS  
- **Total Unique Patterns**: {len(self.pattern_counts)}
- **High-Frequency Patterns**: {len([p for p, c in self.pattern_counts.items() if c >= 10])}
- **Event Classifications**: {sum(self.event_classifications.values())}

### 🔍 SESSION TYPE COVERAGE
{self._format_session_distribution()}

### 🏆 PRODUCTION RECOMMENDATION
Based on statistical power analysis (N={n_sessions} vs target N=50-80):
- **Minimum Viable**: {'✅ Achieved' if n_sessions >= 50 else '❌ Not Achieved'} (N≥50)
- **Production Grade**: {'✅ Achieved' if n_sessions >= 80 else f'⚠️ Approaching (N={n_sessions}/80)'}

### 📈 NEXT STEPS
{self._generate_next_steps(n_sessions)}
"""
        return report.strip()
    
    def _format_session_distribution(self) -> str:
        """Format session distribution for report."""
        lines = []
        for session_type, count in sorted(self.session_type_counts.items()):
            status = "✅" if count >= 7 else "⚠️" if count >= 5 else "❌"
            lines.append(f"- {session_type}: {count} sessions {status}")
        return '\n'.join(lines)
    
    def _generate_next_steps(self, n_sessions: int) -> str:
        """Generate next steps based on current status."""
        if n_sessions >= 80:
            return "🚀 READY FOR PRODUCTION DEPLOYMENT with full statistical confidence"
        elif n_sessions >= 50:
            return "✅ MINIMUM THRESHOLD MET - Consider limited production deployment with monitoring"
        else:
            needed = 50 - n_sessions
            return f"🔄 EXPAND DATASET - Process {needed}+ additional sessions to reach minimum threshold"

def main():
    """Run comprehensive statistical analysis."""
    print("🔬 ENHANCED DUAL-LAYER STATISTICAL ANALYSIS")
    print("=" * 60)
    
    analyzer = EnhancedStatisticalAnalyzer()
    
    # Load enhanced sessions
    sessions_loaded = analyzer.load_enhanced_sessions("enhanced_sessions_batch")
    
    if sessions_loaded == 0:
        print("❌ No enhanced sessions found. Run dual-layer processor first.")
        return
    
    # Run comprehensive analysis
    analyzer.analyze_enhanced_patterns()
    analyzer.analyze_session_distribution() 
    analyzer.top_patterns_analysis()
    assessment = analyzer.production_readiness_assessment()
    
    # Generate and save final report
    final_report = analyzer.generate_final_report()
    
    report_path = "enhanced_statistical_analysis_final.md"
    with open(report_path, 'w') as f:
        f.write(final_report)
    
    print(f"\n💾 Final analysis saved to: {report_path}")
    
    return assessment

if __name__ == "__main__":
    main()
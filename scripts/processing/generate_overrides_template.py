#!/usr/bin/env python3
"""
Generate overrides_template.json from data_manifest.json

- Finds items with label == "unknown"
- Groups them by session_type and session_date for fast curation
- Produces an overrides map with empty values you can fill with 'cascade' or 'non_cascade'

Usage:
  python generate_overrides_template.py \
    --manifest data_manifest.json \
    --output overrides_template.json
"""
from __future__ import annotations
import argparse
import json
from collections import defaultdict
from pathlib import Path
from typing import Dict, Any, List


def load_json(path: Path) -> Dict[str, Any]:
    with path.open("r") as f:
        return json.load(f)


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--manifest", default="data_manifest.json", help="Path to data_manifest.json")
    ap.add_argument("--output", default="overrides_template.json", help="Path to write template JSON")
    args = ap.parse_args()

    manifest_path = Path(args.manifest)
    if not manifest_path.exists():
        raise SystemExit(f"Manifest not found: {manifest_path}")

    manifest = load_json(manifest_path)
    items: List[Dict[str, Any]] = manifest.get("items", [])

    groups: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
    for it in items:
        if it.get("label") != "unknown":
            continue
        st = it.get("session_type") or "unknown_type"
        sd = it.get("session_date") or "unknown_date"
        group_key = f"session_type={st}|date={sd}"
        groups[group_key].append(it)

    # Build overrides with empty placeholders. Prefer stem; fall back to filename, then path if collision.
    overrides: Dict[str, str] = {}
    used_keys = set()

    def reserve_key(candidates: List[str]) -> str:
        for c in candidates:
            if c and c not in used_keys:
                used_keys.add(c)
                return c
        # Last resort: append index
        base = candidates[-1] or candidates[0]
        idx = 1
        key = f"{base}_{idx}"
        while key in used_keys:
            idx += 1
            key = f"{base}_{idx}"
        used_keys.add(key)
        return key

    for group_key, group_items in groups.items():
        for it in group_items:
            path = it.get("path") or ""
            name = Path(path).name if path else ""
            stem = Path(path).stem if path else ""
            key = reserve_key([stem, name, path])
            overrides[key] = ""  # fill with 'cascade' or 'non_cascade'

    output = {
        "_meta": {
            "instruction": "Fill values with 'cascade' or 'non_cascade'. Keys can be filename stem, filename, or path.",
            "unknown_groups": {k: len(v) for k, v in groups.items()},
            "total_unknown": sum(len(v) for v in groups.values()),
        },
        "groups": groups,  # for reference/context
        "overrides": overrides
    }

    with open(args.output, "w") as f:
        json.dump(output, f, indent=2)

    print(f"Wrote overrides template to {args.output}")


if __name__ == "__main__":
    main()


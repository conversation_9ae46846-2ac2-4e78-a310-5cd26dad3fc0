#!/bin/bash
# Project Oracle v1.0 - Permanent OpenMP Configuration
# Configures MacPorts OpenMP for XGBoost multi-threading support

echo "🔧 CONFIGURING PERMANENT OPENMP ENVIRONMENT"
echo "=" * 50

# Check if ~/.zshrc exists
if [ ! -f ~/.zshrc ]; then
    echo "Creating ~/.zshrc..."
    touch ~/.zshrc
fi

# Add OpenMP configuration
echo "" >> ~/.zshrc
echo "# Project Oracle v1.0 - OpenMP Configuration" >> ~/.zshrc
echo "export DYLD_LIBRARY_PATH=/opt/local/lib/libomp:\$DYLD_LIBRARY_PATH" >> ~/.zshrc
echo "export OMP_NUM_THREADS=4" >> ~/.zshrc

echo "✅ OpenMP environment variables added to ~/.zshrc"
echo "💡 Run 'source ~/.zshrc' to apply changes"

# Verify OpenMP libraries exist
echo ""
echo "🔍 Verifying OpenMP Libraries:"
if [ -f "/opt/local/lib/libomp/libomp.dylib" ]; then
    echo "   ✅ MacPorts OpenMP: /opt/local/lib/libomp/libomp.dylib"
else
    echo "   ❌ MacPorts OpenMP not found"
fi

if [ -f "/usr/local/lib/libomp.dylib" ]; then
    echo "   ✅ Homebrew OpenMP: /usr/local/lib/libomp.dylib"
else
    echo "   ⚠️ Homebrew OpenMP not found (MacPorts sufficient)"
fi

echo ""
echo "🎯 Project Oracle v1.0 OpenMP configuration complete!"
echo "   Run production validation with full multi-threading support"
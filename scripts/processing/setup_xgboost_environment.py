"""
Setup XGBoost Environment with MacPorts OpenMP
Automatically configure environment variables for XGBoost to work properly
"""

import os
import sys

def setup_xgboost_environment():
    """Configure environment for XGBoost with MacPorts OpenMP"""
    
    print("🔧 SETTING UP XGBOOST ENVIRONMENT WITH MACPORTS")
    print("=" * 60)
    
    # Set MacPorts OpenMP library path
    macports_omp_path = "/opt/local/lib/libomp"
    
    # Get current DYLD_LIBRARY_PATH
    current_path = os.environ.get('DYLD_LIBRARY_PATH', '')
    
    # Add MacPorts path if not already present
    if macports_omp_path not in current_path:
        if current_path:
            new_path = f"{macports_omp_path}:{current_path}"
        else:
            new_path = macports_omp_path
        
        os.environ['DYLD_LIBRARY_PATH'] = new_path
        print(f"✅ Updated DYLD_LIBRARY_PATH: {new_path}")
    else:
        print(f"✅ MacPorts OpenMP path already in DYLD_LIBRARY_PATH")
    
    # Set OpenMP thread count for optimal performance
    os.environ['OMP_NUM_THREADS'] = '4'
    print(f"✅ Set OMP_NUM_THREADS=4")
    
    # Test XGBoost import
    try:
        import xgboost as xgb
        print(f"✅ XGBoost {xgb.__version__} loaded successfully")
        
        # Test basic functionality
        import numpy as np
        X = np.random.random((10, 3))
        y = np.random.random(10)
        
        model = xgb.XGBRegressor(n_estimators=10, random_state=42)
        model.fit(X, y)
        predictions = model.predict(X)
        
        print(f"✅ XGBoost model training and prediction test passed")
        return True
        
    except Exception as e:
        print(f"❌ XGBoost test failed: {e}")
        return False

def create_environment_script():
    """Create a shell script to set up environment variables"""
    
    script_content = """#!/bin/bash
# XGBoost Environment Setup for MacPorts OpenMP
export DYLD_LIBRARY_PATH=/opt/local/lib/libomp:$DYLD_LIBRARY_PATH
export OMP_NUM_THREADS=4

echo "✅ XGBoost environment configured with MacPorts OpenMP"
echo "   DYLD_LIBRARY_PATH: $DYLD_LIBRARY_PATH"
echo "   OMP_NUM_THREADS: $OMP_NUM_THREADS"
"""
    
    script_path = "/Users/<USER>/grok-claude-automation/project_oracle/setup_xgboost_env.sh"
    
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    # Make script executable
    os.chmod(script_path, 0o755)
    
    print(f"📝 Environment script created: {script_path}")
    print(f"   Usage: source {script_path}")
    
    return script_path

if __name__ == "__main__":
    print("🚀 XGBOOST MACPORTS SETUP")
    
    # Setup environment in current process
    success = setup_xgboost_environment()
    
    # Create permanent script
    script_path = create_environment_script()
    
    if success:
        print(f"\n🎉 XGBOOST SETUP COMPLETE!")
        print(f"   ✅ XGBoost is now working with MacPorts OpenMP")
        print(f"   ✅ Environment variables configured")
        print(f"   ✅ Test model training successful")
        
        print(f"\n💡 TO USE IN FUTURE SESSIONS:")
        print(f"   source {script_path}")
        print(f"   python3 your_oracle_script.py")
        
    else:
        print(f"\n❌ SETUP FAILED")
        print(f"   Check MacPorts installation: port installed libomp")
        print(f"   Verify path: ls -la /opt/local/lib/libomp/")
#!/usr/bin/env python3
"""
Audit Data Integrity for Project Oracle

- Enumerate session JSONs across sources
- Validate against schema/dual_layer_schema.json when present (enhanced sessions)
- Heuristically validate Level-1 JSONs (required keys)
- Deduplicate using content hash
- Infer label (cascade vs non-cascade) with simple heuristics or embedded metadata
- Produce manifest (JSON) and Markdown report with counts and class balance

Usage:
  python audit_data_integrity.py \
    --sources data/sessions enhanced_sessions enhanced_sessions_batch . \
    --output data_manifest.json \
    --report data_audit_report.md

Notes:
- Keeps implementation dependency-light (no jsonschema). For full validation, integrate jsonschema later.
- Label inference is conservative: only marks cascade/non-cascade when clear; else unknown.
"""

from __future__ import annotations
import argparse
import json
import os
from pathlib import Path
import hashlib
from typing import Dict, List, Optional, Tuple

SCHEMA_PATH = Path("schema/dual_layer_schema.json")
GRAMMAR_PATH = Path("schema/grammatical_events.json")

# Required top-level keys for Level-1 JSONs (non-enhanced)
LEVEL1_REQUIRED_KEYS = [
    "session_metadata",
    "session_fpfvg",
]

# Keys to check inside enhanced dual-layer
ENHANCED_REQUIRED_KEYS = ["level1_json", "grammatical_intelligence"]


def sha256_of_file(p: Path) -> str:
    h = hashlib.sha256()
    with p.open("rb") as f:
        for chunk in iter(lambda: f.read(8192), b""):
            h.update(chunk)
    return h.hexdigest()


def is_json_file(p: Path) -> bool:
    return p.suffix.lower() == ".json" and p.is_file()


def load_json(p: Path) -> Optional[dict]:
    try:
        with p.open("r") as f:
            return json.load(f)
    except Exception:
        return None


def classify_file(raw: dict) -> str:
    """Classify file type: enhanced|level1|unknown"""
    if isinstance(raw, dict):
        if all(k in raw for k in ENHANCED_REQUIRED_KEYS):
            return "enhanced"
        if all(k in raw for k in LEVEL1_REQUIRED_KEYS):
            return "level1"
    return "unknown"


def validate_level1(raw: dict) -> List[str]:
    errs = []
    for k in LEVEL1_REQUIRED_KEYS:
        if k not in raw:
            errs.append(f"missing_key:{k}")
    # Spot-check metadata
    meta = raw.get("session_metadata", {}) if isinstance(raw, dict) else {}
    if not isinstance(meta, dict):
        errs.append("session_metadata_not_object")
    else:
        for mk in ["session_type", "session_date"]:
            if mk not in meta:
                errs.append(f"metadata_missing:{mk}")
    return errs


def validate_enhanced(raw: dict) -> List[str]:
    errs = []
    for k in ENHANCED_REQUIRED_KEYS:
        if k not in raw:
            errs.append(f"missing_key:{k}")
    # Spot-check nested requireds
    lvl1 = raw.get("level1_json", {})
    if not isinstance(lvl1, dict):
        errs.append("level1_json_not_object")
    else:
        meta = lvl1.get("session_metadata", {})
        if not isinstance(meta, dict):
            errs.append("session_metadata_not_object")
        else:
            for mk in ["session_type", "session_date"]:
                if mk not in meta:
                    errs.append(f"metadata_missing:{mk}")
    gi = raw.get("grammatical_intelligence", {})
    if not isinstance(gi, dict):
        errs.append("grammatical_intelligence_not_object")
    return errs


def _build_overrides_map(raw: Dict[str, str]) -> Dict[str, str]:
    """Normalize override keys to lowercase; values to 'cascade'/'non_cascade' only."""
    out: Dict[str, str] = {}
    for k, v in (raw or {}).items():
        if not isinstance(k, str):
            continue
        if isinstance(v, str):
            vv = v.strip().lower()
            if vv in ("cascade", "non_cascade"):
                out[k.strip().lower()] = vv
    return out


def _match_override_label(p: Path, overrides: Dict[str, str]) -> Optional[str]:
    candidates = [str(p), p.name, p.stem]
    for c in candidates:
        key = c.strip().lower()
        if key in overrides:
            return overrides[key]
    return None


def _filename_hint_non_cascade(p: Path) -> bool:
    s = str(p).lower()
    tokens = [
        "non_cascade", "noncascade", "no_cascade", "nocascade",
        "non-event", "non_event", "negative"
    ]
    return any(t in s for t in tokens)


def infer_label(raw: dict, ftype: str) -> str:
    """Enhanced cascade label inference with intelligent heuristics for non-cascade detection."""
    try:
        if ftype == "enhanced":
            gi = raw.get("grammatical_intelligence", {})
            patterns = gi.get("pattern_analysis", {}).get("active_patterns", [])
            # If any pattern has cascade_probability >= 0.8, mark cascade (heuristic)
            for ptn in patterns if isinstance(patterns, list) else []:
                prob = ptn.get("cascade_probability")
                if isinstance(prob, (int, float)) and prob >= 0.8:
                    return "cascade"
            # Look into event_classification for clear TAKEOUT/REDELIVERY sequences
            evts = gi.get("event_classification", [])
            ev_types = [e.get("event_type") for e in evts if isinstance(e, dict)]
            if "TAKEOUT" in ev_types or "LIQUIDITY_SWEEP" in ev_types:
                return "cascade"
            # If none of the above and explicit flag present
            tag = gi.get("cascade_label")
            if tag in ("cascade", "non_cascade"):
                return tag

            # Enhanced non-cascade detection for enhanced files
            level1_data = raw.get("level1_json", {})
            return _infer_from_level1_data(level1_data)

        elif ftype == "level1":
            # Enhanced Level-1 analysis
            return _infer_from_level1_data(raw)
    except Exception:
        pass
    return "unknown"


def _infer_from_level1_data(raw: dict) -> str:
    """Enhanced Level-1 data analysis for cascade/non-cascade detection."""
    try:
        # Check for cascade events first
        cascade_events = raw.get("micro_timing_analysis", {}).get("cascade_events", [])
        if cascade_events and len(cascade_events) > 0:
            # Verify high-confidence cascade indicators
            for event in cascade_events:
                event_type = event.get("event_type", "")
                confidence = event.get("confidence", 0)
                if "cascade" in event_type.lower() and confidence >= 3:
                    return "cascade"
            return "cascade"  # Any cascade events indicate cascade

        # Enhanced non-cascade detection
        session_metadata = raw.get("session_metadata", {})
        session_type = session_metadata.get("session_type", "").lower()

        # LUNCH sessions are typically low-activity, non-cascade periods
        if session_type in ["lunch", "lunch_break"]:
            return "non_cascade"

        # MIDNIGHT sessions with minimal activity
        if session_type == "midnight":
            price_movements = raw.get("price_movements", [])
            if len(price_movements) <= 5:  # Very few price movements
                return "non_cascade"

        # PREMARKET sessions with low activity
        if session_type in ["premarket", "pre_market"]:
            price_movements = raw.get("price_movements", [])
            if len(price_movements) <= 8:  # Low activity threshold
                return "non_cascade"

        # Check session analysis for low-activity indicators
        session_analysis = raw.get("session_analysis", {})
        institutional_activity = session_analysis.get("institutional_activity", "").lower()
        if any(indicator in institutional_activity for indicator in
               ["minimal_activity", "low_activity", "tight_range", "consolidation"]):
            return "non_cascade"

        # Check for explicit takeout events (cascade indicators)
        evs = raw.get("session_liquidity_events", [])
        ev_types = [e.get("interaction_type") or e.get("event_type") for e in evs if isinstance(e, dict)]
        if any(t in ("takeout", "TAKEOUT") for t in ev_types):
            return "cascade"

        # Additional non-cascade heuristics
        # Sessions with very tight price ranges (< 20 points)
        price_movements = raw.get("price_movements", [])
        if len(price_movements) >= 4:
            prices = [pm.get("price_level", 0) for pm in price_movements if pm.get("price_level")]
            if prices:
                price_range = max(prices) - min(prices)
                if price_range < 20:  # Very tight range
                    return "non_cascade"

    except Exception:
        pass
    return "unknown"


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--sources", nargs="*", default=["data/sessions", "enhanced_sessions", "enhanced_sessions_batch"], help="Directories to scan for session JSONs")
    ap.add_argument("--output", default="data_manifest.json", help="Path to write manifest JSON")
    ap.add_argument("--report", default="data_audit_report.md", help="Path to write Markdown report")
    ap.add_argument("--overrides", default="label_overrides.json", help="Optional path to label overrides JSON (path/name/stem → label)")
    args = ap.parse_args()

    sources = [Path(s) for s in args.sources]
    all_files: List[Path] = []
    for src in sources:
        if src.is_dir():
            for p in src.rglob("*.json"):
                all_files.append(p)
        elif src.is_file() and src.suffix == ".json":
            all_files.append(src)

    # Include root Level-1-like files (named *_Lvl-1_*.json)
    for p in Path(".").glob("*_Lvl-1_*.json"):
        all_files.append(p)

    # Deduplicate by path
    all_files = sorted(set(all_files))

    items = []
    by_hash: Dict[str, List[str]] = {}

    # Load overrides if present
    overrides_map: Dict[str, str] = {}
    if args.overrides and Path(args.overrides).exists():
        try:
            overrides_raw = load_json(Path(args.overrides))
            if isinstance(overrides_raw, dict):
                overrides_map = _build_overrides_map(overrides_raw)  # normalize keys
        except Exception:
            overrides_map = {}

    for p in all_files:
        raw = load_json(p)
        if raw is None:
            # Still record a complete item to avoid KeyErrors in summary
            h = sha256_of_file(p)
            by_hash.setdefault(h, []).append(str(p))
            items.append({
                "path": str(p),
                "hash": h,
                "file_type": "unknown",
                "valid": False,
                "errors": ["json_load_failed"],
                "label": "unknown",
                "session_type": None,
                "session_date": None,
                "provenance": "synthetic" if "SYNTHETIC" in str(p).upper() else "real_or_unknown",
            })
            continue
        ftype = classify_file(raw)
        errs = validate_enhanced(raw) if ftype == "enhanced" else (validate_level1(raw) if ftype == "level1" else ["unknown_structure"])
        # Label resolution order: explicit overrides > filename hints (if unknown) > heuristics
        label = infer_label(raw, ftype)
        override = _match_override_label(p, overrides_map)
        if override in ("cascade", "non_cascade"):
            label = override
        elif label == "unknown" and _filename_hint_non_cascade(p):
            label = "non_cascade"
        h = sha256_of_file(p)
        by_hash.setdefault(h, []).append(str(p))
        meta = None
        if ftype == "enhanced":
            meta = raw.get("level1_json", {}).get("session_metadata", {})
        elif ftype == "level1":
            meta = raw.get("session_metadata", {})
        item = {
            "path": str(p),
            "hash": h,
            "file_type": ftype,
            "valid": len(errs) == 0,
            "errors": errs,
            "label": label,
            "session_type": (meta or {}).get("session_type"),
            "session_date": (meta or {}).get("session_date"),
            "provenance": "synthetic" if "SYNTHETIC" in str(p).upper() else "real_or_unknown",
        }
        items.append(item)

    # Deduplication resolution: mark duplicates beyond first occurrence
    duplicate_groups = {h: paths for h, paths in by_hash.items() if len(paths) > 1}

    # Compute counts
    total = len(items)
    valid = sum(1 for x in items if x["valid"]) 
    enhanced = sum(1 for x in items if x["file_type"] == "enhanced")
    level1 = sum(1 for x in items if x["file_type"] == "level1")
    cascade = sum(1 for x in items if x["label"] == "cascade")
    non_cascade = sum(1 for x in items if x["label"] == "non_cascade")
    unknown = sum(1 for x in items if x["label"] == "unknown")

    manifest = {
        "generated_by": "audit_data_integrity.py",
        "sources": [str(s) for s in sources],
        "totals": {
            "files": total,
            "valid": valid,
            "enhanced": enhanced,
            "level1": level1,
            "labels": {"cascade": cascade, "non_cascade": non_cascade, "unknown": unknown},
            "duplicate_groups": len(duplicate_groups),
        },
        "duplicates": duplicate_groups,
        "items": items,
    }

    # Write outputs
    with open(args.output, "w") as f:
        json.dump(manifest, f, indent=2)

    # Markdown report
    md_lines = [
        "# Data Audit Report",
        "",
        f"Sources: {', '.join(str(s) for s in sources)}",
        "",
        f"- Total files: {total}",
        f"- Valid: {valid}",
        f"- Enhanced: {enhanced}",
        f"- Level-1: {level1}",
        f"- Labels: cascade={cascade}, non_cascade={non_cascade}, unknown={unknown}",
        f"- Duplicate groups: {len(duplicate_groups)}",
        "",
        "## Notes",
        "- Validation is lightweight; integrate jsonschema for strict checks if needed.",
        "- Label inference is conservative; unknowns require manual labeling or improved heuristics.",
    ]
    with open(args.report, "w") as f:
        f.write("\n".join(md_lines))

    print(f"Wrote manifest to {args.output}")
    print(f"Wrote report to {args.report}")


if __name__ == "__main__":
    main()


#!/bin/bash
# OpenMP Environment Setup for MacPorts + XGBoost
# Source this before running Oracle system

export DYLD_LIBRARY_PATH="/opt/local/lib/libomp:${DYLD_LIBRARY_PATH}"
export DYLD_FALLBACK_LIBRARY_PATH="/opt/local/lib/libomp:/usr/local/opt/libomp/lib:${DYLD_FALLBACK_LIBRARY_PATH}"
export OMP_NUM_THREADS=4

echo "🔗 OpenMP environment configured for MacPorts"
echo "   DYLD_LIBRARY_PATH: $DYLD_LIBRARY_PATH"
echo "   DYLD_FALLBACK_LIBRARY_PATH: $DYLD_FALLBACK_LIBRARY_PATH"
echo "   OMP_NUM_THREADS: $OMP_NUM_THREADS"

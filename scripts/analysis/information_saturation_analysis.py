#!/usr/bin/env python3
"""
Information Saturation Analysis - Find Minimum Viable Training Set
================================================================

Mathematical objective: Determine optimal training set size where marginal 
information gain I(X_n+1; Y | X_1...X_n) < 0.01 bits becomes negligible.

Test hypothesis: 45 strategically selected sessions achieve 90%+ accuracy 
in 75% of training time while preventing non-cascade overfitting.
"""

import json
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score, log_loss
from sklearn.model_selection import train_test_split
import pickle

from xgboost_real_trainer import XGBoostRealTrainer, TrainingExample

@dataclass
class SaturationPoint:
    """Information saturation analysis result"""
    n_sessions: int
    accuracy: float
    marginal_gain: float  # ∂accuracy/∂n
    information_content: float  # H(predictions)
    training_time: float
    overfitting_ratio: float
    minority_class_coverage: float

@dataclass
class OptimalSubset:
    """Strategically selected training subset"""
    session_ids: List[str]
    n_cascades: int
    n_non_cascades: int
    expected_accuracy: float
    information_preservation: float
    computational_savings: float

class InformationSaturationAnalyzer:
    """
    Analyze information saturation to find minimum viable training set
    
    Mathematical principle: Information plateaus at n ≈ 10×k where k = minority class size
    Expected saturation: n = 40-45 sessions (95% of learnable patterns)
    """
    
    def __init__(self):
        self.trainer = XGBoostRealTrainer()
        self.saturation_points = []
        self.all_sessions = None
        self.cascade_sessions = []
        self.non_cascade_sessions = []
        
        print("🔬 INFORMATION SATURATION ANALYZER")
        print("=" * 40)
        print("Objective: Find minimum viable training set")
        print("Hypothesis: n=45 sessions achieve 90%+ accuracy")
        print("Method: Learning curve + marginal information analysis")
        print()
    
    def load_and_categorize_sessions(self):
        """Load all sessions and categorize by cascade/non-cascade"""
        
        print("📁 Loading and categorizing all sessions...")
        
        # Load enhanced sessions
        sessions = self.trainer.load_enhanced_sessions()
        examples = self.trainer.extract_training_examples(sessions)
        
        self.all_sessions = examples
        
        # Categorize by outcome
        for example in examples:
            if example.cascade_occurred:
                self.cascade_sessions.append(example)
            else:
                self.non_cascade_sessions.append(example)
        
        print(f"✅ Sessions categorized:")
        print(f"   Total sessions: {len(examples)}")
        print(f"   Cascade sessions: {len(self.cascade_sessions)}")  
        print(f"   Non-cascade sessions: {len(self.non_cascade_sessions)}")
        print(f"   Effective sample size: {2 * np.sqrt(len(self.cascade_sessions) * len(self.non_cascade_sessions)):.1f}")
        
        return examples
    
    def create_strategic_subset(self, n_total: int) -> List[TrainingExample]:
        """
        Create strategically selected subset preserving minority class integrity
        
        Strategy: ALL non-cascades + diverse selection of cascades
        """
        
        print(f"🎯 Creating strategic subset (n={n_total})...")
        
        # Always include ALL non-cascade sessions (critical for minority class)
        n_non_cascades = len(self.non_cascade_sessions)
        selected_sessions = self.non_cascade_sessions.copy()
        
        # Calculate remaining cascade slots
        n_cascades_needed = n_total - n_non_cascades
        
        if n_cascades_needed > len(self.cascade_sessions):
            print(f"⚠️ Requested {n_cascades_needed} cascades but only {len(self.cascade_sessions)} available")
            n_cascades_needed = len(self.cascade_sessions)
        
        # Select diverse cascade sessions using pattern diversity
        if n_cascades_needed > 0:
            # Simple diversity selection: spread across available cascades
            cascade_indices = np.linspace(0, len(self.cascade_sessions)-1, n_cascades_needed, dtype=int)
            selected_cascades = [self.cascade_sessions[i] for i in cascade_indices]
            selected_sessions.extend(selected_cascades)
        
        print(f"   Strategic subset: {len(selected_sessions)} sessions")
        print(f"   Non-cascades: {n_non_cascades} (100% coverage)")
        print(f"   Cascades: {len(selected_sessions) - n_non_cascades}")
        print(f"   Minority class preservation: 100%")
        
        return selected_sessions
    
    def measure_saturation_point(self, n_sessions: int) -> SaturationPoint:
        """
        Measure information saturation at specific training set size
        """
        
        print(f"📊 Measuring saturation point for n={n_sessions}...")
        
        # Create strategic subset
        subset_sessions = self.create_strategic_subset(n_sessions)
        
        # Prepare training data
        X, y = self.trainer.prepare_training_data(subset_sessions)
        
        # Split for validation
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, stratify=y, random_state=42
        )
        
        # Train model and measure performance
        import time
        start_time = time.time()
        
        # Use conservative model to prevent overfitting
        model = self.trainer.train_conservative_model(X_train, y_train)
        
        training_time = time.time() - start_time
        
        # Evaluate performance
        train_pred = model.predict(X_train)
        val_pred = model.predict(X_val)
        
        train_accuracy = accuracy_score(y_train, train_pred)
        val_accuracy = accuracy_score(y_val, val_pred)
        
        # Calculate overfitting ratio
        overfitting_ratio = train_accuracy / (val_accuracy + 1e-8)
        
        # Calculate information content (entropy of predictions)
        val_proba = model.predict_proba(X_val)[:, 1]
        information_content = -np.mean(val_proba * np.log2(val_proba + 1e-8) + 
                                      (1-val_proba) * np.log2(1-val_proba + 1e-8))
        
        # Calculate minority class coverage
        n_non_cascades_used = sum(1 for ex in subset_sessions if not ex.cascade_occurred)
        minority_coverage = n_non_cascades_used / len(self.non_cascade_sessions)
        
        saturation_point = SaturationPoint(
            n_sessions=n_sessions,
            accuracy=val_accuracy,
            marginal_gain=0.0,  # Will calculate after collecting multiple points
            information_content=information_content,
            training_time=training_time,
            overfitting_ratio=overfitting_ratio,
            minority_class_coverage=minority_coverage
        )
        
        print(f"   Validation accuracy: {val_accuracy:.1%}")
        print(f"   Training time: {training_time:.1f}s")
        print(f"   Overfitting ratio: {overfitting_ratio:.2f}")
        print(f"   Information content: {information_content:.3f} bits")
        
        return saturation_point
    
    def construct_learning_curve(self) -> List[SaturationPoint]:
        """
        Construct learning curve for n ∈ {20, 30, 40, 45, 50, 58} sessions
        """
        
        print("📈 Constructing learning curve...")
        
        # Test points based on mathematical analysis
        test_sizes = [20, 30, 40, 45, 50, len(self.all_sessions)]
        
        # Remove sizes larger than available data
        max_size = len(self.all_sessions)
        test_sizes = [size for size in test_sizes if size <= max_size]
        
        saturation_points = []
        
        for n in test_sizes:
            try:
                point = self.measure_saturation_point(n)
                saturation_points.append(point)
            except Exception as e:
                print(f"⚠️ Failed to measure n={n}: {e}")
                continue
        
        # Calculate marginal gains
        for i in range(1, len(saturation_points)):
            curr = saturation_points[i]
            prev = saturation_points[i-1]
            
            # ∂accuracy/∂n
            marginal_gain = (curr.accuracy - prev.accuracy) / (curr.n_sessions - prev.n_sessions)
            saturation_points[i].marginal_gain = marginal_gain
        
        self.saturation_points = saturation_points
        
        print(f"✅ Learning curve constructed with {len(saturation_points)} points")
        return saturation_points
    
    def find_saturation_threshold(self) -> int:
        """
        Find the session count where marginal information gain < 0.01 bits
        """
        
        print("🔍 Finding information saturation threshold...")
        
        saturation_threshold = None
        
        for point in self.saturation_points[1:]:  # Skip first point (no marginal gain)
            print(f"   n={point.n_sessions}: accuracy={point.accuracy:.1%}, "
                  f"marginal_gain={point.marginal_gain:.4f}")
            
            # Check if marginal gain is negligible
            if abs(point.marginal_gain) < 0.001:  # < 0.1% improvement per session
                saturation_threshold = point.n_sessions
                print(f"   📊 Saturation detected at n={saturation_threshold}")
                break
        
        if saturation_threshold is None:
            print("   ⚠️ No clear saturation point found - may need more data")
            # Use point with best accuracy/time ratio
            best_point = max(self.saturation_points, 
                           key=lambda p: p.accuracy / (p.training_time + 1))
            saturation_threshold = best_point.n_sessions
            print(f"   📊 Using best efficiency point: n={saturation_threshold}")
        
        return saturation_threshold
    
    def generate_optimal_subset(self, optimal_n: int) -> OptimalSubset:
        """
        Generate the optimal training subset recommendation
        """
        
        print(f"🎯 Generating optimal subset (n={optimal_n})...")
        
        # Create optimal subset
        optimal_sessions = self.create_strategic_subset(optimal_n)
        
        # Get session IDs
        session_ids = [ex.session_id for ex in optimal_sessions]
        
        # Count cascade/non-cascade
        n_cascades = sum(1 for ex in optimal_sessions if ex.cascade_occurred)
        n_non_cascades = sum(1 for ex in optimal_sessions if not ex.cascade_occurred)
        
        # Estimate performance from learning curve
        optimal_point = None
        for point in self.saturation_points:
            if point.n_sessions == optimal_n:
                optimal_point = point
                break
        
        expected_accuracy = optimal_point.accuracy if optimal_point else 0.90
        
        # Calculate computational savings
        full_size = len(self.all_sessions)
        computational_savings = 1.0 - (optimal_n / full_size)
        
        # Estimate information preservation (based on minority class coverage)
        information_preservation = n_non_cascades / len(self.non_cascade_sessions)
        
        optimal_subset = OptimalSubset(
            session_ids=session_ids,
            n_cascades=n_cascades,
            n_non_cascades=n_non_cascades,
            expected_accuracy=expected_accuracy,
            information_preservation=information_preservation,
            computational_savings=computational_savings
        )
        
        print(f"✅ Optimal subset generated:")
        print(f"   Sessions: {len(session_ids)}")
        print(f"   Cascades: {n_cascades}")
        print(f"   Non-cascades: {n_non_cascades}")
        print(f"   Expected accuracy: {expected_accuracy:.1%}")
        print(f"   Information preservation: {information_preservation:.1%}")
        print(f"   Computational savings: {computational_savings:.1%}")
        
        return optimal_subset
    
    def save_optimal_subset(self, optimal_subset: OptimalSubset, 
                           filename: str = "train_optimal.json"):
        """Save optimal training subset to file"""
        
        # Get the actual session data
        optimal_sessions = []
        for session_id in optimal_subset.session_ids:
            for example in self.all_sessions:
                if example.session_id == session_id:
                    optimal_sessions.append({
                        'session_id': example.session_id,
                        'cascade_occurred': example.cascade_occurred,
                        'context_features': {
                            'volatility_regime': example.context_features.volatility_regime,
                            'trend_strength': example.context_features.trend_strength,
                            'momentum_score': example.context_features.momentum_score,
                            'energy_carryover': example.context_features.energy_carryover
                        }
                    })
                    break
        
        # Save optimal subset
        subset_data = {
            'metadata': {
                'n_sessions': len(optimal_subset.session_ids),
                'n_cascades': optimal_subset.n_cascades,
                'n_non_cascades': optimal_subset.n_non_cascades,
                'expected_accuracy': optimal_subset.expected_accuracy,
                'information_preservation': optimal_subset.information_preservation,
                'computational_savings': optimal_subset.computational_savings,
                'selection_strategy': 'strategic_minority_preservation',
                'creation_timestamp': pd.Timestamp.now().isoformat()
            },
            'sessions': optimal_sessions
        }
        
        with open(filename, 'w') as f:
            json.dump(subset_data, f, indent=2)
        
        print(f"💾 Optimal subset saved: {filename}")
        return filename
    
    def visualize_learning_curve(self, filename: str = "learning_curve.png"):
        """Generate learning curve visualization"""
        
        if not self.saturation_points:
            print("⚠️ No saturation points available for visualization")
            return
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        n_sessions = [p.n_sessions for p in self.saturation_points]
        accuracies = [p.accuracy for p in self.saturation_points]
        training_times = [p.training_time for p in self.saturation_points]
        marginal_gains = [p.marginal_gain for p in self.saturation_points]
        
        # 1. Learning curve
        ax1.plot(n_sessions, accuracies, 'b-o', linewidth=2, markersize=8)
        ax1.set_xlabel('Training Set Size (n)')
        ax1.set_ylabel('Validation Accuracy')
        ax1.set_title('Learning Curve: Accuracy vs Training Set Size')
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=0.90, color='r', linestyle='--', alpha=0.7, label='90% Target')
        ax1.legend()
        
        # 2. Training efficiency
        ax2.plot(n_sessions, training_times, 'g-s', linewidth=2, markersize=8)
        ax2.set_xlabel('Training Set Size (n)')
        ax2.set_ylabel('Training Time (seconds)')
        ax2.set_title('Training Efficiency')
        ax2.grid(True, alpha=0.3)
        
        # 3. Marginal gains
        ax3.plot(n_sessions[1:], marginal_gains[1:], 'r-^', linewidth=2, markersize=8)
        ax3.set_xlabel('Training Set Size (n)')
        ax3.set_ylabel('Marginal Accuracy Gain')
        ax3.set_title('Marginal Information Gain (∂accuracy/∂n)')
        ax3.axhline(y=0.001, color='r', linestyle='--', alpha=0.7, label='Negligible Threshold')
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        
        # 4. Efficiency ratio
        efficiency = [acc/time for acc, time in zip(accuracies, training_times)]
        ax4.plot(n_sessions, efficiency, 'm-d', linewidth=2, markersize=8)
        ax4.set_xlabel('Training Set Size (n)')
        ax4.set_ylabel('Accuracy per Second')
        ax4.set_title('Training Efficiency Ratio')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(filename, dpi=150, bbox_inches='tight')
        plt.show()
        
        print(f"📊 Learning curve visualization saved: {filename}")
        
    def execute_saturation_analysis(self) -> Dict[str, Any]:
        """Execute complete information saturation analysis"""
        
        print("🔬 EXECUTING INFORMATION SATURATION ANALYSIS")
        print("=" * 50)
        print("Testing hypothesis: n=45 sessions achieve 90%+ accuracy")
        print()
        
        # Step 1: Load and categorize sessions
        sessions = self.load_and_categorize_sessions()
        
        if len(self.non_cascade_sessions) < 8:
            print(f"⚠️ WARNING: Only {len(self.non_cascade_sessions)} non-cascade sessions")
            print("   May not have sufficient minority class diversity")
        
        # Step 2: Construct learning curve
        saturation_points = self.construct_learning_curve()
        
        # Step 3: Find saturation threshold
        optimal_n = self.find_saturation_threshold()
        
        # Step 4: Generate optimal subset
        optimal_subset = self.generate_optimal_subset(optimal_n)
        
        # Step 5: Save optimal subset
        subset_file = self.save_optimal_subset(optimal_subset)
        
        # Step 6: Visualize results
        self.visualize_learning_curve()
        
        # Compile results
        results = {
            'hypothesis_validated': optimal_subset.expected_accuracy >= 0.90,
            'optimal_n_sessions': optimal_n,
            'computational_savings': optimal_subset.computational_savings,
            'information_preservation': optimal_subset.information_preservation,
            'expected_accuracy': optimal_subset.expected_accuracy,
            'effective_sample_size': 2 * np.sqrt(optimal_subset.n_cascades * optimal_subset.n_non_cascades),
            'saturation_points': saturation_points,
            'optimal_subset_file': subset_file,
            'mathematical_justification': f"Information plateaus at n ≈ 10×{len(self.non_cascade_sessions)} ≈ {10*len(self.non_cascade_sessions)} sessions"
        }
        
        print(f"\n🎯 INFORMATION SATURATION ANALYSIS COMPLETE")
        print(f"Hypothesis: {'VALIDATED' if results['hypothesis_validated'] else 'REJECTED'}")
        print(f"Optimal training set: {optimal_n} sessions")
        print(f"Expected accuracy: {optimal_subset.expected_accuracy:.1%}")
        print(f"Computational savings: {optimal_subset.computational_savings:.1%}")
        print(f"Information preservation: {optimal_subset.information_preservation:.1%}")
        
        if results['hypothesis_validated']:
            print("✅ Strategic subset recommended for production training")
        else:
            print("⚠️ Full dataset recommended - subset insufficient")
        
        return results

def main():
    """Execute information saturation analysis"""
    
    analyzer = InformationSaturationAnalyzer()
    results = analyzer.execute_saturation_analysis()
    
    return results

if __name__ == "__main__":
    saturation_results = main()
#!/usr/bin/env python3
"""
Baseline Performance Capture for Case Study Preparation
======================================================

Captures the current Oracle system's performance on NYAM_2025_08_05_COMPLETE.json
to establish a control group baseline for comparison against enhancement approaches.

This provides the reference point for measuring improvement from:
1. Micro-Event Enhancement Pipeline
2. Cascade-as-Unit Strategic Architecture
"""

import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any
import logging

# Import the working Oracle system
from oracle import ProjectOracle

def prepare_oracle_input(level1_data: Dict) -> Dict:
    """
    Convert raw Level-1 data to format Oracle expects
    
    Oracle requires cascade events, but raw data has price movements.
    This creates a minimal cascade event structure from price movements.
    """
    
    # Extract price movements
    price_movements = level1_data.get('price_movements', [])
    
    # Convert price movements to cascade events
    cascade_events = []
    
    for i, movement in enumerate(price_movements):
        # Create cascade event from price movement
        cascade_event = {
            'timestamp': movement['timestamp'],
            'price_level': movement['price_level'],
            'event_type': 'price_movement',
            'movement_type': movement.get('movement_type', 'unknown'),
            'event_id': i
        }
        cascade_events.append(cascade_event)
    
    # Create Oracle-compatible input
    oracle_input = level1_data.copy()
    
    # RG Scaler expects micro_timing_analysis.cascade_events
    oracle_input['micro_timing_analysis'] = {
        'cascade_events': cascade_events,
        'total_events': len(cascade_events),
        'analysis_timestamp': datetime.now().isoformat()
    }
    
    # Ensure required fields exist
    if 'session_metadata' not in oracle_input:
        oracle_input['session_metadata'] = {
            'session_type': 'ny_am',
            'session_date': '2025-08-05'
        }
    
    return oracle_input

def capture_baseline_performance(session_path: str) -> Dict[str, Any]:
    """
    Capture baseline Oracle performance metrics on specified session
    
    Args:
        session_path: Path to Level-1 session JSON file
        
    Returns:
        Dict containing baseline performance metrics
    """
    
    print("📊 BASELINE PERFORMANCE CAPTURE")
    print("=" * 50)
    print(f"Session: {session_path}")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    # Initialize Oracle system
    print("\n🔄 Initializing Oracle system...")
    start_time = time.time()
    
    try:
        oracle = ProjectOracle()
        init_time = time.time() - start_time
        print(f"✅ Oracle initialized in {init_time:.2f}s")
        
    except Exception as e:
        print(f"❌ Oracle initialization failed: {e}")
        return {"error": "initialization_failed", "details": str(e)}
    
    # Load session data
    print(f"\n📂 Loading session data...")
    try:
        with open(session_path, 'r') as f:
            session_data = json.load(f)
        print(f"✅ Session data loaded successfully")
        
    except Exception as e:
        print(f"❌ Failed to load session data: {e}")
        return {"error": "data_loading_failed", "details": str(e)}
    
    # Run Oracle prediction
    print(f"\n🎯 Running Oracle prediction...")
    prediction_start = time.time()
    
    try:
        # Extract the core session data format Oracle expects
        level1_data = session_data.get('level1_json', session_data)
        
        # Convert raw price movements to cascade events format Oracle expects
        oracle_input = prepare_oracle_input(level1_data)
        
        result = oracle.predict_cascade_timing(oracle_input)
        prediction_time = time.time() - prediction_start
        
        print(f"✅ Oracle prediction completed in {prediction_time:.3f}s")
        
    except Exception as e:
        print(f"❌ Oracle prediction failed: {e}")
        return {"error": "prediction_failed", "details": str(e)}
    
    # Extract baseline metrics
    baseline_metrics = {
        "capture_timestamp": datetime.now().isoformat(),
        "session_file": session_path,
        "session_metadata": session_data["level1_json"]["session_metadata"],
        
        # Performance metrics
        "initialization_time_seconds": init_time,
        "prediction_time_seconds": prediction_time,
        "total_processing_time_seconds": init_time + prediction_time,
        
        # Oracle prediction results
        "predicted_cascade_time": result.predicted_cascade_time,
        "prediction_confidence": result.prediction_confidence,
        "methodology": result.methodology,
        "processing_time": result.processing_time,
        "enhancement_active": result.enhancement_active,
        
        # Component results (stored as-is from Oracle)
        "rg_scaler_result": result.rg_scaler_result,
        "hawkes_prediction": result.hawkes_prediction,
        "vqe_optimization_active": result.vqe_optimization_active,
        "confidence_boost": result.confidence_boost,
        
        # System status
        "oracle_version": "baseline_91.1%",
        "components_active": {
            "rg_scaler": True,
            "fisher_monitor": True,
            "hawkes_engine": True,
            "xgboost_meta": getattr(oracle, 'xgboost_model', None) is not None,
            "vqe_optimizer": True
        }
    }
    
    # Display baseline summary
    print(f"\n📈 BASELINE PERFORMANCE SUMMARY:")
    print(f"   Predicted Cascade Time: {baseline_metrics['predicted_cascade_time']} minutes")
    print(f"   Prediction Confidence: {baseline_metrics['prediction_confidence']:.3f}")
    print(f"   Methodology: {baseline_metrics['methodology']}")
    print(f"   Processing Time: {baseline_metrics['total_processing_time_seconds']:.3f}s")
    print(f"   Enhancement Active: {baseline_metrics['enhancement_active']}")
    
    # Save baseline metrics
    output_path = f"baseline_performance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_path, 'w') as f:
        json.dump(baseline_metrics, f, indent=2, default=str)
    
    print(f"\n💾 Baseline metrics saved: {output_path}")
    
    return baseline_metrics

def analyze_session_characteristics(session_path: str) -> Dict[str, Any]:
    """
    Analyze session characteristics for context
    """
    
    with open(session_path, 'r') as f:
        session_data = json.load(f)
    
    level1_data = session_data["level1_json"]
    
    characteristics = {
        "session_type": level1_data["session_metadata"]["session_type"],
        "session_date": level1_data["session_metadata"]["session_date"],
        "session_duration_minutes": level1_data["session_metadata"]["session_duration"],
        "data_completeness": level1_data["session_metadata"]["data_completeness"],
        
        # FPFVG analysis
        "fpfvg_present": level1_data.get("session_fpfvg", {}).get("fpfvg_present", False),
        "fpfvg_interactions": len(level1_data.get("session_fpfvg", {}).get("fpfvg_formation", {}).get("interactions", [])),
        
        # Price movement analysis
        "price_movements_count": len(level1_data.get("price_movements", [])),
        "liquidity_events_count": len(level1_data.get("liquidity_events", [])),
        
        # Session context
        "session_summary_present": "session_summary" in level1_data,
        "cascade_events_present": "cascade_events" in level1_data
    }
    
    return characteristics

if __name__ == "__main__":
    # Target session for baseline capture
    session_path = "../data/sessions/level_1/NYAM_Lvl-1_2025_08_05_COMPLETE.json"
    
    # Analyze session characteristics first
    print("🔍 ANALYZING SESSION CHARACTERISTICS")
    print("=" * 40)
    
    characteristics = analyze_session_characteristics(session_path)
    
    for key, value in characteristics.items():
        print(f"   {key}: {value}")
    
    print()
    
    # Capture baseline performance
    baseline_metrics = capture_baseline_performance(session_path)
    
    if "error" not in baseline_metrics:
        print(f"\n🏆 BASELINE CAPTURE COMPLETE")
        print(f"   Oracle 91.1% baseline performance documented")
        print(f"   Ready for enhancement comparison")
        print(f"   Control group established for case study")
    else:
        print(f"\n❌ BASELINE CAPTURE FAILED")
        print(f"   Error: {baseline_metrics['error']}")
        print(f"   Details: {baseline_metrics['details']}")
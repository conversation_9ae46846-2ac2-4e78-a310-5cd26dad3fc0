#!/usr/bin/env python3
"""
Oracle Hawkes Vectorization Benchmark
=====================================

Tactical benchmark for validating vectorized Hawkes process implementation
performance improvements over iterative baseline with memory profiling.

AUGMENT AI TACTICAL INSTRUCTIONS:

1. VECTORIZATION VALIDATION:
   - Iterative Hawkes: O(n²) loop-based implementation (baseline)  
   - Vectorized Hawkes: NumPy broadcasting O(n²) optimized (target)
   - Memory profiling: Track RSS memory usage during computation
   - Numerical accuracy validation: Ensure results are mathematically equivalent

2. ORACLE HAWKES PARAMETERS:
   - μ = 0.02 (baseline intensity from HTF constants)
   - α = 35.51 (excitation strength from Oracle validation) 
   - β = 0.00442 (decay rate from Oracle system)
   - Event sizes: [100, 500, 1000, 2000, 5000] realistic session scales

3. PERFORMANCE METRICS:
   - Execution time comparison with 95% confidence intervals
   - Memory usage delta (vectorized vs iterative)
   - Numerical precision validation (absolute difference < 1e-10)
   - Scalability analysis: Time complexity validation

4. TACTICAL OUTCOMES:
   - Prove vectorization provides significant speedup without accuracy loss
   - Demonstrate memory efficiency for large-scale Oracle predictions
   - Validate production readiness for 5000+ event sessions

Expected Results: >40% speedup for n=5000 with equivalent numerical accuracy
"""

import sys
import os
import time
import json
import gc
import psutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import logging

import numpy as np
from scipy import stats

# Add project paths
sys.path.append('.')
sys.path.append('./core_predictor')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class HawkesVectorizationResult:
    """Benchmark result for Hawkes vectorization comparison"""
    implementation: str
    data_size: int
    execution_time_ms: float
    memory_peak_mb: float
    memory_delta_mb: float
    numerical_result: np.ndarray
    accuracy_maintained: bool
    trials_completed: int
    confidence_interval: Tuple[float, float]
    speedup_factor: Optional[float] = None

class OracleHawkesBenchmark:
    """Benchmark vectorized vs iterative Hawkes implementations"""
    
    def __init__(self, n_trials: int = 5, confidence_level: float = 0.95):
        self.n_trials = n_trials
        self.confidence_level = confidence_level
        
        # Test data sizes for Oracle session scales
        self.test_sizes = [100, 500, 1000, 2000, 5000]
        
        # Oracle validated Hawkes parameters
        self.oracle_params = {
            "mu": 0.02,      # HTF baseline intensity
            "alpha": 35.51,  # HTF excitation strength
            "beta": 0.00442  # HTF decay rate
        }
        
        logger.info(f"Oracle Hawkes benchmark initialized")
        logger.info(f"Test sizes: {self.test_sizes}")
        logger.info(f"Oracle parameters: μ={self.oracle_params['mu']}, α={self.oracle_params['alpha']}, β={self.oracle_params['beta']}")
    
    def generate_oracle_hawkes_events(self, n_events: int) -> np.ndarray:
        """Generate realistic Oracle session event times"""
        
        # Generate events with realistic Oracle session patterns
        session_duration = 150.0  # 2.5 hours typical session
        
        # Create clustered events (realistic market microstructure)
        n_clusters = max(1, n_events // 20)  # ~20 events per cluster
        cluster_centers = np.sort(np.random.uniform(0, session_duration, n_clusters))
        
        events = []
        events_per_cluster = n_events // n_clusters
        remaining_events = n_events % n_clusters
        
        for i, center in enumerate(cluster_centers):
            cluster_size = events_per_cluster + (1 if i < remaining_events else 0)
            
            # Generate events around cluster center
            cluster_spread = min(10.0, session_duration / (2 * n_clusters))
            cluster_events = np.random.normal(center, cluster_spread, cluster_size)
            
            # Keep events within session bounds
            cluster_events = np.clip(cluster_events, 0, session_duration)
            events.extend(cluster_events)
        
        # Sort and return as array
        return np.sort(np.array(events))
    
    def iterative_hawkes_intensity(self, events: np.ndarray, params: Dict[str, float]) -> np.ndarray:
        """
        Iterative Hawkes intensity computation - O(n²) baseline implementation
        
        This is the traditional loop-based approach that vectorization improves upon.
        """
        
        mu = params["mu"]
        alpha = params["alpha"] 
        beta = params["beta"]
        
        n_events = len(events)
        intensities = np.full(n_events, mu, dtype=np.float64)
        
        if n_events <= 1:
            return intensities
        
        # Iterative computation - explicit double loop O(n²)
        for i in range(n_events):
            current_time = events[i]
            
            # Sum excitation from all previous events
            for j in range(i):
                past_time = events[j]
                dt = current_time - past_time
                
                if dt > 0:
                    # Apply numerical stability check
                    if beta * dt < 20.0:  # Avoid underflow
                        excitation = alpha * np.exp(-beta * dt)
                        intensities[i] += excitation
        
        return intensities
    
    def vectorized_hawkes_intensity(self, events: np.ndarray, params: Dict[str, float]) -> np.ndarray:
        """
        Vectorized Hawkes intensity computation - O(n²) optimized implementation
        
        This uses NumPy broadcasting to eliminate explicit loops and optimize memory access.
        """
        
        mu = params["mu"]
        alpha = params["alpha"]
        beta = params["beta"]
        
        n_events = len(events)
        intensities = np.full(n_events, mu, dtype=np.float64)
        
        if n_events <= 1:
            return intensities
        
        # Vectorized computation using broadcasting
        # time_matrix[i,j] = events[i] - events[j]
        time_matrix = np.subtract.outer(events, events)
        
        # Only consider past events (lower triangular matrix, excluding diagonal)
        past_mask = np.tril(time_matrix > 0, k=-1).T
        
        # Numerical stability: avoid underflow for large time differences
        underflow_threshold = 20.0 / beta if beta > 0 else np.inf
        stable_mask = past_mask & (time_matrix < underflow_threshold)
        
        # Vectorized exponential computation with stability
        with np.errstate(over='ignore', under='ignore'):
            exp_terms = np.where(
                stable_mask,
                alpha * np.exp(-beta * time_matrix),
                0.0
            )
        
        # Sum excitation contributions for each event
        excitation_sums = np.sum(exp_terms, axis=0)
        intensities += excitation_sums
        
        return intensities
    
    def validate_numerical_accuracy(self, iterative_result: np.ndarray, 
                                  vectorized_result: np.ndarray, 
                                  tolerance: float = 1e-10) -> bool:
        """Validate that vectorized and iterative results are numerically equivalent"""
        
        if len(iterative_result) != len(vectorized_result):
            return False
        
        # Check element-wise absolute differences
        abs_diff = np.abs(iterative_result - vectorized_result)
        max_diff = np.max(abs_diff)
        mean_diff = np.mean(abs_diff)
        
        # Relative error for non-zero elements
        nonzero_mask = iterative_result != 0
        if np.any(nonzero_mask):
            rel_diff = abs_diff[nonzero_mask] / np.abs(iterative_result[nonzero_mask])
            max_rel_diff = np.max(rel_diff)
        else:
            max_rel_diff = 0.0
        
        # Accuracy criteria
        absolute_accuracy = max_diff < tolerance
        relative_accuracy = max_rel_diff < (tolerance * 1000)  # More lenient for relative
        
        logger.debug(f"Numerical validation: max_abs_diff={max_diff:.2e}, max_rel_diff={max_rel_diff:.2e}")
        
        return absolute_accuracy and relative_accuracy
    
    def benchmark_hawkes_implementation(self, implementation_name: str, 
                                      hawkes_func, data_size: int) -> HawkesVectorizationResult:
        """Benchmark a specific Hawkes implementation with memory profiling"""
        
        logger.info(f"Benchmarking {implementation_name} with {data_size} events")
        
        execution_times = []
        memory_peaks = []
        memory_deltas = []
        results = []
        completed_trials = 0
        
        for trial in range(self.n_trials):
            try:
                # Generate fresh event data for each trial
                events = self.generate_oracle_hawkes_events(data_size)
                
                # Memory profiling setup
                process = psutil.Process()
                gc.collect()  # Clean garbage before measurement
                memory_before = process.memory_info().rss / (1024 * 1024)  # MB
                
                # Time the computation
                start_time = time.perf_counter()
                intensity_result = hawkes_func(events, self.oracle_params)
                execution_time = (time.perf_counter() - start_time) * 1000  # ms
                
                # Memory measurement after computation
                memory_after = process.memory_info().rss / (1024 * 1024)  # MB
                memory_delta = memory_after - memory_before
                memory_peak = memory_after
                
                execution_times.append(execution_time)
                memory_peaks.append(memory_peak)
                memory_deltas.append(max(0, memory_delta))  # Non-negative delta
                results.append(intensity_result)
                completed_trials += 1
                
            except Exception as e:
                logger.error(f"Trial {trial} failed for {implementation_name}: {e}")
        
        if completed_trials == 0:
            logger.error(f"All trials failed for {implementation_name}")
            return HawkesVectorizationResult(
                implementation=implementation_name,
                data_size=data_size,
                execution_time_ms=float('inf'),
                memory_peak_mb=0.0,
                memory_delta_mb=0.0,
                numerical_result=np.array([]),
                accuracy_maintained=False,
                trials_completed=0,
                confidence_interval=(0.0, 0.0)
            )
        
        # Statistical analysis
        mean_time = np.mean(execution_times)
        mean_memory_peak = np.mean(memory_peaks)
        mean_memory_delta = np.mean(memory_deltas)
        
        # Confidence interval for execution time
        std_error = stats.sem(execution_times)
        ci_lower, ci_upper = stats.t.interval(
            self.confidence_level, completed_trials - 1,
            loc=mean_time, scale=std_error
        )
        
        # Use the last successful result for numerical validation
        final_result = results[-1] if results else np.array([])
        
        result = HawkesVectorizationResult(
            implementation=implementation_name,
            data_size=data_size,
            execution_time_ms=mean_time,
            memory_peak_mb=mean_memory_peak,
            memory_delta_mb=mean_memory_delta,
            numerical_result=final_result,
            accuracy_maintained=True,  # Will be validated separately
            trials_completed=completed_trials,
            confidence_interval=(ci_lower, ci_upper)
        )
        
        logger.info(f"{implementation_name}: {mean_time:.2f}ms, memory: {mean_memory_delta:.1f}MB delta")
        
        return result
    
    def run_comprehensive_benchmark(self) -> Dict[str, List[HawkesVectorizationResult]]:
        """Run comprehensive benchmark comparing vectorized vs iterative Hawkes"""
        
        logger.info("🚀 Running comprehensive Hawkes vectorization benchmark")
        
        results = {
            "iterative": [],
            "vectorized": []
        }
        
        for data_size in self.test_sizes:
            logger.info(f"📊 Testing data size: {data_size}")
            
            # Benchmark iterative implementation (baseline)
            iterative_result = self.benchmark_hawkes_implementation(
                f"Iterative_{data_size}",
                self.iterative_hawkes_intensity,
                data_size
            )
            results["iterative"].append(iterative_result)
            
            # Benchmark vectorized implementation (optimized)
            vectorized_result = self.benchmark_hawkes_implementation(
                f"Vectorized_{data_size}",
                self.vectorized_hawkes_intensity,
                data_size
            )
            results["vectorized"].append(vectorized_result)
            
            # Numerical accuracy validation
            if (len(iterative_result.numerical_result) > 0 and 
                len(vectorized_result.numerical_result) > 0):
                
                accuracy_maintained = self.validate_numerical_accuracy(
                    iterative_result.numerical_result,
                    vectorized_result.numerical_result
                )
                
                # Update accuracy status
                vectorized_result.accuracy_maintained = accuracy_maintained
                
                # Calculate speedup factor
                if iterative_result.execution_time_ms > 0:
                    speedup = iterative_result.execution_time_ms / vectorized_result.execution_time_ms
                    vectorized_result.speedup_factor = speedup
                    
                    logger.info(f"  Size {data_size}: {speedup:.2f}x speedup, accuracy: {'✅' if accuracy_maintained else '❌'}")
                else:
                    logger.warning(f"  Size {data_size}: Cannot calculate speedup (iterative failed)")
            
            # Statistical significance test
            if (iterative_result.execution_time_ms != float('inf') and 
                vectorized_result.execution_time_ms != float('inf')):
                
                # Check if confidence intervals overlap
                iter_ci = iterative_result.confidence_interval
                vec_ci = vectorized_result.confidence_interval
                
                ci_overlap = not (iter_ci[1] < vec_ci[0] or vec_ci[1] < iter_ci[0])
                significance = "significant" if not ci_overlap else "not significant"
                logger.info(f"  Statistical significance: {significance}")
        
        return results
    
    def analyze_vectorization_improvements(self, results: Dict[str, List[HawkesVectorizationResult]]) -> List[Dict[str, Any]]:
        """Analyze vectorization improvements across all data sizes"""
        
        improvements = []
        
        iterative_results = results.get("iterative", [])
        vectorized_results = results.get("vectorized", [])
        
        for iter_result, vec_result in zip(iterative_results, vectorized_results):
            if (iter_result.execution_time_ms != float('inf') and
                vec_result.execution_time_ms != float('inf') and
                iter_result.data_size == vec_result.data_size):
                
                # Time improvement calculation
                time_improvement = ((iter_result.execution_time_ms - vec_result.execution_time_ms) / 
                                   iter_result.execution_time_ms) * 100
                
                # Memory efficiency calculation
                memory_efficiency = ((iter_result.memory_delta_mb - vec_result.memory_delta_mb) / 
                                   max(iter_result.memory_delta_mb, 0.001)) * 100
                
                # Speedup factor
                speedup_factor = vec_result.speedup_factor or 1.0
                
                improvement = {
                    "data_size": iter_result.data_size,
                    "time_improvement_percent": time_improvement,
                    "memory_efficiency_percent": memory_efficiency,
                    "speedup_factor": speedup_factor,
                    "iterative_time_ms": iter_result.execution_time_ms,
                    "vectorized_time_ms": vec_result.execution_time_ms,
                    "iterative_memory_mb": iter_result.memory_delta_mb,
                    "vectorized_memory_mb": vec_result.memory_delta_mb,
                    "accuracy_maintained": vec_result.accuracy_maintained,
                    "statistical_confidence": {
                        "iterative_ci": iter_result.confidence_interval,
                        "vectorized_ci": vec_result.confidence_interval
                    }
                }
                
                improvements.append(improvement)
        
        return improvements
    
    def generate_stakeholder_report(self, results: Dict[str, List[HawkesVectorizationResult]],
                                  improvements: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate comprehensive stakeholder report"""
        
        # Calculate summary metrics
        successful_vectorized = [r for r in results.get("vectorized", []) 
                               if r.execution_time_ms != float('inf')]
        successful_iterative = [r for r in results.get("iterative", []) 
                              if r.execution_time_ms != float('inf')]
        
        total_tests = len(results.get("vectorized", [])) + len(results.get("iterative", []))
        successful_tests = len(successful_vectorized) + len(successful_iterative)
        
        # Performance metrics
        avg_speedup = np.mean([imp["speedup_factor"] for imp in improvements]) if improvements else 1.0
        max_speedup = max([imp["speedup_factor"] for imp in improvements], default=1.0)
        avg_time_improvement = np.mean([imp["time_improvement_percent"] for imp in improvements]) if improvements else 0.0
        
        # Accuracy validation
        accuracy_maintained = all(imp["accuracy_maintained"] for imp in improvements)
        
        # Memory efficiency 
        avg_memory_efficiency = np.mean([imp["memory_efficiency_percent"] for imp in improvements]) if improvements else 0.0
        
        # Generate recommendations
        recommendations = []
        
        if avg_speedup > 2.0:
            recommendations.append(f"✅ Excellent vectorization performance ({avg_speedup:.1f}x average speedup)")
        elif avg_speedup > 1.4:
            recommendations.append(f"🔶 Good vectorization performance ({avg_speedup:.1f}x average speedup)")
        else:
            recommendations.append(f"⚠️ Vectorization below expectations ({avg_speedup:.1f}x speedup)")
        
        if accuracy_maintained:
            recommendations.append("✅ Numerical accuracy maintained across all test sizes")
        else:
            recommendations.append("⚠️ Numerical accuracy concerns detected - requires investigation")
        
        if max_speedup > 3.0:
            recommendations.append(f"✅ Peak speedup >3.0x achieved for large datasets ({max_speedup:.1f}x)")
        
        if avg_memory_efficiency > 0:
            recommendations.append(f"✅ Memory efficiency improved by {avg_memory_efficiency:.1f}%")
        elif avg_memory_efficiency < -20:
            recommendations.append("⚠️ Vectorization increases memory usage significantly")
        
        # Production readiness assessment
        production_ready = (avg_speedup > 1.4 and accuracy_maintained and 
                          successful_tests == total_tests)
        
        if production_ready:
            recommendations.append("✅ Vectorized Hawkes implementation ready for production deployment")
        else:
            recommendations.append("⚠️ Additional optimization required before production deployment")
        
        report = {
            "executive_summary": {
                "test_name": "Oracle Hawkes Process Vectorization Validation",
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "success_rate_percent": (successful_tests / total_tests) * 100 if total_tests > 0 else 0,
                "average_speedup_factor": avg_speedup,
                "maximum_speedup_factor": max_speedup,
                "average_time_improvement_percent": avg_time_improvement,
                "numerical_accuracy_maintained": accuracy_maintained,
                "production_ready": production_ready
            },
            "performance_improvements": improvements,
            "recommendations": recommendations,
            "detailed_results": {
                implementation: [
                    {
                        "data_size": r.data_size,
                        "execution_time_ms": r.execution_time_ms,
                        "memory_peak_mb": r.memory_peak_mb,
                        "memory_delta_mb": r.memory_delta_mb,
                        "confidence_interval": r.confidence_interval,
                        "trials_completed": r.trials_completed,
                        "accuracy_maintained": r.accuracy_maintained,
                        "speedup_factor": r.speedup_factor
                    }
                    for r in impl_results
                ]
                for implementation, impl_results in results.items()
            },
            "test_configuration": {
                "oracle_parameters": self.oracle_params,
                "test_sizes": self.test_sizes,
                "trials_per_size": self.n_trials,
                "confidence_level": self.confidence_level,
                "timestamp": datetime.now().isoformat()
            }
        }
        
        return report
    
    def save_report(self, report: Dict[str, Any], output_path: Optional[Path] = None) -> Path:
        """Save benchmark report to JSON file"""
        
        if output_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_path = Path(f"oracle_hawkes_vectorization_benchmark_{timestamp}.json")
        
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Benchmark report saved: {output_path}")
        return output_path

def main():
    """Run Oracle Hawkes vectorization benchmark"""
    
    print("🚀 ORACLE HAWKES PROCESS VECTORIZATION BENCHMARK")
    print("=" * 60)
    print("Objective: Validate vectorized vs iterative Hawkes implementation")
    print("Focus: Performance improvement with maintained numerical accuracy")
    print("Oracle Parameters: μ=0.02, α=35.51, β=0.00442")
    print("=" * 60)
    
    # Initialize benchmark
    benchmark = OracleHawkesBenchmark(n_trials=5, confidence_level=0.95)
    
    # Run comprehensive benchmark
    print("\n📊 Running comprehensive benchmark...")
    results = benchmark.run_comprehensive_benchmark()
    
    # Analyze improvements
    print("\n🎯 Analyzing vectorization improvements...")
    improvements = benchmark.analyze_vectorization_improvements(results)
    
    # Generate stakeholder report
    print("\n📋 Generating stakeholder report...")
    report = benchmark.generate_stakeholder_report(results, improvements)
    
    # Save report
    report_path = benchmark.save_report(report)
    
    # Display summary
    print(f"\n🏆 BENCHMARK RESULTS SUMMARY")
    print("=" * 40)
    summary = report["executive_summary"]
    print(f"Total Tests: {summary['total_tests']}")
    print(f"Success Rate: {summary['success_rate_percent']:.1f}%")
    print(f"Average Speedup: {summary['average_speedup_factor']:.2f}x")
    print(f"Maximum Speedup: {summary['maximum_speedup_factor']:.2f}x")
    print(f"Average Time Improvement: {summary['average_time_improvement_percent']:.1f}%")
    print(f"Numerical Accuracy: {'✅ Maintained' if summary['numerical_accuracy_maintained'] else '❌ Issues'}")
    print(f"Production Ready: {'✅ Yes' if summary['production_ready'] else '❌ No'}")
    
    print(f"\n📋 KEY FINDINGS:")
    for rec in report["recommendations"]:
        print(f"  {rec}")
    
    print(f"\n📊 DETAILED IMPROVEMENTS BY DATA SIZE:")
    for imp in improvements:
        print(f"  Size {imp['data_size']}: {imp['speedup_factor']:.2f}x speedup, "
              f"{imp['time_improvement_percent']:.1f}% improvement, "
              f"accuracy: {'✅' if imp['accuracy_maintained'] else '❌'}")
    
    print(f"\n📄 Full report: {report_path}")
    
    # Tactical assessment for Augment AI
    avg_speedup = summary['average_speedup_factor']
    if avg_speedup > 2.0 and summary['numerical_accuracy_maintained']:
        print(f"\n✅ TACTICAL OBJECTIVE ACHIEVED")
        print(f"   Vectorization provides {avg_speedup:.1f}x speedup with accuracy")
        print(f"   Ready for production deployment")
    elif avg_speedup > 1.4:
        print(f"\n🔶 TACTICAL OBJECTIVE PARTIALLY ACHIEVED")
        print(f"   Good performance improvement ({avg_speedup:.1f}x speedup)")
    else:
        print(f"\n⚠️ TACTICAL OBJECTIVE NOT MET")
        print(f"   Vectorization improvement below target (<1.4x speedup)")
    
    return report

if __name__ == "__main__":
    try:
        report = main()
        
        # Exit code based on performance and accuracy
        summary = report["executive_summary"]
        success = (summary['average_speedup_factor'] > 1.4 and 
                  summary['numerical_accuracy_maintained'])
        exit_code = 0 if success else 1
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n⏹️ Benchmark interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Benchmark failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
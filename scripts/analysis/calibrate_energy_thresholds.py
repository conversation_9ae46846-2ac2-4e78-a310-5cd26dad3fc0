"""
Calibrate Energy Divergence Thresholds for Catastrophic Failure Detection
Fix the energy validation system to properly detect 89% vs 35% contamination discrepancies
"""

import json
import sys
import os
sys.path.append(os.path.dirname(__file__))
from validation.energy_validator import EnergyValidator

def calibrate_energy_thresholds():
    """Calibrate energy validation thresholds based on August 5 catastrophic failure"""
    
    print("🔧 CALIBRATING ENERGY DIVERGENCE THRESHOLDS")
    print("=" * 60)
    
    # Initialize energy validator with current settings
    energy_validator = EnergyValidator({
        'divergence_threshold': 0.2,  # Current threshold (20%)
        'echo_threshold': 0.8
    })
    
    print(f"📊 CURRENT CONFIGURATION:")
    print(f"   Divergence Threshold: {energy_validator.divergence_threshold:.1%}")
    print(f"   Echo Threshold: {energy_validator.echo_threshold:.1%}")
    
    # Test with August 5 catastrophic failure data
    catastrophic_test_cases = [
        {
            'name': 'August 5 PM - 2:30 PM Event',
            'predicted': {'contamination': 0.35},  # Original Oracle prediction
            'actual': {'contamination': 0.89},     # Actual PM session
            'expected_detection': True,            # Should detect this as failure
            'error_magnitude': abs(0.89 - 0.35)   # 54% error
        },
        {
            'name': 'August 5 PM - 2:53 PM Session Low',
            'predicted': {'contamination': 0.40},  # Slightly adjusted prediction
            'actual': {'contamination': 0.92},     # Peak contamination
            'expected_detection': True,            # Should detect this as failure
            'error_magnitude': abs(0.92 - 0.40)   # 52% error
        },
        {
            'name': 'Normal Case - Good Prediction',
            'predicted': {'contamination': 0.45},  # Reasonable prediction
            'actual': {'contamination': 0.50},     # Close actual
            'expected_detection': False,           # Should NOT flag this
            'error_magnitude': abs(0.50 - 0.45)   # 5% error
        },
        {
            'name': 'Borderline Case - Moderate Error',
            'predicted': {'contamination': 0.30},  # Moderate underestimate
            'actual': {'contamination': 0.55},     # Moderate actual
            'expected_detection': True,            # Should detect this
            'error_magnitude': abs(0.55 - 0.30)   # 25% error
        }
    ]
    
    print(f"\n🧪 TESTING CURRENT THRESHOLD (20%):")
    current_results = []
    for test in catastrophic_test_cases:
        detected = energy_validator.detect_energy_divergence(test['predicted'], test['actual'])
        correct = detected == test['expected_detection']
        current_results.append(correct)
        
        status = "✅ CORRECT" if correct else "❌ WRONG"
        detection = "🚨 DETECTED" if detected else "✅ Normal"
        expected = "Should detect" if test['expected_detection'] else "Should NOT detect"
        
        print(f"   {test['name']}: {status}")
        print(f"      Error: {test['error_magnitude']:.1%}, {detection} ({expected})")
    
    current_accuracy = sum(current_results) / len(current_results)
    print(f"\n📊 CURRENT THRESHOLD ACCURACY: {current_accuracy:.1%}")
    
    # Test different threshold values
    print(f"\n🔬 TESTING DIFFERENT THRESHOLDS:")
    threshold_candidates = [0.10, 0.15, 0.25, 0.30, 0.35, 0.40, 0.50]
    
    best_threshold = 0.2
    best_accuracy = current_accuracy
    
    for threshold in threshold_candidates:
        energy_validator.divergence_threshold = threshold
        results = []
        
        for test in catastrophic_test_cases:
            detected = energy_validator.detect_energy_divergence(test['predicted'], test['actual'])
            correct = detected == test['expected_detection']
            results.append(correct)
        
        accuracy = sum(results) / len(results)
        print(f"   Threshold {threshold:.1%}: {accuracy:.1%} accuracy")
        
        if accuracy > best_accuracy:
            best_accuracy = accuracy
            best_threshold = threshold
    
    print(f"\n🎯 OPTIMAL THRESHOLD ANALYSIS:")
    print(f"   Best Threshold: {best_threshold:.1%}")
    print(f"   Best Accuracy: {best_accuracy:.1%}")
    print(f"   Improvement: {(best_accuracy - current_accuracy) * 100:.0f} percentage points")
    
    # Test the optimal threshold in detail
    print(f"\n🔍 DETAILED ANALYSIS WITH OPTIMAL THRESHOLD ({best_threshold:.1%}):")
    energy_validator.divergence_threshold = best_threshold
    
    for test in catastrophic_test_cases:
        detected = energy_validator.detect_energy_divergence(test['predicted'], test['actual'])
        correct = detected == test['expected_detection']
        
        status = "✅ CORRECT" if correct else "❌ WRONG"
        detection = "🚨 DETECTED" if detected else "✅ Normal"
        
        print(f"   {test['name']}:")
        print(f"      Predicted: {test['predicted']['contamination']:.1%}")
        print(f"      Actual: {test['actual']['contamination']:.1%}")
        print(f"      Error: {test['error_magnitude']:.1%}")
        print(f"      Result: {detection} ({status})")
    
    # Create calibrated configuration
    calibrated_config = {
        'divergence_threshold': best_threshold,
        'echo_threshold': 0.8,
        'calibration_source': 'August_5_PM_catastrophic_failure',
        'calibration_accuracy': best_accuracy,
        'test_cases_passed': sum([
            energy_validator.detect_energy_divergence(test['predicted'], test['actual']) == test['expected_detection']
            for test in catastrophic_test_cases
        ]),
        'total_test_cases': len(catastrophic_test_cases)
    }
    
    # Save calibrated configuration
    with open('/Users/<USER>/grok-claude-automation/project_oracle/validation/calibrated_energy_config.json', 'w') as f:
        json.dump(calibrated_config, f, indent=2)
    
    print(f"\n💾 CALIBRATED CONFIGURATION SAVED:")
    print(f"   File: validation/calibrated_energy_config.json")
    print(f"   Divergence Threshold: {best_threshold:.1%}")
    print(f"   Validation Accuracy: {best_accuracy:.1%}")
    print(f"   Test Cases Passed: {calibrated_config['test_cases_passed']}/{calibrated_config['total_test_cases']}")
    
    # Verify the catastrophic failure would now be detected
    august_5_detected = energy_validator.detect_energy_divergence(
        {'contamination': 0.35},  # Original Oracle prediction
        {'contamination': 0.89}   # Actual PM session
    )
    
    print(f"\n🚨 CATASTROPHIC FAILURE DETECTION TEST:")
    print(f"   Original Oracle: 35% contamination")
    print(f"   Actual PM Session: 89% contamination")
    print(f"   Error Magnitude: 54%")
    print(f"   Calibrated System: {'🚨 DETECTED' if august_5_detected else '❌ MISSED'}")
    
    if august_5_detected:
        print(f"   ✅ SUCCESS: Calibrated system would have caught the energy prediction failure!")
    else:
        print(f"   ❌ FAILURE: Even calibrated system misses the catastrophic error")
    
    return {
        'optimal_threshold': best_threshold,
        'accuracy': best_accuracy,
        'catastrophic_failure_detected': august_5_detected,
        'config_saved': True
    }

if __name__ == "__main__":
    results = calibrate_energy_thresholds()
    
    print("\n" + "=" * 60)
    print("🎯 ENERGY THRESHOLD CALIBRATION SUMMARY:")
    print(f"   🔧 Optimal Threshold: {results['optimal_threshold']:.1%}")
    print(f"   📊 Validation Accuracy: {results['accuracy']:.1%}")
    print(f"   🚨 Catastrophic Detection: {'✅ SUCCESS' if results['catastrophic_failure_detected'] else '❌ FAILED'}")
    print(f"   💾 Configuration Saved: {'✅ YES' if results['config_saved'] else '❌ NO'}")
    
    if results['catastrophic_failure_detected']:
        print(f"\n🎉 BREAKTHROUGH: Energy validation system successfully calibrated!")
        print(f"   The enhanced Three-Oracle system can now detect catastrophic energy failures.")
    else:
        print(f"\n🔧 FURTHER WORK NEEDED: Additional calibration required for full protection.")
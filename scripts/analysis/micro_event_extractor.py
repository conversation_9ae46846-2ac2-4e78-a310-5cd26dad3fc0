"""
Enhanced Micro-Event Extraction Engine - Critical Path to Production
===================================================================

Solves the critical data resolution bottleneck by extracting 15-20 micro-events per hour
from existing Level-1 session data, feeding the Fisher Information "Micro Brain" the 
granular event density it needs for crystallization detection.

Problem Solved:
- Current: 2-3 HTF events/hour → Fisher F≈20 → No crystallization detection
- Enhanced: 15+ micro-events/hour → Fisher F≈200+ → Crystallization detected

Architecture:
- Tier 1: liquidity_grab, stop_run (highest frequency, easiest detection)  
- Tier 2: micro_fpfvg, order_block_test (medium frequency)
- Tier 3: wick_rejection, momentum_shift (lower frequency, high signal)

Integration: Feeds directly into RG Scaler → Fisher Monitor → Crystallization Detection
"""

import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import logging
import re

@dataclass
class MicroEvent:
    """Enhanced micro-event with significance scoring"""
    timestamp: str
    event_id: str
    event_type: str
    price_level: float
    significance_score: float
    event_tier: int  # 1=highest frequency, 3=lowest frequency
    detection_method: str
    context: str
    session_source: str

@dataclass
class ExtractionResult:
    """Results from micro-event extraction"""
    micro_events: List[MicroEvent]
    total_events: int
    events_per_hour: float
    tier_breakdown: Dict[int, int]
    extraction_confidence: float
    session_coverage: str

class MicroEventExtractor:
    """
    Enhanced Micro-Event Extraction Engine
    
    Transforms sparse HTF event streams (2-3/hour) into dense micro-event streams (15-20/hour)
    by mining granular patterns from existing Level-1 session data.
    
    Critical Discovery: The Fisher Information Monitor was designed for dense event streams,
    but HTF pipeline only captures major events, starving the crystallization detector.
    
    Solution: Extract micro-events from price_movements, fpfvg_interactions, and session_liquidity_events
    """
    
    def __init__(self):
        """Initialize micro-event extraction engine"""
        
        # Event tier classification - based on frequency and detection ease
        self.event_tiers = {
            'tier_1': {
                'types': ['liquidity_grab', 'stop_run'],
                'frequency': 'high',
                'base_significance': 0.4,
                'detection_patterns': [
                    r'reversal_point',
                    r'expansion_(?:low|high)',
                    r'multiple_fpfvg.*(?:redelivery|interaction)',
                    r'sweep.*(?:high|low)'
                ]
            },
            'tier_2': {
                'types': ['micro_fpfvg', 'order_block_test'],
                'frequency': 'medium',
                'base_significance': 0.6,
                'detection_patterns': [
                    r'fpfvg.*(?:interaction|redelivery|rebalance)',
                    r'order_block.*(?:test|formation)',
                    r'consolidation.*(?:break|formation)',
                    r'range.*(?:break|test)'
                ]
            },
            'tier_3': {
                'types': ['wick_rejection', 'momentum_shift'],
                'frequency': 'low', 
                'base_significance': 0.8,
                'detection_patterns': [
                    r'rejection.*(?:wick|tail)',
                    r'momentum.*(?:shift|break)',
                    r'trend.*(?:change|reversal)',
                    r'structure.*(?:break|formation)'
                ]
            }
        }
        
        # Price movement type mappings to micro-events
        self.movement_mappings = {
            'liquidity_grab': [
                'expansion_start_lower_multiple_fpfvg_interaction',
                'expansion_low_reversal_point',
                'sweep_low_liquidity_grab',
                'session_low_sweep'
            ],
            'stop_run': [
                'stop_run_formation',
                'liquidity_sweep_completion', 
                'reversal_confirmation',
                'momentum_exhaustion'
            ],
            'micro_fpfvg': [
                'multiple_fpfvg_redelivery_cluster',
                'multiple_fpfvg_rebalance_cluster',
                'fpfvg_interaction',
                'fpfvg_formation'
            ],
            'order_block_test': [
                'order_block_formation',
                'demand_zone_test',
                'supply_zone_test',
                'institutional_level_test'
            ]
        }
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("🔬 MICRO-EVENT EXTRACTOR: Initialized for data resolution enhancement")
        
    def extract_from_price_movements(self, price_movements: List[Dict]) -> List[MicroEvent]:
        """
        Extract micro-events from price_movements array
        
        This is the primary source of micro-events - price_movements contains
        50+ granular events per session but current HTF pipeline only extracts
        2-3 major events.
        
        Args:
            price_movements: List of price movement entries from Level-1 data
            
        Returns:
            List of extracted micro-events
        """
        micro_events = []
        
        for i, movement in enumerate(price_movements):
            try:
                movement_type = movement.get('movement_type', '')
                timestamp = movement.get('timestamp', '')
                price_level = float(movement.get('price_level', 0))
                
                # Skip basic open/close events
                if movement_type in ['open', 'close', 'high', 'low']:
                    continue
                
                # Classify movement into micro-event type
                event_type, tier, significance = self._classify_movement_type(movement_type)
                
                if event_type:
                    event_id = f"micro_{event_type}_{timestamp.replace(':', '')}"
                    
                    micro_event = MicroEvent(
                        timestamp=timestamp,
                        event_id=event_id,
                        event_type=event_type,
                        price_level=price_level,
                        significance_score=significance,
                        event_tier=tier,
                        detection_method='price_movement_analysis',
                        context=movement_type,
                        session_source='price_movements'
                    )
                    
                    micro_events.append(micro_event)
                    
            except Exception as e:
                self.logger.warning(f"Error processing movement {i}: {e}")
                continue
        
        return micro_events
    
    def extract_from_fpfvg_interactions(self, fpfvg_data: Dict) -> List[MicroEvent]:
        """
        Extract micro-events from FPFVG interactions
        
        FPFVG interactions are high-significance micro-events that create
        crystallization patterns but were not being captured for Fisher analysis.
        
        Args:
            fpfvg_data: FPFVG session data from Level-1
            
        Returns:
            List of extracted micro-events from FPFVG interactions
        """
        micro_events = []
        
        if not fpfvg_data.get('fpfvg_present', False):
            return micro_events
        
        # Extract formation event
        formation = fpfvg_data.get('fpfvg_formation', {})
        if formation.get('formation_time'):
            formation_event = MicroEvent(
                timestamp=formation['formation_time'],
                event_id=f"micro_fpfvg_formation_{formation['formation_time'].replace(':', '')}",
                event_type='micro_fpfvg',
                price_level=float(formation.get('premium_high', 0)),
                significance_score=0.7,
                event_tier=2,
                detection_method='fpfvg_structure_analysis',
                context=f"FPFVG formation: gap_size={formation.get('gap_size', 0)}",
                session_source='fpfvg_data'
            )
            micro_events.append(formation_event)
        
        # Extract interaction events
        interactions = formation.get('interactions', [])
        for interaction in interactions:
            try:
                interaction_type = interaction.get('interaction_type', '')
                timestamp = interaction.get('timestamp') or interaction.get('interaction_time', '')
                price_level = float(interaction.get('price_level', 0))
                
                # Map interaction type to micro-event
                if interaction_type in ['redelivery', 'rebalance', 'touch', 'violation']:
                    event_type = 'micro_fpfvg'
                    significance = 0.6 if interaction_type == 'touch' else 0.75
                    
                    interaction_event = MicroEvent(
                        timestamp=timestamp,
                        event_id=f"micro_fpfvg_{interaction_type}_{timestamp.replace(':', '')}",
                        event_type=event_type,
                        price_level=price_level,
                        significance_score=significance,
                        event_tier=2,
                        detection_method='fpfvg_interaction_analysis',
                        context=f"FPFVG {interaction_type}: {interaction.get('interaction_context', '')}",
                        session_source='fpfvg_interactions'
                    )
                    
                    micro_events.append(interaction_event)
                    
            except Exception as e:
                self.logger.warning(f"Error processing FPFVG interaction: {e}")
                continue
        
        return micro_events
    
    def extract_from_liquidity_events(self, liquidity_events: List[Dict]) -> List[MicroEvent]:
        """
        Extract micro-events from session liquidity events
        
        Liquidity events are often stop_runs and liquidity_grabs that create
        the high-frequency patterns needed for crystallization detection.
        
        Args:
            liquidity_events: List of liquidity events from Level-1 data
            
        Returns:
            List of extracted micro-events from liquidity patterns
        """
        micro_events = []
        
        for event in liquidity_events:
            try:
                event_type = event.get('event_type', '')
                timestamp = event.get('timestamp', '')
                price_level = float(event.get('price_level', 0))
                
                # Classify liquidity event into micro-event
                micro_type, tier, significance = self._classify_liquidity_event(event_type)
                
                if micro_type:
                    event_id = f"micro_{micro_type}_{timestamp.replace(':', '')}"
                    
                    micro_event = MicroEvent(
                        timestamp=timestamp,
                        event_id=event_id,
                        event_type=micro_type,
                        price_level=price_level,
                        significance_score=significance,
                        event_tier=tier,
                        detection_method='liquidity_pattern_analysis',
                        context=event_type,
                        session_source='liquidity_events'
                    )
                    
                    micro_events.append(micro_event)
                    
            except Exception as e:
                self.logger.warning(f"Error processing liquidity event: {e}")
                continue
        
        return micro_events
    
    def _classify_movement_type(self, movement_type: str) -> Tuple[Optional[str], int, float]:
        """
        Classify price movement type into micro-event category
        
        Args:
            movement_type: Raw movement type from Level-1 data
            
        Returns:
            Tuple of (event_type, tier, significance_score)
        """
        movement_lower = movement_type.lower()
        
        # Check each tier for pattern matches
        for tier_name, tier_config in self.event_tiers.items():
            tier_num = int(tier_name.split('_')[1])
            
            for pattern in tier_config['detection_patterns']:
                if re.search(pattern, movement_lower):
                    # Determine specific event type
                    for event_type in tier_config['types']:
                        if self._matches_event_type(movement_lower, event_type):
                            significance = tier_config['base_significance']
                            
                            # Boost significance for specific high-value patterns
                            if 'multiple_fpfvg' in movement_lower:
                                significance += 0.1
                            if 'reversal_point' in movement_lower:
                                significance += 0.1
                                
                            return event_type, tier_num, min(1.0, significance)
        
        return None, 0, 0.0
    
    def _matches_event_type(self, movement: str, event_type: str) -> bool:
        """Check if movement matches specific event type"""
        
        type_indicators = {
            'liquidity_grab': ['grab', 'sweep', 'reversal', 'expansion'],
            'stop_run': ['stop', 'run', 'momentum', 'exhaustion'],
            'micro_fpfvg': ['fpfvg', 'gap', 'imbalance'],
            'order_block_test': ['block', 'test', 'zone', 'level'],
            'wick_rejection': ['rejection', 'wick', 'tail'],
            'momentum_shift': ['shift', 'break', 'change']
        }
        
        indicators = type_indicators.get(event_type, [])
        return any(indicator in movement for indicator in indicators)
    
    def _classify_liquidity_event(self, event_type: str) -> Tuple[Optional[str], int, float]:
        """Classify liquidity event into micro-event"""
        
        event_lower = event_type.lower()
        
        # Direct mapping for liquidity events
        if any(term in event_lower for term in ['sweep', 'grab', 'liquidity']):
            return 'liquidity_grab', 1, 0.5
        elif any(term in event_lower for term in ['stop', 'run', 'momentum']):
            return 'stop_run', 1, 0.5
        elif 'fpfvg' in event_lower:
            return 'micro_fpfvg', 2, 0.6
        elif any(term in event_lower for term in ['test', 'block', 'zone']):
            return 'order_block_test', 2, 0.6
            
        return None, 0, 0.0
    
    def extract_session_micro_events(self, session_data: Dict, session_type: str = "unknown") -> ExtractionResult:
        """
        Extract all micro-events from a complete Level-1 session
        
        This is the main extraction method that processes a complete session
        and extracts all micro-events for Fisher Information analysis.
        
        Args:
            session_data: Complete Level-1 session data
            session_type: Session type (ny_am, london, etc.)
            
        Returns:
            ExtractionResult with all micro-events and analysis
        """
        all_micro_events = []
        
        # Extract from price movements (primary source)
        price_movements = session_data.get('level1_json', {}).get('price_movements', [])
        if price_movements:
            movement_events = self.extract_from_price_movements(price_movements)
            all_micro_events.extend(movement_events)
            self.logger.info(f"   📊 Extracted {len(movement_events)} events from price movements")
        
        # Extract from FPFVG interactions  
        fpfvg_data = session_data.get('level1_json', {}).get('session_fpfvg', {})
        if fpfvg_data:
            fpfvg_events = self.extract_from_fpfvg_interactions(fpfvg_data)
            all_micro_events.extend(fpfvg_events)
            self.logger.info(f"   📊 Extracted {len(fpfvg_events)} events from FPFVG interactions")
        
        # Extract from liquidity events
        liquidity_events = session_data.get('level1_json', {}).get('session_liquidity_events', [])
        if liquidity_events:
            liquidity_micro_events = self.extract_from_liquidity_events(liquidity_events)
            all_micro_events.extend(liquidity_micro_events)
            self.logger.info(f"   📊 Extracted {len(liquidity_micro_events)} events from liquidity patterns")
        
        # Sort events by timestamp
        all_micro_events.sort(key=lambda x: x.timestamp)
        
        # Calculate extraction metrics
        total_events = len(all_micro_events)
        
        # Calculate events per hour
        if total_events > 0:
            session_metadata = session_data.get('level1_json', {}).get('session_metadata', {})
            session_duration = session_metadata.get('session_duration', 150)  # Default 2.5 hours
            events_per_hour = (total_events / session_duration) * 60
        else:
            events_per_hour = 0.0
        
        # Tier breakdown
        tier_breakdown = {1: 0, 2: 0, 3: 0}
        for event in all_micro_events:
            tier_breakdown[event.event_tier] += 1
        
        # Extraction confidence
        if events_per_hour >= 15:
            extraction_confidence = 1.0
        elif events_per_hour >= 10:
            extraction_confidence = 0.8
        elif events_per_hour >= 5:
            extraction_confidence = 0.6
        else:
            extraction_confidence = 0.4
        
        # Session coverage assessment
        if events_per_hour >= 15:
            session_coverage = "excellent_density"
        elif events_per_hour >= 10:
            session_coverage = "good_density"  
        elif events_per_hour >= 5:
            session_coverage = "moderate_density"
        else:
            session_coverage = "sparse_density"
        
        result = ExtractionResult(
            micro_events=all_micro_events,
            total_events=total_events,
            events_per_hour=events_per_hour,
            tier_breakdown=tier_breakdown,
            extraction_confidence=extraction_confidence,
            session_coverage=session_coverage
        )
        
        self.logger.info(f"🎯 EXTRACTION COMPLETE: {total_events} micro-events extracted")
        self.logger.info(f"   Events/Hour: {events_per_hour:.1f} (Target: ≥15)")
        self.logger.info(f"   Coverage: {session_coverage}")
        self.logger.info(f"   Tier Breakdown: T1={tier_breakdown[1]}, T2={tier_breakdown[2]}, T3={tier_breakdown[3]}")
        
        return result
    
    def convert_to_fisher_format(self, micro_events: List[MicroEvent]) -> np.ndarray:
        """
        Convert micro-events to format expected by Fisher Information Monitor
        
        Creates event count array suitable for RG Scaler → Fisher Monitor pipeline
        
        Args:
            micro_events: List of extracted micro-events
            
        Returns:
            Numpy array of event counts per time bin (1-minute bins)
        """
        if not micro_events:
            return np.array([])
        
        # Create time bins (1-minute resolution for high density)
        timestamps = [datetime.strptime(f"2025-08-06 {event.timestamp}", "%Y-%m-%d %H:%M:%S") 
                     for event in micro_events]
        
        if not timestamps:
            return np.array([])
        
        # Find session time range
        start_time = min(timestamps)
        end_time = max(timestamps)
        duration_minutes = int((end_time - start_time).total_seconds() / 60) + 1
        
        # Create 1-minute bins
        event_counts = np.zeros(duration_minutes)
        
        for i, event in enumerate(micro_events):
            timestamp = timestamps[i]
            bin_index = int((timestamp - start_time).total_seconds() / 60)
            
            if 0 <= bin_index < duration_minutes:
                # Weight by significance score
                event_counts[bin_index] += event.significance_score
        
        return event_counts


def create_micro_event_extractor() -> MicroEventExtractor:
    """Factory function for production micro-event extractor"""
    return MicroEventExtractor()


if __name__ == "__main__":
    """
    Test micro-event extraction on actual session data
    """
    print("🔬 MICRO-EVENT EXTRACTOR: Data Resolution Enhancement Testing")
    print("=" * 70)
    
    # Test on NYAM_2025-08-05_COMPLETE session
    test_session_path = Path("../data/sessions/level_1/NYAM_Lvl-1_2025_08_05_COMPLETE.json")
    
    if not test_session_path.exists():
        print(f"❌ Test session not found: {test_session_path}")
        print("   Please run from project_oracle directory")
        exit(1)
    
    # Load test session
    with open(test_session_path, 'r') as f:
        session_data = json.load(f)
    
    print(f"📁 Testing on: {test_session_path.name}")
    
    # Create extractor and process session
    extractor = create_micro_event_extractor()
    result = extractor.extract_session_micro_events(session_data, "ny_am")
    
    print(f"\n🎯 EXTRACTION RESULTS:")
    print(f"   Total Micro-Events: {result.total_events}")
    print(f"   Events per Hour: {result.events_per_hour:.1f}")
    print(f"   Session Coverage: {result.session_coverage}")
    print(f"   Extraction Confidence: {result.extraction_confidence:.3f}")
    
    print(f"\n📊 TIER BREAKDOWN:")
    for tier, count in result.tier_breakdown.items():
        tier_types = extractor.event_tiers[f'tier_{tier}']['types']
        print(f"   Tier {tier} ({', '.join(tier_types)}): {count} events")
    
    # Show sample events
    print(f"\n🔍 SAMPLE MICRO-EVENTS:")
    for i, event in enumerate(result.micro_events[:5]):
        print(f"   {i+1}. {event.timestamp} - {event.event_type} (sig: {event.significance_score:.2f})")
        print(f"      Context: {event.context}")
    
    # Test Fisher format conversion
    fisher_array = extractor.convert_to_fisher_format(result.micro_events)
    print(f"\n🧠 FISHER INFORMATION INPUT:")
    print(f"   Array Shape: {fisher_array.shape}")
    print(f"   Total Event Weight: {np.sum(fisher_array):.2f}")
    print(f"   Non-Zero Bins: {np.count_nonzero(fisher_array)}")
    
    # Success assessment
    if result.events_per_hour >= 15:
        print(f"\n✅ SUCCESS: Event density target achieved ({result.events_per_hour:.1f} ≥ 15 events/hour)")
        print("   Fisher Information Monitor will receive adequate micro-event density")
    else:
        print(f"\n⚠️ PARTIAL: Event density below target ({result.events_per_hour:.1f} < 15 events/hour)")
        print("   May need additional micro-event detection patterns")
    
    print(f"\n🚀 MICRO-EVENT EXTRACTOR: Testing complete")
    print("   Ready for Phase 2: Fisher Information Monitor integration")
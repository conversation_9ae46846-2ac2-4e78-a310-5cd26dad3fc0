"""
RG_graph Multi-Timeframe Cycle Analysis Engine
Advanced analysis of RG_graph patterns across multiple timeframes with focus on 24-minute cycle phenomenon

Integrates with existing RG Scaler and cascade prediction system to detect and validate
periodic patterns that reduce randomness and enhance predictive accuracy.
"""

import numpy as np
import json
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from typing import Dict, <PERSON>, Tuple, Any, Optional
from dataclasses import dataclass
from scipy import signal
from scipy.fft import fft, fftfreq
from scipy.stats import entropy
import logging

# Import existing system components
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'rg_scaler'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'core_predictor'))

@dataclass
class CycleAnalysisResult:
    """Results from cycle detection and analysis"""
    cycle_period_minutes: float
    cycle_strength: float  # 0-1 scale
    randomness_reduction: float  # Percentage reduction in randomness
    phase_predictions: Dict[str, float]  # Predictions by cycle phase
    statistical_significance: float  # p-value
    timeframe_analysis: Dict[str, Any]  # Multi-timeframe results
    integration_recommendations: List[str]

@dataclass  
class MultiTimeframeData:
    """Price and event data across multiple timeframes"""
    minute_data: List[Dict[str, Any]]
    five_minute_data: List[Dict[str, Any]]
    fifteen_minute_data: List[Dict[str, Any]]
    hourly_data: List[Dict[str, Any]]
    session_metadata: Dict[str, Any]

class CycleDetectionEngine:
    """
    Advanced cycle detection engine for RG_graph multi-timeframe analysis
    Focuses on 24-minute cycle phenomenon with randomness drop detection
    """
    
    def __init__(self, target_cycle_minutes: float = 24.0):
        """
        Initialize cycle detection engine
        
        Args:
            target_cycle_minutes: Primary cycle period to investigate
        """
        self.target_cycle = target_cycle_minutes
        self.analysis_window = target_cycle_minutes * 4  # 96-minute window
        self.min_cycles_for_validation = 3  # Minimum cycles for statistical significance
        
        # Timeframe configurations
        self.timeframes = {
            '1min': {'resolution': 1, 'context_window': 120},
            '5min': {'resolution': 5, 'context_window': 300}, 
            '15min': {'resolution': 15, 'context_window': 600},
            '1hour': {'resolution': 60, 'context_window': 1440}
        }
        
        # Statistical thresholds
        self.significance_threshold = 0.05  # p < 0.05 for statistical significance
        self.cycle_strength_threshold = 0.3  # Minimum strength for valid cycle
        self.randomness_threshold = 0.1  # Minimum randomness reduction for relevance
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"🔄 Cycle Detection Engine initialized (target: {target_cycle_minutes} min)")
        
    def analyze_session_cycles(self, session_data: Dict[str, Any]) -> CycleAnalysisResult:
        """
        Comprehensive cycle analysis of session data across multiple timeframes
        
        Args:
            session_data: Complete session data with price movements and events
            
        Returns:
            CycleAnalysisResult with comprehensive cycle analysis
        """
        
        self.logger.info("🔍 COMPREHENSIVE CYCLE ANALYSIS STARTING")
        self.logger.info("=" * 60)
        
        # Extract multi-timeframe data
        multi_tf_data = self._prepare_multiframe_data(session_data)
        
        # Phase 1: Spectral Analysis for Cycle Detection
        self.logger.info("1️⃣ Spectral Analysis - Cycle Detection")
        spectral_results = self._perform_spectral_analysis(multi_tf_data)
        
        # Phase 2: Randomness Drop Detection
        self.logger.info("2️⃣ Randomness Analysis - Pattern Detection")
        randomness_results = self._analyze_randomness_patterns(multi_tf_data)
        
        # Phase 3: Phase-Based Prediction Analysis
        self.logger.info("3️⃣ Phase Analysis - Predictability Assessment")
        phase_results = self._analyze_cycle_phases(multi_tf_data, spectral_results)
        
        # Phase 4: Statistical Validation
        self.logger.info("4️⃣ Statistical Validation - Significance Testing")
        statistical_results = self._validate_statistical_significance(spectral_results, randomness_results)
        
        # Phase 5: Integration Assessment
        self.logger.info("5️⃣ Integration Assessment - System Enhancement")
        integration_analysis = self._assess_integration_potential(
            spectral_results, randomness_results, phase_results, statistical_results
        )
        
        # Compile comprehensive results
        cycle_result = CycleAnalysisResult(
            cycle_period_minutes=spectral_results.get('dominant_period', self.target_cycle),
            cycle_strength=spectral_results.get('cycle_strength', 0.0),
            randomness_reduction=randomness_results.get('reduction_percentage', 0.0),
            phase_predictions=phase_results.get('phase_predictions', {}),
            statistical_significance=statistical_results.get('p_value', 1.0),
            timeframe_analysis=spectral_results.get('timeframe_results', {}),
            integration_recommendations=integration_analysis.get('recommendations', [])
        )
        
        # Log comprehensive results
        self._log_analysis_results(cycle_result)
        
        return cycle_result
    
    def _prepare_multiframe_data(self, session_data: Dict[str, Any]) -> MultiTimeframeData:
        """Prepare data across multiple timeframes for analysis"""
        
        # Extract base price and event data
        events = session_data.get('micro_timing_analysis', {}).get('cascade_events', [])
        price_data = session_data.get('price_data', {})
        
        # Convert events to time series data
        minute_data = self._convert_to_timeseries(events, resolution=1)
        five_minute_data = self._convert_to_timeseries(events, resolution=5)  
        fifteen_minute_data = self._convert_to_timeseries(events, resolution=15)
        hourly_data = self._convert_to_timeseries(events, resolution=60)
        
        return MultiTimeframeData(
            minute_data=minute_data,
            five_minute_data=five_minute_data,
            fifteen_minute_data=fifteen_minute_data,
            hourly_data=hourly_data,
            session_metadata=session_data.get('session_metadata', {})
        )
    
    def _convert_to_timeseries(self, events: List[Dict], resolution: int) -> List[Dict[str, Any]]:
        """Convert events to time series with specified resolution"""
        
        if not events:
            return []
            
        # Create time bins
        session_start = 0  # Start of session
        session_duration = 480  # 8 hours in minutes
        num_bins = session_duration // resolution
        
        time_series = []
        for i in range(num_bins):
            bin_start = session_start + (i * resolution)
            bin_end = bin_start + resolution
            
            # Count events in this time bin
            bin_events = []
            total_magnitude = 0.0
            price_movement = 0.0
            
            for event in events:
                event_time = self._parse_time_to_minutes(event.get('timestamp', '00:00'))
                if bin_start <= event_time < bin_end:
                    bin_events.append(event)
                    total_magnitude += event.get('magnitude', 1.0)
                    
            # Calculate bin metrics
            bin_data = {
                'bin_start_minutes': bin_start,
                'bin_end_minutes': bin_end,
                'event_count': len(bin_events),
                'total_magnitude': total_magnitude,
                'average_magnitude': total_magnitude / len(bin_events) if bin_events else 0,
                'event_density': len(bin_events) / resolution,
                'bin_resolution': resolution
            }
            
            time_series.append(bin_data)
            
        return time_series
    
    def _perform_spectral_analysis(self, data: MultiTimeframeData) -> Dict[str, Any]:
        """Perform spectral analysis to detect periodic patterns"""
        
        results = {
            'timeframe_results': {},
            'dominant_period': None,
            'cycle_strength': 0.0,
            'harmonics_detected': []
        }
        
        for tf_name, tf_data in [
            ('1min', data.minute_data),
            ('5min', data.five_minute_data), 
            ('15min', data.fifteen_minute_data),
            ('1hour', data.hourly_data)
        ]:
            if not tf_data:
                continue
                
            # Extract event density signal
            signal_data = [point['event_density'] for point in tf_data]
            if len(signal_data) < 10:  # Insufficient data
                continue
                
            # Perform FFT analysis
            fft_result = fft(signal_data)
            freqs = fftfreq(len(signal_data), d=tf_data[0]['bin_resolution'])
            
            # Convert frequencies to periods (in minutes)
            periods = []
            magnitudes = []
            
            for i, freq in enumerate(freqs):
                if freq > 0:  # Only positive frequencies
                    period = 1.0 / freq  # Period in time units
                    magnitude = abs(fft_result[i])
                    periods.append(period)
                    magnitudes.append(magnitude)
            
            # Find dominant periods near target cycle
            target_tolerance = 0.2  # ±20% tolerance
            target_min = self.target_cycle * (1 - target_tolerance)
            target_max = self.target_cycle * (1 + target_tolerance)
            
            relevant_periods = []
            relevant_magnitudes = []
            
            for period, magnitude in zip(periods, magnitudes):
                if target_min <= period <= target_max:
                    relevant_periods.append(period)
                    relevant_magnitudes.append(magnitude)
            
            # Store timeframe results
            tf_result = {
                'resolution_minutes': tf_data[0]['bin_resolution'],
                'signal_length': len(signal_data),
                'dominant_periods': relevant_periods[:5] if relevant_periods else [],
                'period_strengths': relevant_magnitudes[:5] if relevant_magnitudes else [],
                'total_cycles_detected': len(relevant_periods)
            }
            
            results['timeframe_results'][tf_name] = tf_result
            
            # Update global dominant period
            if relevant_magnitudes and max(relevant_magnitudes) > results['cycle_strength']:
                max_idx = relevant_magnitudes.index(max(relevant_magnitudes))
                results['dominant_period'] = relevant_periods[max_idx]
                results['cycle_strength'] = max(relevant_magnitudes)
        
        return results
    
    def _analyze_randomness_patterns(self, data: MultiTimeframeData) -> Dict[str, Any]:
        """Analyze randomness reduction during cycle periods"""
        
        results = {
            'baseline_entropy': 0.0,
            'cycle_entropy': 0.0,
            'reduction_percentage': 0.0,
            'timeframe_entropy': {}
        }
        
        for tf_name, tf_data in [
            ('1min', data.minute_data),
            ('5min', data.five_minute_data),
            ('15min', data.fifteen_minute_data)
        ]:
            if not tf_data or len(tf_data) < 20:
                continue
                
            # Extract event patterns
            event_counts = [point['event_count'] for point in tf_data]
            magnitudes = [point['total_magnitude'] for point in tf_data]
            
            # Calculate baseline entropy (entire session)
            if event_counts:
                # Normalize to create probability distribution
                total_events = sum(event_counts)
                if total_events > 0:
                    event_probs = [count / total_events for count in event_counts]
                    baseline_entropy = entropy(event_probs)
                else:
                    baseline_entropy = 0.0
            else:
                baseline_entropy = 0.0
            
            # Calculate cycle-specific entropy
            cycle_bins = self._identify_cycle_bins(tf_data, self.target_cycle)
            cycle_entropy = 0.0
            
            if cycle_bins:
                cycle_event_counts = [tf_data[i]['event_count'] for i in cycle_bins]
                total_cycle_events = sum(cycle_event_counts)
                
                if total_cycle_events > 0:
                    cycle_probs = [count / total_cycle_events for count in cycle_event_counts]
                    cycle_entropy = entropy(cycle_probs)
            
            # Calculate reduction percentage
            if baseline_entropy > 0:
                reduction = ((baseline_entropy - cycle_entropy) / baseline_entropy) * 100
            else:
                reduction = 0.0
                
            results['timeframe_entropy'][tf_name] = {
                'baseline_entropy': baseline_entropy,
                'cycle_entropy': cycle_entropy,
                'reduction_percentage': reduction,
                'cycle_bins_analyzed': len(cycle_bins) if cycle_bins else 0
            }
            
            # Update global results with best reduction
            if reduction > results['reduction_percentage']:
                results['baseline_entropy'] = baseline_entropy
                results['cycle_entropy'] = cycle_entropy
                results['reduction_percentage'] = reduction
        
        return results
    
    def _identify_cycle_bins(self, tf_data: List[Dict], cycle_period: float) -> List[int]:
        """Identify time bins that correspond to cycle periods"""
        
        if not tf_data:
            return []
            
        resolution = tf_data[0]['bin_resolution']
        bins_per_cycle = int(cycle_period / resolution)
        
        cycle_bins = []
        
        # Identify repeating patterns based on cycle period
        for start_idx in range(0, len(tf_data), bins_per_cycle):
            end_idx = min(start_idx + bins_per_cycle, len(tf_data))
            cycle_bins.extend(range(start_idx, end_idx))
            
        return cycle_bins
    
    def _analyze_cycle_phases(self, data: MultiTimeframeData, spectral_results: Dict) -> Dict[str, Any]:
        """Analyze predictability during different cycle phases"""
        
        results = {
            'phase_predictions': {},
            'phase_accuracy': {},
            'optimal_phases': []
        }
        
        dominant_period = spectral_results.get('dominant_period')
        if not dominant_period:
            return results
            
        # Analyze 1-minute data for phase detection
        if not data.minute_data:
            return results
            
        # Divide cycle into 4 phases: Early (0-25%), Mid-Early (25-50%), Mid-Late (50-75%), Late (75-100%)
        phase_names = ['early', 'mid_early', 'mid_late', 'late']
        phase_boundaries = [0.25, 0.5, 0.75, 1.0]
        
        cycle_length_bins = int(dominant_period / 1)  # 1-minute resolution
        
        for phase_idx, (phase_name, boundary) in enumerate(zip(phase_names, phase_boundaries)):
            phase_start = int(cycle_length_bins * (boundary - 0.25)) if phase_idx > 0 else 0
            phase_end = int(cycle_length_bins * boundary)
            
            phase_events = []
            phase_magnitudes = []
            
            # Extract events from this phase across all cycles
            for cycle_start in range(0, len(data.minute_data), cycle_length_bins):
                for bin_idx in range(phase_start, min(phase_end, cycle_length_bins)):
                    global_idx = cycle_start + bin_idx
                    if global_idx < len(data.minute_data):
                        bin_data = data.minute_data[global_idx]
                        phase_events.append(bin_data['event_count'])
                        phase_magnitudes.append(bin_data['total_magnitude'])
            
            # Calculate phase predictions
            if phase_events:
                avg_events = np.mean(phase_events)
                avg_magnitude = np.mean(phase_magnitudes)
                predictability = np.std(phase_events) / (np.mean(phase_events) + 1e-6)  # Inverse of coefficient of variation
                
                results['phase_predictions'][phase_name] = {
                    'average_events': avg_events,
                    'average_magnitude': avg_magnitude,
                    'predictability_score': 1.0 / (1.0 + predictability),  # Higher score = more predictable
                    'sample_size': len(phase_events)
                }
        
        # Identify optimal phases (highest predictability)
        if results['phase_predictions']:
            sorted_phases = sorted(
                results['phase_predictions'].items(),
                key=lambda x: x[1]['predictability_score'],
                reverse=True
            )
            results['optimal_phases'] = [phase[0] for phase in sorted_phases[:2]]  # Top 2 phases
        
        return results
    
    def _validate_statistical_significance(self, spectral_results: Dict, 
                                        randomness_results: Dict) -> Dict[str, Any]:
        """Validate statistical significance of detected patterns"""
        
        results = {
            'p_value': 1.0,
            'confidence_level': 0.0,
            'effect_size': 0.0,
            'validation_status': 'insufficient_data'
        }
        
        # Check if we have sufficient data for validation
        cycle_strength = spectral_results.get('cycle_strength', 0.0)
        randomness_reduction = randomness_results.get('reduction_percentage', 0.0)
        
        if cycle_strength < self.cycle_strength_threshold:
            results['validation_status'] = 'weak_signal'
            return results
            
        if randomness_reduction < self.randomness_threshold:
            results['validation_status'] = 'minimal_effect'
            return results
        
        # Simple significance testing based on effect size
        effect_size = min(cycle_strength, randomness_reduction / 100.0)
        
        # Heuristic p-value calculation (in production, use proper statistical tests)
        if effect_size > 0.5:
            p_value = 0.01  # Highly significant
        elif effect_size > 0.3:
            p_value = 0.025  # Significant  
        elif effect_size > 0.1:
            p_value = 0.05  # Marginally significant
        else:
            p_value = 0.1  # Not significant
        
        results['p_value'] = p_value
        results['confidence_level'] = 1.0 - p_value
        results['effect_size'] = effect_size
        results['validation_status'] = 'significant' if p_value < self.significance_threshold else 'not_significant'
        
        return results
    
    def _assess_integration_potential(self, spectral_results: Dict, randomness_results: Dict,
                                    phase_results: Dict, statistical_results: Dict) -> Dict[str, Any]:
        """Assess potential for integrating cycle analysis into existing system"""
        
        integration_analysis = {
            'integration_score': 0.0,
            'recommendations': [],
            'enhancement_potential': {},
            'implementation_priority': 'low'
        }
        
        # Calculate integration score based on multiple factors
        factors = {
            'cycle_strength': spectral_results.get('cycle_strength', 0.0),
            'randomness_reduction': randomness_results.get('reduction_percentage', 0.0) / 100.0,
            'statistical_significance': 1.0 - statistical_results.get('p_value', 1.0),
            'phase_predictability': self._calculate_average_predictability(phase_results)
        }
        
        # Weighted integration score
        weights = {'cycle_strength': 0.3, 'randomness_reduction': 0.3, 'statistical_significance': 0.2, 'phase_predictability': 0.2}
        integration_score = sum(factors[factor] * weights[factor] for factor in factors)
        
        integration_analysis['integration_score'] = integration_score
        
        # Generate recommendations based on score
        if integration_score > 0.7:
            integration_analysis['implementation_priority'] = 'high'
            integration_analysis['recommendations'].extend([
                "Implement cycle-enhanced RG scaling formula",
                "Add 24-minute cycle detection to Temporal Correlator", 
                "Create cycle-aware parameter optimization in VQE framework",
                "Integrate cycle phase analysis into Three-Oracle decision making"
            ])
        elif integration_score > 0.5:
            integration_analysis['implementation_priority'] = 'medium'
            integration_analysis['recommendations'].extend([
                "Add cycle detection as experimental feature",
                "Validate cycle patterns across more session data",
                "Implement cycle monitoring for pattern confirmation"
            ])
        else:
            integration_analysis['implementation_priority'] = 'low'
            integration_analysis['recommendations'].extend([
                "Continue investigating cycle patterns with more data",
                "Focus on improving base RG scaling accuracy first"
            ])
        
        # Enhancement potential estimates
        if integration_score > 0.5:
            integration_analysis['enhancement_potential'] = {
                'accuracy_improvement_estimate': f"{integration_score * 5:.1f}%",  # Conservative estimate
                'timing_precision_improvement': f"{integration_score * 20:.0f}%",
                'confidence_boost_potential': f"{integration_score * 15:.0f}%"
            }
            
        return integration_analysis
    
    def _calculate_average_predictability(self, phase_results: Dict) -> float:
        """Calculate average predictability across cycle phases"""
        
        phase_predictions = phase_results.get('phase_predictions', {})
        if not phase_predictions:
            return 0.0
            
        predictability_scores = [
            phase_data['predictability_score'] 
            for phase_data in phase_predictions.values()
        ]
        
        return np.mean(predictability_scores) if predictability_scores else 0.0
    
    def _parse_time_to_minutes(self, timestamp: str) -> float:
        """Parse timestamp to minutes from start of session"""
        try:
            if ':' in timestamp:
                parts = timestamp.split(':')
                hours = int(parts[0])
                minutes = int(parts[1])
                seconds = int(parts[2]) if len(parts) > 2 else 0
                # Convert to minutes from start of day, then normalize to session start (9:30 AM = 570 min)
                total_minutes = hours * 60 + minutes + seconds / 60.0
                return total_minutes - 570  # Subtract 9:30 AM to get session-relative time
            else:
                return float(timestamp)
        except:
            return 0.0
    
    def _log_analysis_results(self, result: CycleAnalysisResult):
        """Log comprehensive analysis results"""
        
        self.logger.info("🎯 CYCLE ANALYSIS RESULTS SUMMARY")
        self.logger.info("=" * 50)
        self.logger.info(f"🔄 Dominant Cycle Period: {result.cycle_period_minutes:.1f} minutes")
        self.logger.info(f"💪 Cycle Strength: {result.cycle_strength:.3f}")
        self.logger.info(f"📉 Randomness Reduction: {result.randomness_reduction:.1f}%")
        self.logger.info(f"📊 Statistical Significance: p = {result.statistical_significance:.4f}")
        
        if result.phase_predictions:
            self.logger.info("\n🔍 PHASE ANALYSIS:")
            for phase, predictions in result.phase_predictions.items():
                self.logger.info(f"   {phase.title()}: Predictability {predictions['predictability_score']:.3f}")
        
        if result.timeframe_analysis:
            self.logger.info("\n📅 TIMEFRAME ANALYSIS:")
            for tf, analysis in result.timeframe_analysis.items():
                if 'dominant_periods' in analysis and analysis['dominant_periods']:
                    periods = analysis['dominant_periods'][:3]
                    self.logger.info(f"   {tf}: {len(periods)} cycles detected ({periods})")
        
        self.logger.info("\n🚀 INTEGRATION RECOMMENDATIONS:")
        for rec in result.integration_recommendations:
            self.logger.info(f"   • {rec}")

# Factory function for production use
def create_cycle_detection_engine(target_cycle_minutes: float = 24.0) -> CycleDetectionEngine:
    """Create production-ready cycle detection engine"""
    return CycleDetectionEngine(target_cycle_minutes)

if __name__ == "__main__":
    # Demo and test the cycle detection system
    print("🔄 RG_GRAPH MULTI-TIMEFRAME CYCLE ANALYZER")
    print("=" * 60)
    
    # Create sample session data for testing
    sample_session_data = {
        'session_metadata': {
            'session_type': 'ny_pm',
            'session_duration': 159,
            'date': '2025-08-05'
        },
        'micro_timing_analysis': {
            'cascade_events': [
                {'timestamp': '13:31:00', 'price_level': 23208.75, 'event_type': 'fpfvg_formation', 'magnitude': 0.8},
                {'timestamp': '13:55:00', 'price_level': 23225.25, 'event_type': 'session_high_takeout', 'magnitude': 1.6},  # 24 min later
                {'timestamp': '14:19:00', 'price_level': 23252.0, 'event_type': 'reversal_point', 'magnitude': 2.1},      # 24 min later
                {'timestamp': '14:43:00', 'price_level': 23153.75, 'event_type': 'rebalance', 'magnitude': 1.9},           # 24 min later
                {'timestamp': '15:07:00', 'price_level': 23115.0, 'event_type': 'session_low', 'magnitude': 2.3}           # 24 min later
            ]
        },
        'price_data': {
            'session_high': 23252.0,
            'session_low': 23115.0,
            'session_range': 137.0
        }
    }
    
    # Initialize cycle detection engine  
    cycle_engine = create_cycle_detection_engine(target_cycle_minutes=24.0)
    
    # Perform comprehensive analysis
    results = cycle_engine.analyze_session_cycles(sample_session_data)
    
    print(f"\n🎉 CYCLE ANALYSIS DEMONSTRATION COMPLETE")
    print(f"   Integration Score: {results.statistical_significance:.1%}")
    print(f"   Cycle Detection: {'✅ Success' if results.cycle_strength > 0.1 else '❌ No significant cycle'}")
    print(f"   Randomness Reduction: {results.randomness_reduction:.1f}%")
    print(f"   Integration Priority: {len(results.integration_recommendations)} recommendations")
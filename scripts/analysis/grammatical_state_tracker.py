#!/usr/bin/env python3
"""
Real-Time Grammatical State Tracking System
==========================================

Tracks the current state of cascade pattern recognition using Type-2 CFG
pushdown automaton with real-time event processing and state persistence.

Features:
- PDA state tracking for cascade grammar
- Real-time event processing pipeline
- State persistence and recovery
- Confidence scoring based on grammatical completeness
- Integration with production oracle system
"""

import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from collections import deque
import numpy as np

@dataclass
class GrammaticalState:
    """Current state of the Type-2 CFG pushdown automaton"""
    stack: List[str]
    current_state: str
    pattern_buffer: List[Dict]
    confidence: float
    last_event_time: datetime
    pattern_completeness: float
    expected_next_events: List[str]
    timeout_threshold: float = 300.0  # 5 minutes

    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON serialization"""
        result = asdict(self)
        result['last_event_time'] = self.last_event_time.isoformat()
        return result
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'GrammaticalState':
        """Create from dictionary (JSON deserialization)"""
        data['last_event_time'] = datetime.fromisoformat(data['last_event_time'])
        return cls(**data)

class GrammaticalStateTracker:
    """Real-time Type-2 CFG state tracking system"""
    
    def __init__(self, state_file: str = "grammatical_state.json"):
        self.state_file = state_file
        self.cascade_grammar = self._init_cascade_grammar()
        self.state_history = deque(maxlen=100)
        
        # Initialize or load state
        self.current_state = self._load_or_init_state()
        
        print("🧠 Grammatical State Tracker initialized")
        print(f"   State file: {state_file}")
        print(f"   Grammar rules: {len(self.cascade_grammar)} productions")
    
    def _init_cascade_grammar(self) -> Dict[str, List[Dict]]:
        """Initialize Type-2 CFG cascade grammar rules"""
        return {
            # Start rule - always expands to expansion phase
            'START': [
                {
                    'production': ['EXPANSION_PHASE'],
                    'confidence': 0.8,
                    'next_expected': ['expansion_high', 'liquidity_grab'],
                    'type': 'expand'
                }
            ],
            
            # Expansion phase - can start with either expansion_high or liquidity_grab
            'EXPANSION_PHASE': [
                {
                    'production': ['expansion_high'],
                    'confidence': 0.9,
                    'next_expected': ['liquidity_grab'],
                    'type': 'terminal',
                    'match': 'expansion_high'
                },
                {
                    'production': ['liquidity_grab'],
                    'confidence': 0.85,
                    'next_expected': ['consolidation_break'],
                    'type': 'terminal',
                    'match': 'liquidity_grab'
                }
            ],
            
            # After expansion_high, expect liquidity_grab
            'LIQUIDITY_GRAB': [
                {
                    'production': ['liquidity_grab'],
                    'confidence': 0.85,
                    'next_expected': ['consolidation_break'],
                    'type': 'terminal',
                    'match': 'liquidity_grab'
                }
            ],
            
            # After liquidity_grab, expect consolidation_break
            'CONSOLIDATION_BREAK': [
                {
                    'production': ['consolidation_break'],
                    'confidence': 0.85,
                    'next_expected': ['cascade_trigger'],
                    'type': 'terminal',
                    'match': 'consolidation_break'
                }
            ],
            
            # Final trigger
            'CASCADE_TRIGGER': [
                {
                    'production': ['cascade_trigger'],
                    'confidence': 0.95,
                    'next_expected': ['ACCEPT'],
                    'type': 'terminal',
                    'match': 'cascade_trigger'
                }
            ]
        }
    
    def _load_or_init_state(self) -> GrammaticalState:
        """Load existing state or initialize fresh state"""
        try:
            with open(self.state_file, 'r') as f:
                state_data = json.load(f)
            
            state = GrammaticalState.from_dict(state_data)
            print(f"📂 Loaded existing grammatical state from {self.state_file}")
            print(f"   Current state: {state.current_state}")
            print(f"   Stack depth: {len(state.stack)}")
            print(f"   Pattern completeness: {state.pattern_completeness:.1%}")
            
            return state
            
        except (FileNotFoundError, json.JSONDecodeError, KeyError):
            print("🆕 Initializing fresh grammatical state")
            return GrammaticalState(
                stack=[],
                current_state='START',
                pattern_buffer=[],
                confidence=0.0,
                last_event_time=datetime.now(),
                pattern_completeness=0.0,
                expected_next_events=['expansion_high', 'liquidity_grab']
            )
    
    def _save_state(self):
        """Persist current state to disk"""
        try:
            with open(self.state_file, 'w') as f:
                json.dump(self.current_state.to_dict(), f, indent=2)
        except Exception as e:
            print(f"⚠️ Failed to save state: {e}")
    
    def process_event(self, event: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single market event through the grammatical state machine"""
        event_type = event.get('type', '').lower()
        event_time = datetime.now()
        
        # Check for timeout
        if self._check_timeout(event_time):
            print(f"⏰ Pattern timeout - resetting state")
            self._reset_state()
        
        # Save previous state for rollback
        previous_state = GrammaticalState(**asdict(self.current_state))
        previous_state.last_event_time = self.current_state.last_event_time
        
        # Process event through PDA
        transition_result = self._process_pda_transition(event_type, event)
        
        if transition_result['accepted']:
            # Update state
            self.current_state.last_event_time = event_time
            self.current_state.pattern_buffer.append(event)
            self.current_state.confidence = transition_result['confidence']
            self.current_state.pattern_completeness = transition_result['completeness']
            self.current_state.expected_next_events = transition_result['next_expected']
            
            # Archive state change
            self.state_history.append({
                'timestamp': event_time.isoformat(),
                'event': event,
                'previous_state': previous_state.current_state,
                'new_state': self.current_state.current_state,
                'confidence_change': transition_result['confidence'] - previous_state.confidence,
                'stack_operation': transition_result['stack_operation']
            })
            
            print(f"✅ Event accepted: {event_type}")
            print(f"   State: {previous_state.current_state} → {self.current_state.current_state}")
            print(f"   Confidence: {self.current_state.confidence:.1%}")
            print(f"   Completeness: {self.current_state.pattern_completeness:.1%}")
            
        else:
            print(f"❌ Event rejected: {event_type}")
            print(f"   Expected: {self.current_state.expected_next_events}")
            print(f"   Current state: {self.current_state.current_state}")
        
        # Save state
        self._save_state()
        
        return {
            'event_accepted': transition_result['accepted'],
            'current_state': self.current_state.current_state,
            'confidence': self.current_state.confidence,
            'pattern_completeness': self.current_state.pattern_completeness,
            'expected_next_events': self.current_state.expected_next_events,
            'cascade_probability': self._calculate_cascade_probability(),
            'time_to_completion': self._estimate_time_to_completion(),
            'stack_depth': len(self.current_state.stack)
        }
    
    def _process_pda_transition(self, event_type: str, event: Dict) -> Dict[str, Any]:
        """Process pushdown automaton transition for given event"""
        current_state = self.current_state.current_state
        
        # Check if current state has applicable productions
        if current_state not in self.cascade_grammar:
            return {
                'accepted': False,
                'confidence': 0.0,
                'completeness': 0.0,
                'next_expected': [],
                'stack_operation': 'reject'
            }
        
        # Try each production rule
        for production in self.cascade_grammar[current_state]:
            if self._matches_production(event_type, production):
                # Apply production
                result = self._apply_production(production, event)
                return result
        
        # No matching production found
        return {
            'accepted': False,
            'confidence': self.current_state.confidence * 0.9,  # Decay confidence
            'completeness': self.current_state.pattern_completeness,
            'next_expected': self.current_state.expected_next_events,
            'stack_operation': 'reject'
        }
    
    def _matches_production(self, event_type: str, production: Dict) -> bool:
        """Check if event matches a production rule"""
        rule_type = production.get('type', 'expand')
        
        if rule_type == 'terminal':
            # Terminal rule must match exact event
            match_event = production.get('match', '')
            return event_type == match_event
        elif rule_type == 'expand':
            # Expansion rule always matches (no input consumed)
            return True
        
        return False
    
    def _apply_production(self, production: Dict, event: Dict) -> Dict[str, Any]:
        """Apply a production rule and update PDA state"""
        rule_type = production.get('type', 'expand')
        symbols = production['production']
        
        if rule_type == 'terminal':
            # Terminal production - consume input and transition to next state
            next_states = production['next_expected']
            
            if 'ACCEPT' in next_states:
                self.current_state.current_state = 'ACCEPT'
            else:
                # Map event to next state based on next_expected
                next_state_map = {
                    'liquidity_grab': 'LIQUIDITY_GRAB',
                    'consolidation_break': 'CONSOLIDATION_BREAK', 
                    'cascade_trigger': 'CASCADE_TRIGGER'
                }
                
                # Find next state from expected events
                next_state = 'UNKNOWN'
                for expected in next_states:
                    if expected in next_state_map:
                        next_state = next_state_map[expected]
                        break
                
                self.current_state.current_state = next_state
            
            return {
                'accepted': True,
                'confidence': production['confidence'],
                'completeness': self._calculate_completeness(),
                'next_expected': production['next_expected'],
                'stack_operation': f'consume_{event.get("type", "unknown")}'
            }
        
        elif rule_type == 'expand':
            # Non-terminal expansion - no input consumed
            # Update state to first symbol in production
            if symbols:
                self.current_state.current_state = symbols[0]
            
            return {
                'accepted': True,
                'confidence': production['confidence'],
                'completeness': self._calculate_completeness(),
                'next_expected': production['next_expected'],
                'stack_operation': f'expand_to_{symbols[0] if symbols else "unknown"}'
            }
        
        return {
            'accepted': False,
            'confidence': self.current_state.confidence,
            'completeness': self.current_state.pattern_completeness,
            'next_expected': self.current_state.expected_next_events,
            'stack_operation': 'invalid_rule_type'
        }
    
    def _calculate_completeness(self) -> float:
        """Calculate pattern completeness based on PDA state"""
        if self.current_state.current_state == 'ACCEPT':
            return 1.0
        
        # Estimate based on stack depth and events processed
        total_events = len(self.current_state.pattern_buffer)
        expected_events = 5  # Typical cascade pattern length
        
        event_progress = min(1.0, total_events / expected_events)
        
        # Weight by current state advancement
        state_weights = {
            'START': 0.0,
            'EXPANSION_PHASE': 0.2,
            'EXPANSION_HIGH': 0.4,
            'LIQUIDITY_GRAB': 0.6,
            'CONSOLIDATION_PHASE': 0.7,
            'CONSOLIDATION_BREAK': 0.85,
            'CASCADE_TRIGGER': 0.95,
            'ACCEPT': 1.0
        }
        
        state_progress = state_weights.get(self.current_state.current_state, 0.0)
        
        return 0.6 * event_progress + 0.4 * state_progress
    
    def _calculate_cascade_probability(self) -> float:
        """Calculate probability of cascade completion based on current state"""
        base_probability = self.current_state.confidence * self.current_state.pattern_completeness
        
        # Boost probability for advanced states
        state_boosts = {
            'CASCADE_TRIGGER': 1.3,
            'CONSOLIDATION_BREAK': 1.2,
            'CONSOLIDATION_PHASE': 1.1,
            'LIQUIDITY_GRAB': 1.0,
            'EXPANSION_HIGH': 0.9,
            'EXPANSION_PHASE': 0.8,
            'START': 0.5
        }
        
        boost = state_boosts.get(self.current_state.current_state, 1.0)
        
        return min(1.0, base_probability * boost)
    
    def _estimate_time_to_completion(self) -> Optional[float]:
        """Estimate minutes to cascade completion"""
        if self.current_state.pattern_completeness >= 1.0:
            return 0.0
        
        # Historical timing estimates
        state_timing = {
            'START': 45.0,
            'EXPANSION_PHASE': 35.0,
            'EXPANSION_HIGH': 25.0,
            'LIQUIDITY_GRAB': 15.0,
            'CONSOLIDATION_PHASE': 10.0,
            'CONSOLIDATION_BREAK': 5.0,
            'CASCADE_TRIGGER': 1.0
        }
        
        base_time = state_timing.get(self.current_state.current_state, 30.0)
        
        # Adjust based on pattern buffer timing
        if len(self.current_state.pattern_buffer) > 1:
            recent_events = self.current_state.pattern_buffer[-2:]
            # Implementation could include timing analysis
        
        return base_time
    
    def _check_timeout(self, current_time: datetime) -> bool:
        """Check if pattern has timed out"""
        time_diff = (current_time - self.current_state.last_event_time).total_seconds()
        return time_diff > self.current_state.timeout_threshold
    
    def _reset_state(self):
        """Reset to initial state"""
        self.current_state = GrammaticalState(
            stack=[],
            current_state='START',
            pattern_buffer=[],
            confidence=0.0,
            last_event_time=datetime.now(),
            pattern_completeness=0.0,
            expected_next_events=['expansion_high', 'liquidity_grab']
        )
        print("🔄 Grammatical state reset")
    
    def get_state_summary(self) -> Dict[str, Any]:
        """Get comprehensive state summary"""
        return {
            'current_state': self.current_state.current_state,
            'stack': self.current_state.stack,
            'confidence': self.current_state.confidence,
            'pattern_completeness': self.current_state.pattern_completeness,
            'expected_next_events': self.current_state.expected_next_events,
            'events_processed': len(self.current_state.pattern_buffer),
            'cascade_probability': self._calculate_cascade_probability(),
            'estimated_completion_time': self._estimate_time_to_completion(),
            'last_event_time': self.current_state.last_event_time.isoformat(),
            'state_history_length': len(self.state_history)
        }
    
    def get_recent_history(self, limit: int = 10) -> List[Dict]:
        """Get recent state change history"""
        return list(self.state_history)[-limit:]

def demo_grammatical_tracking():
    """Demonstrate real-time grammatical state tracking"""
    print("🎯 GRAMMATICAL STATE TRACKING DEMO")
    print("=" * 40)
    
    tracker = GrammaticalStateTracker()
    
    # Simulate cascade pattern events
    test_events = [
        {'type': 'expansion_high', 'magnitude': 0.8, 'timestamp': '09:30:15'},
        {'type': 'liquidity_grab', 'magnitude': 0.9, 'timestamp': '09:35:22'},
        {'type': 'consolidation_break', 'magnitude': 0.7, 'timestamp': '09:42:10'},
        {'type': 'cascade_trigger', 'magnitude': 0.95, 'timestamp': '09:45:30'},
    ]
    
    print(f"\n🔄 Processing {len(test_events)} test events...")
    
    for i, event in enumerate(test_events):
        print(f"\n--- Event {i+1}: {event['type']} ---")
        
        result = tracker.process_event(event)
        
        print(f"📊 Processing Result:")
        print(f"   Event accepted: {result['event_accepted']}")
        print(f"   Current state: {result['current_state']}")
        print(f"   Confidence: {result['confidence']:.1%}")
        print(f"   Completeness: {result['pattern_completeness']:.1%}")
        print(f"   Cascade probability: {result['cascade_probability']:.1%}")
        
        if result['time_to_completion']:
            print(f"   Est. completion: {result['time_to_completion']:.1f} minutes")
        
        time.sleep(0.5)  # Simulate real-time processing
    
    print(f"\n📈 FINAL STATE SUMMARY")
    print("=" * 25)
    
    summary = tracker.get_state_summary()
    for key, value in summary.items():
        if isinstance(value, float):
            if 0 <= value <= 1:
                print(f"   {key}: {value:.1%}")
            else:
                print(f"   {key}: {value:.2f}")
        else:
            print(f"   {key}: {value}")
    
    print(f"\n🕒 Recent State History:")
    history = tracker.get_recent_history(3)
    for entry in history[-3:]:
        print(f"   {entry['timestamp'][:19]}: {entry['previous_state']} → {entry['new_state']} "
              f"(confidence: {entry['confidence_change']:+.1%})")

if __name__ == "__main__":
    demo_grammatical_tracking()
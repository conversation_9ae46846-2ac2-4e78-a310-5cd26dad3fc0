"""
RG Graph System - Scale Transition Tracking
==========================================

Implements the renormalization group graph system that tracks feature importance
flow across scales (tick → 1min → 5min → 15min → 1hr) as recommended by the
strategic analysis.

Key Insight: Model cascades as scale-coupling detectors rather than event classifiers.
Transform P(cascade|events) → P(phase_shift|RG_flow)

Mathematical Foundation: dβ/ds = RG flow equation where β(s) is feature importance
"""

import networkx as nx
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import logging

@dataclass
class RGFlowVector:
    """Feature importance vector at a specific scale"""
    scale: str                          # 'tick', '1min', '5min', '15min', '1hr'
    feature_importance: np.ndarray      # Feature weights at this scale
    timestamp: datetime                 # When this flow state was captured
    transition_matrix: Optional[np.ndarray] = None  # Transition to next scale

@dataclass
class ScaleCouplingResult:
    """Result of scale coupling analysis"""
    coupling_strength: float           # How strongly scales are coupled
    flow_direction: str                # 'upward', 'downward', 'bidirectional'
    phase_shift_detected: bool         # Whether a regime shift was detected
    cascade_probability: float         # P(cascade|RG_flow)
    dominant_scale: str                # Which scale dominates the coupling

class RGGraphSystem:
    """
    Renormalization Group Graph System for Multi-Scale Analysis
    
    Tracks feature importance flow across time scales to detect phase transitions
    that indicate cascade potential. Based on the insight that cascades represent
    scale-coupling events rather than simple event accumulation.
    """
    
    def __init__(self, enable_cascade_units: bool = True):
        """Initialize RG Graph system"""
        
        self.enable_cascade_units = enable_cascade_units
        
        # Define scale hierarchy
        self.scales = ['tick', '1min', '5min', '15min', '1hr']
        self.scale_mapping = {scale: i for i, scale in enumerate(self.scales)}
        
        # Build RG graph structure
        self.rg_graph = self._build_rg_graph()
        
        # Feature dimensions (based on Oracle components)
        self.feature_dimensions = {
            'volume': 0,
            'price_volatility': 1,
            'fisher_information': 2,
            'hawkes_intensity': 3,
            'liquidity_flow': 4,
            'fpfvg_density': 5,
            'regime_stability': 6
        }
        self.num_features = len(self.feature_dimensions)
        
        # RG flow tracking
        self.flow_history = []
        self.phase_shift_threshold = 0.3  # Threshold for detecting regime shifts
        
        # Initialize logger first
        self.logger = logging.getLogger(__name__)
        
        # Load cascade units if available
        self.cascade_units = self._load_cascade_units() if enable_cascade_units else None
        
        self.logger.info("🌐 RG GRAPH SYSTEM: Initialized for multi-scale analysis")
        self.logger.info(f"   Scales: {' → '.join(self.scales)}")
        self.logger.info(f"   Features: {self.num_features}")
        
    def _build_rg_graph(self) -> nx.DiGraph:
        """Build renormalization group graph structure"""
        
        G = nx.DiGraph()
        
        # Add scale nodes
        for scale in self.scales:
            G.add_node(scale, scale_index=self.scale_mapping[scale])
        
        # Add directed edges between adjacent scales
        for i in range(len(self.scales) - 1):
            from_scale = self.scales[i]
            to_scale = self.scales[i + 1]
            
            # Forward flow (fine → coarse)
            G.add_edge(from_scale, to_scale, flow_type='upscale', weight=1.0)
            
            # Backward flow (coarse → fine) - for feedback effects
            G.add_edge(to_scale, from_scale, flow_type='downscale', weight=0.3)
        
        return G
    
    def _load_cascade_units(self) -> Optional[Dict]:
        """Load cascade units from extraction if available"""
        
        try:
            # Look for the most recent cascade units file
            import glob
            cascade_files = glob.glob("cascade_units_mathematical_*.json")
            
            if cascade_files:
                latest_file = max(cascade_files, key=lambda x: x.split('_')[-1])
                
                with open(latest_file, 'r') as f:
                    cascade_data = json.load(f)
                
                self.logger.info(f"✅ Cascade units loaded: {latest_file}")
                return cascade_data
            
        except Exception as e:
            self.logger.warning(f"⚠️ Could not load cascade units: {e}")
        
        return None
    
    def extract_features_at_scale(self, data: Dict, scale: str) -> np.ndarray:
        """
        Extract feature vector at specific scale
        
        Converts raw market data into feature importance vector for the given scale
        """
        
        features = np.zeros(self.num_features)
        
        try:
            # Scale-dependent feature extraction
            if scale == 'tick':
                # Tick-level features (micro-structure)
                features[0] = self._extract_tick_volume(data)
                features[1] = self._extract_tick_volatility(data)
                features[2] = data.get('fisher_information', 0) * 0.1  # Lower weight at tick
                
            elif scale == '1min':
                # 1-minute features (short-term patterns)
                features[0] = self._extract_minute_volume(data)
                features[1] = self._extract_minute_volatility(data)
                features[2] = data.get('fisher_information', 0) * 0.5
                features[3] = data.get('hawkes_intensity', 0) * 0.3
                
            elif scale == '5min':
                # 5-minute features (medium-term trends)
                features[0] = self._extract_5min_volume(data)
                features[1] = self._extract_5min_volatility(data)
                features[2] = data.get('fisher_information', 0) * 0.8
                features[3] = data.get('hawkes_intensity', 0) * 0.7
                features[4] = self._extract_liquidity_flow(data)
                
            elif scale == '15min':
                # 15-minute features (regime-level patterns)
                features[0] = self._extract_15min_volume(data)
                features[1] = self._extract_15min_volatility(data)
                features[2] = data.get('fisher_information', 0) * 1.0  # Full weight
                features[3] = data.get('hawkes_intensity', 0) * 1.0
                features[4] = self._extract_liquidity_flow(data)
                features[5] = self._extract_fpfvg_density(data)
                features[6] = self._extract_regime_stability(data)
                
            elif scale == '1hr':
                # 1-hour features (macro trends)
                features[0] = self._extract_hourly_volume(data)
                features[1] = self._extract_hourly_volatility(data)
                features[6] = self._extract_regime_stability(data) * 1.5  # Higher weight
                
            # Normalize features to [0, 1]
            if np.sum(features) > 0:
                features = features / np.linalg.norm(features)
            
        except Exception as e:
            self.logger.warning(f"Feature extraction error at {scale}: {e}")
        
        return features
    
    def compute_rg_flow(self, data_sequence: List[Dict], time_window_minutes: int = 60) -> List[RGFlowVector]:
        """
        Compute RG flow vectors across all scales
        
        This is the core method that implements the RG flow equation dβ/ds
        """
        
        self.logger.info(f"🌊 COMPUTING RG FLOW: {time_window_minutes}min window")
        
        flow_vectors = []
        current_time = datetime.now()
        
        # Extract features at each scale
        for scale in self.scales:
            
            # Aggregate data appropriate for this scale
            scale_data = self._aggregate_data_for_scale(data_sequence, scale, time_window_minutes)
            
            # Extract feature importance vector
            feature_vector = self.extract_features_at_scale(scale_data, scale)
            
            # Create RG flow vector
            flow_vector = RGFlowVector(
                scale=scale,
                feature_importance=feature_vector,
                timestamp=current_time
            )
            
            flow_vectors.append(flow_vector)
            
            self.logger.debug(f"   {scale}: ||β|| = {np.linalg.norm(feature_vector):.3f}")
        
        # Compute transition matrices between scales
        for i in range(len(flow_vectors) - 1):
            transition_matrix = self._compute_transition_matrix(
                flow_vectors[i], flow_vectors[i + 1]
            )
            flow_vectors[i].transition_matrix = transition_matrix
        
        # Store in flow history
        self.flow_history.append(flow_vectors)
        
        # Keep only recent history (last 24 hours)
        cutoff_time = current_time - timedelta(hours=24)
        self.flow_history = [
            fv for fv in self.flow_history 
            if fv[0].timestamp > cutoff_time
        ]
        
        return flow_vectors
    
    def analyze_scale_coupling(self, flow_vectors: List[RGFlowVector]) -> ScaleCouplingResult:
        """
        Analyze coupling strength between scales
        
        This implements the key insight: detect P(phase_shift|RG_flow)
        """
        
        if len(flow_vectors) < 2:
            return self._default_coupling_result()
        
        self.logger.info("🔗 ANALYZING SCALE COUPLING")
        
        # Compute coupling strength between adjacent scales
        coupling_strengths = []
        flow_directions = []
        
        for i in range(len(flow_vectors) - 1):
            current_scale = flow_vectors[i]
            next_scale = flow_vectors[i + 1]
            
            # Compute correlation between feature vectors
            correlation = np.corrcoef(current_scale.feature_importance, 
                                    next_scale.feature_importance)[0, 1]
            
            # Handle NaN correlations
            if np.isnan(correlation):
                correlation = 0.0
            
            coupling_strength = abs(correlation)
            coupling_strengths.append(coupling_strength)
            
            # Determine flow direction
            current_magnitude = np.linalg.norm(current_scale.feature_importance)
            next_magnitude = np.linalg.norm(next_scale.feature_importance)
            
            if current_magnitude > next_magnitude * 1.2:
                flow_directions.append('upward')  # Fine scale dominates
            elif next_magnitude > current_magnitude * 1.2:
                flow_directions.append('downward')  # Coarse scale dominates
            else:
                flow_directions.append('balanced')
            
            self.logger.debug(f"   {current_scale.scale} → {next_scale.scale}: "
                            f"coupling={coupling_strength:.3f}, flow={flow_directions[-1]}")
        
        # Overall coupling analysis
        avg_coupling = np.mean(coupling_strengths)
        dominant_flow = max(set(flow_directions), key=flow_directions.count) if flow_directions else 'balanced'
        
        # Phase shift detection
        phase_shift_detected = self._detect_phase_shift(flow_vectors, coupling_strengths)
        
        # Cascade probability based on coupling
        cascade_prob = self._compute_cascade_probability(avg_coupling, phase_shift_detected, flow_vectors)
        
        # Determine dominant scale
        scale_magnitudes = [np.linalg.norm(fv.feature_importance) for fv in flow_vectors]
        dominant_scale_idx = np.argmax(scale_magnitudes)
        dominant_scale = flow_vectors[dominant_scale_idx].scale
        
        result = ScaleCouplingResult(
            coupling_strength=avg_coupling,
            flow_direction=dominant_flow,
            phase_shift_detected=phase_shift_detected,
            cascade_probability=cascade_prob,
            dominant_scale=dominant_scale
        )
        
        self.logger.info(f"📊 COUPLING ANALYSIS COMPLETE:")
        self.logger.info(f"   Coupling Strength: {avg_coupling:.3f}")
        self.logger.info(f"   Flow Direction: {dominant_flow}")
        self.logger.info(f"   Phase Shift: {'✅ DETECTED' if phase_shift_detected else 'Not detected'}")
        self.logger.info(f"   Cascade Probability: {cascade_prob:.3f}")
        self.logger.info(f"   Dominant Scale: {dominant_scale}")
        
        return result
    
    def _detect_phase_shift(self, flow_vectors: List[RGFlowVector], coupling_strengths: List[float]) -> bool:
        """Detect phase transitions in RG flow"""
        
        if len(self.flow_history) < 2:
            return False
        
        # Compare current flow with recent history
        current_coupling = np.mean(coupling_strengths) if coupling_strengths else 0
        
        # Get historical coupling strengths
        historical_couplings = []
        for historical_flow in self.flow_history[-5:]:  # Last 5 measurements
            if len(historical_flow) >= 2:
                hist_couplings = []
                for i in range(len(historical_flow) - 1):
                    correlation = np.corrcoef(
                        historical_flow[i].feature_importance,
                        historical_flow[i + 1].feature_importance
                    )[0, 1]
                    if not np.isnan(correlation):
                        hist_couplings.append(abs(correlation))
                
                if hist_couplings:
                    historical_couplings.append(np.mean(hist_couplings))
        
        if not historical_couplings:
            return False
        
        # Detect sudden change in coupling
        historical_avg = np.mean(historical_couplings)
        coupling_change = abs(current_coupling - historical_avg)
        
        # Phase shift detected if coupling change exceeds threshold
        return coupling_change > self.phase_shift_threshold
    
    def _compute_cascade_probability(self, coupling_strength: float, phase_shift: bool, 
                                   flow_vectors: List[RGFlowVector]) -> float:
        """Compute P(cascade|RG_flow)"""
        
        base_probability = 0.1  # 10% baseline cascade probability
        
        # Boost probability based on coupling strength
        coupling_boost = coupling_strength * 0.4  # Up to 40% boost
        
        # Phase shift boost
        phase_shift_boost = 0.3 if phase_shift else 0.0  # 30% boost for phase shift
        
        # Fisher Information boost (if available)
        fisher_boost = 0.0
        for fv in flow_vectors:
            fisher_value = fv.feature_importance[self.feature_dimensions['fisher_information']]
            if fisher_value > 0.5:  # Normalized threshold
                fisher_boost = min(0.4, fisher_value * 0.4)
                break
        
        # Cascade unit boost (if available)
        cascade_unit_boost = 0.0
        if self.cascade_units:
            # Check if current flow matches known cascade operators
            cascade_unit_boost = self._evaluate_cascade_units(flow_vectors)
        
        total_probability = min(0.95, base_probability + coupling_boost + 
                              phase_shift_boost + fisher_boost + cascade_unit_boost)
        
        return total_probability
    
    def _evaluate_cascade_units(self, flow_vectors: List[RGFlowVector]) -> float:
        """Evaluate current flow against cascade unit operators"""
        
        if not self.cascade_units or 'cascade_units' not in self.cascade_units:
            return 0.0
        
        max_operator_match = 0.0
        
        try:
            for unit_data in self.cascade_units['cascade_units']:
                trigger_vector = np.array(unit_data['trigger_vector'])
                frequency_weight = unit_data['frequency']
                
                # Compare current flow with cascade unit trigger
                for fv in flow_vectors:
                    if len(fv.feature_importance) == len(trigger_vector):
                        similarity = np.dot(fv.feature_importance, trigger_vector)
                        weighted_similarity = similarity * frequency_weight
                        max_operator_match = max(max_operator_match, weighted_similarity)
            
        except Exception as e:
            self.logger.warning(f"Error evaluating cascade units: {e}")
        
        return min(0.3, max_operator_match)  # Cap at 30% boost
    
    # Feature extraction helpers (simplified implementations)
    def _extract_tick_volume(self, data: Dict) -> float:
        """Extract tick-level volume features"""
        return min(1.0, data.get('volume', 0) / 1000000)  # Normalize
    
    def _extract_tick_volatility(self, data: Dict) -> float:
        """Extract tick-level volatility"""
        return min(1.0, data.get('volatility', 0) * 100)  # Scale and normalize
    
    def _extract_minute_volume(self, data: Dict) -> float:
        """Extract 1-minute volume features"""
        return min(1.0, data.get('volume', 0) / 5000000)
    
    def _extract_minute_volatility(self, data: Dict) -> float:
        """Extract 1-minute volatility"""
        return min(1.0, data.get('volatility', 0) * 50)
    
    def _extract_5min_volume(self, data: Dict) -> float:
        """Extract 5-minute volume features"""
        return min(1.0, data.get('volume', 0) / 25000000)
    
    def _extract_5min_volatility(self, data: Dict) -> float:
        """Extract 5-minute volatility"""
        return min(1.0, data.get('volatility', 0) * 20)
    
    def _extract_15min_volume(self, data: Dict) -> float:
        """Extract 15-minute volume features"""
        return min(1.0, data.get('volume', 0) / 75000000)
    
    def _extract_15min_volatility(self, data: Dict) -> float:
        """Extract 15-minute volatility"""
        return min(1.0, data.get('volatility', 0) * 10)
    
    def _extract_hourly_volume(self, data: Dict) -> float:
        """Extract hourly volume features"""
        return min(1.0, data.get('volume', 0) / 300000000)
    
    def _extract_hourly_volatility(self, data: Dict) -> float:
        """Extract hourly volatility"""
        return min(1.0, data.get('volatility', 0) * 5)
    
    def _extract_liquidity_flow(self, data: Dict) -> float:
        """Extract liquidity flow patterns"""
        return min(1.0, data.get('liquidity_events', 0) / 10)
    
    def _extract_fpfvg_density(self, data: Dict) -> float:
        """Extract FPFVG density"""
        return min(1.0, data.get('fpfvg_count', 0) / 5)
    
    def _extract_regime_stability(self, data: Dict) -> float:
        """Extract regime stability measure"""
        return min(1.0, 1.0 - data.get('regime_changes', 0) / 3)
    
    def _aggregate_data_for_scale(self, data_sequence: List[Dict], scale: str, window_minutes: int) -> Dict:
        """Aggregate raw data appropriate for the given scale"""
        
        if not data_sequence:
            return {}
        
        # Simple aggregation - in production this would be more sophisticated
        aggregated = {
            'volume': sum(d.get('volume', 0) for d in data_sequence),
            'volatility': np.std([d.get('price', 0) for d in data_sequence]) if len(data_sequence) > 1 else 0,
            'fisher_information': np.mean([d.get('fisher_information', 0) for d in data_sequence]),
            'hawkes_intensity': np.mean([d.get('hawkes_intensity', 0) for d in data_sequence]),
            'liquidity_events': sum(d.get('liquidity_events', 0) for d in data_sequence),
            'fpfvg_count': sum(d.get('fpfvg_count', 0) for d in data_sequence),
            'regime_changes': sum(d.get('regime_changes', 0) for d in data_sequence)
        }
        
        return aggregated
    
    def _compute_transition_matrix(self, from_flow: RGFlowVector, to_flow: RGFlowVector) -> np.ndarray:
        """Compute transition matrix between scales"""
        
        # Simple outer product - could be enhanced with more sophisticated methods
        transition = np.outer(from_flow.feature_importance, to_flow.feature_importance)
        
        # Normalize
        norm = np.linalg.norm(transition)
        if norm > 0:
            transition = transition / norm
        
        return transition
    
    def _default_coupling_result(self) -> ScaleCouplingResult:
        """Default coupling result for edge cases"""
        return ScaleCouplingResult(
            coupling_strength=0.1,
            flow_direction='balanced',
            phase_shift_detected=False,
            cascade_probability=0.1,
            dominant_scale='5min'
        )


def create_rg_graph_system(enable_cascade_units: bool = True) -> RGGraphSystem:
    """Factory function for production RG Graph system"""
    return RGGraphSystem(enable_cascade_units=enable_cascade_units)


if __name__ == "__main__":
    """Test RG Graph system with sample data"""
    
    print("🌐 RG GRAPH SYSTEM - Multi-Scale Analysis Testing")
    print("=" * 60)
    
    # Create RG Graph system
    rg_system = create_rg_graph_system()
    
    # Create sample data sequence
    sample_data = [
        {
            'volume': 1000000,
            'price': 23500,
            'fisher_information': 0.3,
            'hawkes_intensity': 0.5,
            'liquidity_events': 2,
            'fpfvg_count': 1,
            'regime_changes': 0
        },
        {
            'volume': 1500000,
            'price': 23510,
            'fisher_information': 0.7,
            'hawkes_intensity': 0.8,
            'liquidity_events': 3,
            'fpfvg_count': 2,
            'regime_changes': 1
        },
        {
            'volume': 2000000,
            'price': 23520,
            'fisher_information': 1.2,
            'hawkes_intensity': 1.1,
            'liquidity_events': 5,
            'fpfvg_count': 3,
            'regime_changes': 0
        }
    ]
    
    print(f"📊 Sample Data: {len(sample_data)} time points")
    
    # Compute RG flow
    flow_vectors = rg_system.compute_rg_flow(sample_data)
    
    # Analyze scale coupling
    coupling_result = rg_system.analyze_scale_coupling(flow_vectors)
    
    print(f"\n🎯 RG ANALYSIS RESULTS:")
    print(f"   Scale Coupling: {coupling_result.coupling_strength:.3f}")
    print(f"   Flow Direction: {coupling_result.flow_direction}")
    print(f"   Phase Shift: {'✅ DETECTED' if coupling_result.phase_shift_detected else 'Not detected'}")
    print(f"   Cascade Probability: {coupling_result.cascade_probability:.1%}")
    print(f"   Dominant Scale: {coupling_result.dominant_scale}")
    
    if coupling_result.cascade_probability > 0.5:
        print(f"\n🚨 HIGH CASCADE PROBABILITY DETECTED")
        print(f"   Probability: {coupling_result.cascade_probability:.1%}")
        print(f"   Recommended Action: Activate cascade monitoring")
    
    print(f"\n✅ RG GRAPH SYSTEM: Testing complete")
    print("   Ready for NASDAQ case study integration")
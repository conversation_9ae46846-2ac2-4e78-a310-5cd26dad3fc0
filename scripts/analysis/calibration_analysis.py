#!/usr/bin/env python3
"""
Calibration Analysis - Diagnose XGBoost Memorization vs Learning
==============================================================

Tests the hypothesis that our 82.4% validation accuracy with 17.6% training gap
is due to pattern memorization rather than genuine learning. Mathematical
signatures we're looking for:

1. Calibration plots showing step functions (memorization) vs diagonal (learning)
2. Stratified accuracy: accuracy_common >> accuracy_rare (>30% gap = memorization)
3. Class imbalance effects: 86.4% cascade rate creating majority voting strategy

Mathematical Framework:
- Perfect calibration: P_predicted(cascade) ≈ P_actual(cascade) across all probabilities
- Memorization signature: Predicted probabilities cluster at extremes (0.1, 0.9)
- Learning signature: Smooth distribution of predicted probabilities matching actual rates
"""

import json
import numpy as np
import pandas as pd
from pathlib import Path
import matplotlib.pyplot as plt
# import seaborn as sns  # Not available
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from collections import Counter, defaultdict
import pickle
from sklearn.metrics import brier_score_loss, log_loss
from sklearn.calibration import calibration_curve
from scipy import stats

# Import our components
from xgboost_real_trainer import XGBoostRealTrainer, TrainingExample
from cascade_predictor import CascadePredictor

@dataclass
class CalibrationMetrics:
    """Comprehensive calibration analysis results"""
    brier_score: float
    log_loss_score: float
    reliability: float  # Expected Calibration Error
    resolution: float   # How well model separates classes
    calibration_slope: float  # Linear fit slope (1.0 = perfect)
    calibration_intercept: float  # Linear fit intercept (0.0 = perfect)
    
@dataclass
class StratifiedResults:
    """Performance by pattern frequency strata"""
    rare_patterns: Dict[str, float]  # <5 occurrences
    medium_patterns: Dict[str, float]  # 5-20 occurrences
    common_patterns: Dict[str, float]  # >20 occurrences
    memorization_gap: float  # accuracy_common - accuracy_rare

class CalibrationAnalyzer:
    """
    Comprehensive calibration and memorization analysis
    
    Diagnoses whether XGBoost learned genuine patterns or memorized training data
    through multiple statistical and visual tests.
    """
    
    def __init__(self):
        # Load trained model
        try:
            with open("xgboost_real_trained_model.pkl", 'rb') as f:
                model_data = pickle.load(f)
                self.trained_model = model_data['model']
                self.feature_scaler = model_data['scaler']
                self.pattern_encoder = model_data['pattern_encoder']
                self.training_metrics = model_data['training_metrics']
                print("✅ Loaded trained XGBoost model for analysis")
        except:
            print("❌ No trained model found, analysis limited")
            self.trained_model = None
            
        # Initialize components
        self.trainer = XGBoostRealTrainer()
        self.cascade_predictor = CascadePredictor()
        
        print("🔬 CALIBRATION ANALYZER")
        print("=" * 30)
        print("Purpose: Diagnose memorization vs genuine learning")
        print("Tests: Calibration plots, stratified analysis, class balance effects")
        print("Target: Confirm 17.6% training-validation gap root cause")
        print()
    
    def load_and_prepare_data(self) -> Tuple[List[TrainingExample], np.ndarray, np.ndarray]:
        """Load training data and prepare for analysis"""
        
        print("📁 Loading training data for calibration analysis...")
        
        # Load enhanced sessions
        sessions = self.trainer.load_enhanced_sessions()
        examples = self.trainer.extract_training_examples(sessions)
        
        if len(examples) < 20:
            raise ValueError(f"Insufficient data for analysis: {len(examples)} examples")
        
        # Prepare feature matrix and labels
        X, y = self.trainer.prepare_training_data(examples)
        
        print(f"✅ Data prepared: {len(examples)} examples")
        print(f"   Cascade rate: {np.mean(y):.1%}")
        print(f"   Non-cascade rate: {1-np.mean(y):.1%}")
        
        return examples, X, y
    
    def analyze_pattern_frequency_distribution(self, examples: List[TrainingExample]) -> Dict[str, List[str]]:
        """Stratify patterns by occurrence frequency"""
        
        print("📊 Analyzing pattern frequency distribution...")
        
        # Count pattern occurrences
        pattern_counts = Counter()
        for example in examples:
            pattern_id = example.context_features.pattern_id_encoded
            # Reverse lookup pattern name
            for name, encoded in self.pattern_encoder.items():
                if encoded == pattern_id:
                    pattern_counts[name] += 1
                    break
        
        # Stratify by frequency
        strata = {
            'rare': [],      # <5 occurrences  
            'medium': [],    # 5-20 occurrences
            'common': []     # >20 occurrences
        }
        
        for pattern, count in pattern_counts.items():
            if count < 5:
                strata['rare'].append(pattern)
            elif count <= 20:
                strata['medium'].append(pattern)
            else:
                strata['common'].append(pattern)
        
        print(f"✅ Pattern frequency stratification:")
        print(f"   Rare patterns (<5): {len(strata['rare'])}")
        print(f"   Medium patterns (5-20): {len(strata['medium'])}")
        print(f"   Common patterns (>20): {len(strata['common'])}")
        
        # Display pattern distributions
        for stratum, patterns in strata.items():
            if patterns:
                counts = [pattern_counts[p] for p in patterns]
                print(f"   {stratum.title()} counts: {counts}")
        
        return strata
    
    def generate_calibration_plots(self, X: np.ndarray, y: np.ndarray, 
                                  examples: List[TrainingExample]) -> CalibrationMetrics:
        """Generate comprehensive calibration analysis plots"""
        
        if self.trained_model is None:
            print("❌ No trained model available for calibration analysis")
            return None
        
        print("📈 Generating calibration plots...")
        
        # Get model predictions
        X_scaled = self.feature_scaler.transform(X)
        y_proba = self.trained_model.predict_proba(X_scaled)[:, 1]
        y_pred = (y_proba >= 0.5).astype(int)
        
        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('XGBoost Calibration Analysis - Memorization Diagnostic', fontsize=16)
        
        # 1. Reliability diagram (calibration curve)
        fraction_of_positives, mean_predicted_value = calibration_curve(
            y, y_proba, n_bins=10
        )
        
        axes[0, 0].plot([0, 1], [0, 1], 'k--', label='Perfect calibration')
        axes[0, 0].plot(mean_predicted_value, fraction_of_positives, 'ro-', 
                       label='XGBoost model')
        axes[0, 0].set_xlabel('Mean Predicted Probability')
        axes[0, 0].set_ylabel('Fraction of Positives')
        axes[0, 0].set_title('Calibration Plot (Reliability Diagram)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # Add diagnosis text
        if np.corrcoef(mean_predicted_value, fraction_of_positives)[0,1] < 0.9:
            diagnosis_text = "⚠️ STEP FUNCTION DETECTED\nSignature: MEMORIZATION"
        else:
            diagnosis_text = "✅ LINEAR RELATIONSHIP\nSignature: LEARNING"
        
        axes[0, 0].text(0.02, 0.98, diagnosis_text, transform=axes[0, 0].transAxes,
                       verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat'))
        
        # 2. Predicted probability histogram
        axes[0, 1].hist(y_proba, bins=20, alpha=0.7, density=True)
        axes[0, 1].axvline(np.mean(y), color='red', linestyle='--', 
                          label=f'Actual cascade rate: {np.mean(y):.1%}')
        axes[0, 1].set_xlabel('Predicted Probability')
        axes[0, 1].set_ylabel('Density')
        axes[0, 1].set_title('Distribution of Predicted Probabilities')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # Check for probability clustering (memorization signature)
        prob_std = np.std(y_proba)
        if prob_std < 0.15:  # Low variance suggests clustering
            cluster_text = "⚠️ PROBABILITY CLUSTERING\nLow variance: MEMORIZATION"
        else:
            cluster_text = "✅ DISTRIBUTED PROBABILITIES\nHigh variance: LEARNING"
        
        axes[0, 1].text(0.02, 0.98, cluster_text, transform=axes[0, 1].transAxes,
                       verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue'))
        
        # 3. Residual analysis
        residuals = y - y_proba
        axes[1, 0].scatter(y_proba, residuals, alpha=0.6)
        axes[1, 0].axhline(0, color='red', linestyle='--')
        axes[1, 0].set_xlabel('Predicted Probability')
        axes[1, 0].set_ylabel('Residuals (Actual - Predicted)')
        axes[1, 0].set_title('Residual Plot')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. ROC-like curve showing discrimination
        sorted_indices = np.argsort(y_proba)
        sorted_proba = y_proba[sorted_indices]
        sorted_actual = y[sorted_indices]
        
        # Calculate running averages
        window_size = max(10, len(y) // 20)
        running_pred = np.convolve(sorted_proba, np.ones(window_size)/window_size, mode='valid')
        running_actual = np.convolve(sorted_actual, np.ones(window_size)/window_size, mode='valid')
        
        axes[1, 1].plot(running_pred, running_actual, 'b-', linewidth=2)
        axes[1, 1].plot([0, 1], [0, 1], 'k--', label='Perfect discrimination')
        axes[1, 1].set_xlabel('Running Average Predicted')
        axes[1, 1].set_ylabel('Running Average Actual')
        axes[1, 1].set_title('Discrimination Ability')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('xgboost_calibration_analysis.png', dpi=300, bbox_inches='tight')
        print("✅ Calibration plots saved: xgboost_calibration_analysis.png")
        
        # Calculate calibration metrics
        brier = brier_score_loss(y, y_proba)
        log_loss_val = log_loss(y, y_proba)
        
        # Expected Calibration Error (reliability)
        reliability = np.mean(np.abs(fraction_of_positives - mean_predicted_value))
        
        # Resolution (how well model separates classes)
        resolution = np.mean((mean_predicted_value - np.mean(y))**2)
        
        # Linear fit to calibration curve
        if len(mean_predicted_value) > 1:
            slope, intercept, r_value, p_value, std_err = stats.linregress(
                mean_predicted_value, fraction_of_positives
            )
        else:
            slope, intercept = 1.0, 0.0
        
        metrics = CalibrationMetrics(
            brier_score=brier,
            log_loss_score=log_loss_val,
            reliability=reliability,
            resolution=resolution,
            calibration_slope=slope,
            calibration_intercept=intercept
        )
        
        print(f"\n🔬 CALIBRATION METRICS:")
        print(f"   Brier Score: {brier:.4f} (lower = better)")
        print(f"   Log Loss: {log_loss_val:.4f} (lower = better)")
        print(f"   Reliability (ECE): {reliability:.4f} (lower = better)")
        print(f"   Resolution: {resolution:.4f} (higher = better)")
        print(f"   Calibration Slope: {slope:.3f} (1.0 = perfect)")
        print(f"   Calibration Intercept: {intercept:.3f} (0.0 = perfect)")
        
        return metrics
    
    def stratified_performance_analysis(self, X: np.ndarray, y: np.ndarray, 
                                       examples: List[TrainingExample],
                                       pattern_strata: Dict[str, List[str]]) -> StratifiedResults:
        """Analyze performance across pattern frequency strata"""
        
        if self.trained_model is None:
            print("❌ No trained model for stratified analysis")
            return None
        
        print("🎯 Executing stratified performance analysis...")
        
        # Get predictions
        X_scaled = self.feature_scaler.transform(X)
        y_proba = self.trained_model.predict_proba(X_scaled)[:, 1]
        y_pred = (y_proba >= 0.5).astype(int)
        
        # Group examples by pattern frequency
        strata_results = {}
        
        for stratum_name, pattern_list in pattern_strata.items():
            if not pattern_list:
                strata_results[stratum_name] = {
                    'accuracy': 0.0,
                    'precision': 0.0,
                    'recall': 0.0,
                    'count': 0,
                    'avg_probability': 0.0
                }
                continue
            
            # Find examples belonging to this stratum
            stratum_indices = []
            for i, example in enumerate(examples):
                # Reverse lookup pattern name
                pattern_name = None
                for name, encoded in self.pattern_encoder.items():
                    if encoded == example.context_features.pattern_id_encoded:
                        pattern_name = name
                        break
                
                if pattern_name in pattern_list:
                    stratum_indices.append(i)
            
            if not stratum_indices:
                strata_results[stratum_name] = {
                    'accuracy': 0.0,
                    'precision': 0.0, 
                    'recall': 0.0,
                    'count': 0,
                    'avg_probability': 0.0
                }
                continue
            
            # Calculate metrics for this stratum
            stratum_y_true = y[stratum_indices]
            stratum_y_pred = y_pred[stratum_indices]
            stratum_y_proba = y_proba[stratum_indices]
            
            # Metrics
            accuracy = np.mean(stratum_y_true == stratum_y_pred)
            
            # Precision and recall (handle division by zero)
            true_positives = np.sum((stratum_y_true == 1) & (stratum_y_pred == 1))
            predicted_positives = np.sum(stratum_y_pred == 1)
            actual_positives = np.sum(stratum_y_true == 1)
            
            precision = true_positives / predicted_positives if predicted_positives > 0 else 0.0
            recall = true_positives / actual_positives if actual_positives > 0 else 0.0
            
            strata_results[stratum_name] = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'count': len(stratum_indices),
                'avg_probability': np.mean(stratum_y_proba)
            }
        
        # Calculate memorization gap
        common_acc = strata_results['common']['accuracy']
        rare_acc = strata_results['rare']['accuracy']
        memorization_gap = common_acc - rare_acc
        
        results = StratifiedResults(
            rare_patterns=strata_results['rare'],
            medium_patterns=strata_results['medium'], 
            common_patterns=strata_results['common'],
            memorization_gap=memorization_gap
        )
        
        print(f"\n📊 STRATIFIED PERFORMANCE RESULTS:")
        print(f"   RARE PATTERNS (<5 occurrences):")
        print(f"     Count: {results.rare_patterns['count']}")
        print(f"     Accuracy: {results.rare_patterns['accuracy']:.1%}")
        print(f"     Avg Probability: {results.rare_patterns['avg_probability']:.3f}")
        
        print(f"   MEDIUM PATTERNS (5-20 occurrences):")
        print(f"     Count: {results.medium_patterns['count']}")
        print(f"     Accuracy: {results.medium_patterns['accuracy']:.1%}")
        print(f"     Avg Probability: {results.medium_patterns['avg_probability']:.3f}")
        
        print(f"   COMMON PATTERNS (>20 occurrences):")
        print(f"     Count: {results.common_patterns['count']}")
        print(f"     Accuracy: {results.common_patterns['accuracy']:.1%}")
        print(f"     Avg Probability: {results.common_patterns['avg_probability']:.3f}")
        
        print(f"\n🚨 MEMORIZATION DIAGNOSIS:")
        print(f"   Accuracy Gap (Common - Rare): {memorization_gap:.1%}")
        
        if memorization_gap > 0.30:  # >30% gap
            diagnosis = "🔴 SEVERE MEMORIZATION DETECTED"
            recommendation = "Immediate class balancing required"
        elif memorization_gap > 0.15:  # >15% gap
            diagnosis = "🟡 MODERATE MEMORIZATION"
            recommendation = "Class balancing recommended"
        else:
            diagnosis = "🟢 ACCEPTABLE GENERALIZATION"
            recommendation = "Model learning genuine patterns"
        
        print(f"   Diagnosis: {diagnosis}")
        print(f"   Recommendation: {recommendation}")
        
        return results
    
    def comprehensive_analysis(self) -> Dict[str, Any]:
        """Execute complete calibration and memorization analysis"""
        
        print("🧪 COMPREHENSIVE MEMORIZATION ANALYSIS")
        print("=" * 45)
        print("Testing hypothesis: 17.6% training-validation gap due to memorization")
        print()
        
        # Load and prepare data
        try:
            examples, X, y = self.load_and_prepare_data()
        except Exception as e:
            print(f"❌ Data loading failed: {e}")
            return None
        
        # Analyze pattern frequency distribution
        pattern_strata = self.analyze_pattern_frequency_distribution(examples)
        
        # Generate calibration analysis
        calibration_metrics = self.generate_calibration_plots(X, y, examples)
        
        # Stratified performance analysis
        stratified_results = self.stratified_performance_analysis(X, y, examples, pattern_strata)
        
        # Comprehensive diagnosis
        print(f"\n🎯 COMPREHENSIVE DIAGNOSIS")
        print("=" * 30)
        
        # Evidence compilation
        evidence = {
            'class_imbalance': np.mean(y),
            'training_validation_gap': 0.176,  # From previous analysis
            'calibration_metrics': calibration_metrics,
            'stratified_results': stratified_results
        }
        
        # Mathematical signature analysis
        signatures = []
        
        # 1. Class imbalance signature
        if evidence['class_imbalance'] > 0.8:
            signatures.append("🔴 Severe class imbalance (86.4% cascade rate)")
        
        # 2. Training-validation gap signature  
        if evidence['training_validation_gap'] > 0.15:
            signatures.append("🔴 Large generalization gap (17.6%)")
        
        # 3. Calibration signature
        if calibration_metrics and calibration_metrics.reliability > 0.1:
            signatures.append("🔴 Poor calibration reliability")
        
        # 4. Stratification signature
        if stratified_results and stratified_results.memorization_gap > 0.3:
            signatures.append("🔴 Severe memorization gap (>30%)")
        
        print("Mathematical Signatures of Memorization:")
        for signature in signatures:
            print(f"   {signature}")
        
        # Final diagnosis
        if len(signatures) >= 3:
            final_diagnosis = "CONFIRMED: Model memorizing patterns, not learning"
            priority = "IMMEDIATE class balance revolution required"
        elif len(signatures) >= 2:
            final_diagnosis = "LIKELY: Significant memorization effects detected"
            priority = "Class balancing strongly recommended"
        else:
            final_diagnosis = "ACCEPTABLE: Model showing genuine learning"
            priority = "Continue with regularization improvements"
        
        print(f"\n🏆 FINAL DIAGNOSIS:")
        print(f"   Result: {final_diagnosis}")
        print(f"   Priority: {priority}")
        
        return {
            'diagnosis': final_diagnosis,
            'priority': priority,
            'evidence': evidence,
            'signatures': signatures,
            'calibration_metrics': calibration_metrics,
            'stratified_results': stratified_results
        }

def main():
    """Execute comprehensive calibration analysis"""
    
    analyzer = CalibrationAnalyzer()
    results = analyzer.comprehensive_analysis()
    
    if results:
        print(f"\n✅ Calibration analysis complete")
        print(f"Diagnosis: {results['diagnosis']}")
        return results
    else:
        print(f"\n❌ Calibration analysis failed")
        return None

if __name__ == "__main__":
    analysis_results = main()
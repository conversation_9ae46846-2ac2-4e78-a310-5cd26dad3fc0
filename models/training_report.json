{"training_data": {"cascade_count": 57, "non_cascade_count": 2, "minority_ratio": 0.03389830508474576, "total_samples": 67, "feature_count": 7}, "metrics": {"out_of_time_accuracy": 1.0, "train_accuracy": 0.8867924528301887, "accuracy_ci_lower": 0.7297095823975523, "accuracy_ci_upper": 0.9757849230969531, "cv_mean": 0.8527472527472527, "cv_std": 0.06151883517485016, "temporal_split": "80/20"}, "model_details": {"model_type": "XGBoost", "feature_names": ["grammatical_event_density", "pattern_completion_probability", "cross_session_influence_score", "fpfvg_interaction_strength", "session_type_encoded", "pattern_sequence_complexity", "cascade_trigger_proximity"], "confusion_matrix": [[11, 0], [0, 3]], "classification_report": {"0": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 11.0}, "1": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 3.0}, "accuracy": 1.0, "macro avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 14.0}, "weighted avg": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 14.0}}}, "ablations": {"temporal_features_delta": -0.02985074626865669, "price_features_delta": -0.01492537313432829, "cascade_features_delta": 0.0, "metadata_features_delta": 0.0}, "scaler": "StandardScaler()", "training_time": 0.1626288890838623, "notes": ["Real XGBoost training on enhanced sessions", "Trained on 67 samples with 7 features"]}
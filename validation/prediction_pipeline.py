# DEPRECATED: This module has moved to src.project_oracle.pipelines.prediction_pipeline
# This file provides backward compatibility during the refactoring transition.

import warnings
warnings.warn(
    "prediction_pipeline is deprecated. Use 'from src.ironpulse.pipelines.prediction_pipeline import ...' instead.",
    DeprecationWarning,
    stacklevel=2
)

# Forward all imports to new location
from src.ironpulse.pipelines.prediction_pipeline import *
import numpy as np
from pathlib import Path
from typing import Dict, List, Set, Tuple, Any, Optional
from dataclasses import dataclass, field
import logging
from datetime import datetime, timedelta
from collections import Counter, defaultdict

# Import existing components
from cascade_predictor import CascadePredictor
from market_cascade_pda import MarketCascadePDA, CascadePrediction

@dataclass
class StateVector:
    """Market state vector from overnight sessions"""
    overnight_patterns: List[str]
    energy_carryover: float
    momentum_indicators: Dict[str, float]
    fpfvg_state: Dict[str, Any]
    liquidity_conditions: Dict[str, Any]
    session_transitions: List[str]
    timestamp: datetime

@dataclass
class PipelinePrediction:
    """Complete prediction result from pipeline"""
    target_session: str
    target_date: str
    prediction_time: datetime
    
    # Pattern analysis
    identified_pattern_id: str
    pattern_sequence: List[str]
    pattern_confidence: float
    
    # Cascade probability
    static_probability: float
    final_probability: float
    
    # State context
    overnight_state: StateVector
    previous_close_state: Dict[str, Any]
    
    # Methodology
    pda_time_ms: float
    total_time_ms: float
    method_used: str

class PredictionPipeline:
    """
    Main prediction pipeline implementing correct temporal flow
    
    Workflow:
    1. Load overnight sessions (completed data)
    2. Extract state vector from overnight flow
    3. Load previous session close state
    4. Apply PDA pattern recognition
    5. Assign static probabilities
    6. Generate final prediction
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Initialize core components
        self.pda_parser = MarketCascadePDA()
        self.cascade_predictor = CascadePredictor()
        
        # Static pattern probabilities (from 58 session analysis)
        self.pattern_probabilities = self._load_pattern_probabilities()
        
        # Session ordering for temporal flow
        self.session_order = ['MIDNIGHT', 'ASIA', 'LONDON', 'PREMARKET', 'NYAM', 'LUNCH', 'NYPM']
        
        print("🔄 PREDICTION PIPELINE")
        print("=" * 30)
        print("Temporal Flow: MIDNIGHT → ASIA → LONDON → PREMARKET → NYAM")
        print("Components: PDA Parser + Static Probabilities")
        print("Pattern Database: 18 high-confidence patterns")
        print()
        
        self.logger.info("🔄 Prediction Pipeline: Initialized")
    
    def _load_pattern_probabilities(self) -> Dict[str, float]:
        """Load static pattern probabilities from historical analysis"""
        
        # High-confidence patterns with validated probabilities
        # These come from the 58-session statistical analysis
        return {
            'CONSOLIDATION_EXPANSION_REDELIVERY': 0.93,
            'FPFVG_INTERACTION_REDELIVERY': 0.92,
            'EXPANSION_HIGH_REVERSAL': 0.89,
            'OPEN_CONSOLIDATION_EXPANSION': 0.87,
            'REDELIVERY_EXPANSION_TAKEOUT': 0.86,
            'INTERACTION_TAKEOUT': 0.85,
            'CONSOLIDATION_FPFVG_FORMATION': 0.84,
            'EXPANSION_LOW_REBALANCE': 0.83,
            'OPEN_EXPANSION_HIGH': 0.82,
            'REDELIVERY_REBALANCE': 0.81,
            'CONSOLIDATION_REVERSAL': 0.80,
            'FPFVG_FORMATION_INTERACTION': 0.80,
            'EXPANSION_CONSOLIDATION': 0.80,
            'TAKEOUT_EXPANSION_HIGH': 0.80,
            'REBALANCE_EXPANSION_LOW': 0.80,
            'REVERSAL_CONSOLIDATION': 0.80,
            'INTERACTION_REDELIVERY': 0.80,
            'LIQUIDITY_GRAB_EXPANSION': 0.80,
        }
    
    def load_overnight_sessions(self, date: str) -> Dict[str, Dict]:
        """Load all overnight sessions for given date with proper temporal ordering"""
        
        print(f"📁 Loading overnight sessions for {date}...")
        
        overnight_sessions = {}
        session_types = ['MIDNIGHT', 'ASIA', 'LONDON', 'PREMARKET']
        
        for session_type in session_types:
            # Try different file naming patterns
            possible_files = [
                f"{session_type}_Lvl-1_{date}.json",
                f"enhanced_{session_type}_Lvl-1_{date}.json",
                f"enhanced_sessions/enhanced_{session_type}_Lvl-1_{date}.json"
            ]
            
            loaded = False
            for file_path in possible_files:
                try:
                    if Path(file_path).exists():
                        with open(file_path, 'r') as f:
                            overnight_sessions[session_type] = json.load(f)
                        print(f"✅ {session_type}: {file_path}")
                        loaded = True
                        break
                except Exception as e:
                    continue
            
            if not loaded:
                print(f"⚠️ {session_type}: Not found")
        
        return overnight_sessions
    
    def extract_state_vector(self, overnight_sessions: Dict[str, Dict]) -> StateVector:
        """Extract market state vector from overnight session flow"""
        
        print("🔍 Extracting state vector from overnight flow...")
        
        all_patterns = []
        momentum_scores = {}
        energy_total = 0.0
        fpfvg_states = {}
        liquidity_events = []
        transitions = []
        
        # Process sessions in temporal order
        for session_type in ['MIDNIGHT', 'ASIA', 'LONDON', 'PREMARKET']:
            if session_type not in overnight_sessions:
                continue
            
            session_data = overnight_sessions[session_type]
            
            # Extract patterns using cascade predictor
            result = self.cascade_predictor.predict_cascade(session_data)
            current_events = result.get('current_events', [])
            
            if current_events:
                all_patterns.extend(current_events)
                
                # Calculate momentum for this session
                expansion_events = [e for e in current_events if 'EXPANSION' in e]
                consolidation_events = [e for e in current_events if 'CONSOLIDATION' in e]
                
                momentum_scores[session_type] = len(expansion_events) - len(consolidation_events)
                
                # Track session transition
                if current_events:
                    transitions.append(f"{session_type}:{current_events[-1]}")
            
            # Extract energy and FPFVG data if available
            if 'level1_json' in session_data:
                level1 = session_data['level1_json']
                
                # Energy state
                energy_state = level1.get('energy_state', {})
                if energy_state:
                    session_energy = energy_state.get('total_accumulated', 0)
                    energy_total += session_energy
                
                # FPFVG state
                fpfvg_data = level1.get('session_fpfvg', {})
                if fpfvg_data and fpfvg_data.get('fpfvg_present'):
                    fpfvg_states[session_type] = {
                        'formation_time': fpfvg_data.get('fpfvg_formation', {}).get('formation_time'),
                        'gap_size': fpfvg_data.get('fpfvg_formation', {}).get('gap_size', 0),
                        'premium_high': fpfvg_data.get('fpfvg_formation', {}).get('premium_high'),
                        'discount_low': fpfvg_data.get('fpfvg_formation', {}).get('discount_low')
                    }
                
                # Liquidity events
                liq_events = level1.get('session_liquidity_events', [])
                liquidity_events.extend(liq_events)
        
        # Compile state vector
        state_vector = StateVector(
            overnight_patterns=all_patterns,
            energy_carryover=energy_total,
            momentum_indicators=momentum_scores,
            fpfvg_state=fpfvg_states,
            liquidity_conditions={'total_events': len(liquidity_events)},
            session_transitions=transitions,
            timestamp=datetime.now()
        )
        
        print(f"✅ State vector extracted:")
        print(f"   Total patterns: {len(all_patterns)}")
        print(f"   Energy carryover: {energy_total:.1f}")
        print(f"   Active FPFVGs: {len(fpfvg_states)}")
        print(f"   Session transitions: {len(transitions)}")
        
        return state_vector
    
    def load_previous_session_close(self, target_session: str, date: str) -> Dict[str, Any]:
        """Load previous session's closing state for carryover analysis"""
        
        # Determine previous session
        if target_session == 'NYAM':
            prev_session = 'PREMARKET'
            prev_date = date  # Same day
        elif target_session == 'LUNCH':
            prev_session = 'NYAM'
            prev_date = date
        elif target_session == 'NYPM':
            prev_session = 'LUNCH'  
            prev_date = date
        else:
            return {}
        
        # Try to load previous session
        prev_files = [
            f"{prev_session}_Lvl-1_{prev_date}.json",
            f"enhanced_sessions/enhanced_{prev_session}_Lvl-1_{prev_date}.json"
        ]
        
        for file_path in prev_files:
            try:
                if Path(file_path).exists():
                    with open(file_path, 'r') as f:
                        prev_data = json.load(f)
                    
                    # Extract closing state
                    close_state = {
                        'session_type': prev_session,
                        'final_events': [],
                        'energy_state': {},
                        'fpfvg_carryover': {}
                    }
                    
                    # Get final events
                    result = self.cascade_predictor.predict_cascade(prev_data)
                    close_state['final_events'] = result.get('current_events', [])[-3:]  # Last 3 events
                    
                    # Get energy and FPFVG carryover
                    if 'level1_json' in prev_data:
                        level1 = prev_data['level1_json']
                        close_state['energy_state'] = level1.get('energy_state', {})
                        close_state['fpfvg_carryover'] = level1.get('session_fpfvg', {})
                    
                    print(f"✅ Previous session loaded: {prev_session}")
                    return close_state
                    
            except Exception as e:
                continue
        
        print(f"⚠️ Previous session not found: {prev_session}")
        return {}
    
    def apply_pda_pattern_recognition(self, state_vector: StateVector) -> Tuple[str, List[str], float]:
        """Apply PDA parser to identify patterns in overnight flow"""
        
        start_time = datetime.now()
        print("🧠 Applying PDA pattern recognition...")
        
        # Get recent pattern sequence (last 8 events for analysis)
        recent_patterns = state_vector.overnight_patterns[-8:] if len(state_vector.overnight_patterns) > 8 else state_vector.overnight_patterns
        
        if not recent_patterns:
            return "NO_PATTERN", [], 0.0
        
        # Use PDA parser for pattern identification
        try:
            pda_result = self.pda_parser.parse_cascade_sequence(recent_patterns)
            
            parsing_time = (datetime.now() - start_time).total_seconds() * 1000
            
            if pda_result.parsing_success:
                pattern_id = pda_result.matched_pattern
                confidence = pda_result.confidence
                
                print(f"✅ PDA pattern identified: {pattern_id}")
                print(f"   Confidence: {confidence:.3f}")
                print(f"   Parse time: {parsing_time:.2f}ms")
                
                return pattern_id, recent_patterns, confidence
            else:
                # Fallback to heuristic pattern matching
                pattern_id = self._heuristic_pattern_match(recent_patterns)
                confidence = 0.6  # Lower confidence for heuristic
                
                print(f"⚠️ PDA parsing failed, using heuristic: {pattern_id}")
                return pattern_id, recent_patterns, confidence
                
        except Exception as e:
            print(f"❌ PDA error: {e}")
            pattern_id = self._heuristic_pattern_match(recent_patterns)
            return pattern_id, recent_patterns, 0.5
    
    def _heuristic_pattern_match(self, events: List[str]) -> str:
        """Fallback heuristic pattern matching"""
        
        if not events:
            return "NO_PATTERN"
        
        # Check for common 2-3 event patterns
        if len(events) >= 2:
            last_two = events[-2:]
            pattern_key = '_'.join(last_two)
            
            # Map to known pattern IDs
            pattern_mappings = {
                'CONSOLIDATION_EXPANSION': 'CONSOLIDATION_EXPANSION_REDELIVERY',
                'EXPANSION_HIGH_REVERSAL': 'EXPANSION_HIGH_REVERSAL',
                'FPFVG_INTERACTION': 'FPFVG_INTERACTION_REDELIVERY',
                'OPEN_CONSOLIDATION': 'OPEN_CONSOLIDATION_EXPANSION',
                'INTERACTION_TAKEOUT': 'INTERACTION_TAKEOUT'
            }
            
            if pattern_key in pattern_mappings:
                return pattern_mappings[pattern_key]
        
        # Default based on last event
        last_event = events[-1]
        if 'EXPANSION' in last_event:
            return 'EXPANSION_CONSOLIDATION'
        elif 'CONSOLIDATION' in last_event:
            return 'CONSOLIDATION_EXPANSION_REDELIVERY' 
        elif 'REDELIVERY' in last_event:
            return 'REDELIVERY_EXPANSION_TAKEOUT'
        else:
            return 'UNKNOWN_PATTERN'
    
    def assign_static_probability(self, pattern_id: str, state_vector: StateVector) -> float:
        """Assign static probability based on pattern ID and basic context"""
        
        # Get base probability
        base_prob = self.pattern_probabilities.get(pattern_id, 0.5)
        
        # Apply simple context adjustments
        adjusted_prob = base_prob
        
        # Energy adjustment
        if state_vector.energy_carryover > 100:  # High energy
            adjusted_prob *= 1.1
        elif state_vector.energy_carryover < 20:  # Low energy
            adjusted_prob *= 0.9
        
        # Momentum adjustment
        total_momentum = sum(state_vector.momentum_indicators.values())
        if total_momentum > 2:  # Strong positive momentum
            adjusted_prob *= 1.05
        elif total_momentum < -2:  # Strong negative momentum
            adjusted_prob *= 0.95
        
        # FPFVG adjustment
        if state_vector.fpfvg_state and 'FPFVG' in pattern_id:
            adjusted_prob *= 1.15  # Boost FPFVG patterns when FPFVG is present
        
        # Clamp probability to valid range
        adjusted_prob = max(0.1, min(0.95, adjusted_prob))
        
        print(f"📊 Probability assignment:")
        print(f"   Base: {base_prob:.3f}")
        print(f"   Adjusted: {adjusted_prob:.3f}")
        
        return adjusted_prob
    
    def predict_session(self, target_session: str, date: str) -> PipelinePrediction:
        """
        Main prediction function with proper temporal flow
        
        Args:
            target_session: 'NYAM', 'LUNCH', or 'NYPM'
            date: Date in 'YYYY_MM_DD' format
            
        Returns:
            Complete prediction result
        """
        
        total_start_time = datetime.now()
        
        print(f"🎯 PREDICTING {target_session} SESSION FOR {date}")
        print("=" * 50)
        
        # Step 1: Load overnight sessions
        overnight_sessions = self.load_overnight_sessions(date)
        
        if not overnight_sessions:
            print("❌ No overnight sessions found")
            return None
        
        # Step 2: Extract state vector
        state_vector = self.extract_state_vector(overnight_sessions)
        
        # Step 3: Load previous session close state
        prev_close = self.load_previous_session_close(target_session, date)
        
        # Step 4: Apply PDA pattern recognition
        pda_start = datetime.now()
        pattern_id, pattern_sequence, pattern_confidence = self.apply_pda_pattern_recognition(state_vector)
        pda_time = (datetime.now() - pda_start).total_seconds() * 1000
        
        # Step 5: Assign static probability
        static_prob = self.assign_static_probability(pattern_id, state_vector)
        
        # Step 6: Generate final prediction
        total_time = (datetime.now() - total_start_time).total_seconds() * 1000
        
        prediction = PipelinePrediction(
            target_session=target_session,
            target_date=date,
            prediction_time=datetime.now(),
            identified_pattern_id=pattern_id,
            pattern_sequence=pattern_sequence,
            pattern_confidence=pattern_confidence,
            static_probability=static_prob,
            final_probability=static_prob,  # Same as static for now
            overnight_state=state_vector,
            previous_close_state=prev_close,
            pda_time_ms=pda_time,
            total_time_ms=total_time,
            method_used="PDA_STATIC"
        )
        
        self._display_prediction_result(prediction)
        
        return prediction
    
    def _display_prediction_result(self, prediction: PipelinePrediction):
        """Display formatted prediction results"""
        
        print(f"\n🎯 {prediction.target_session} PREDICTION RESULT")
        print("=" * 35)
        print(f"📅 Date: {prediction.target_date}")
        print(f"⏰ Prediction Time: {prediction.prediction_time.strftime('%H:%M:%S')}")
        print(f"\n📊 PATTERN ANALYSIS:")
        print(f"   Identified Pattern: {prediction.identified_pattern_id}")
        print(f"   Pattern Sequence: {' → '.join(prediction.pattern_sequence)}")
        print(f"   Pattern Confidence: {prediction.pattern_confidence:.3f}")
        print(f"\n🎲 CASCADE PROBABILITY:")
        print(f"   Static Probability: {prediction.static_probability:.1%}")
        print(f"   Final Probability: {prediction.final_probability:.1%}")
        print(f"\n⚡ PERFORMANCE:")
        print(f"   PDA Time: {prediction.pda_time_ms:.2f}ms")
        print(f"   Total Time: {prediction.total_time_ms:.2f}ms")
        print(f"   Method: {prediction.method_used}")
        print(f"\n📈 STATE CONTEXT:")
        print(f"   Energy Carryover: {prediction.overnight_state.energy_carryover:.1f}")
        print(f"   Active FPFVGs: {len(prediction.overnight_state.fpfvg_state)}")
        print(f"   Session Transitions: {len(prediction.overnight_state.session_transitions)}")
        
        # Trading recommendation
        prob = prediction.final_probability
        if prob >= 0.8:
            recommendation = "🟢 HIGH CONFIDENCE - Position for cascade"
        elif prob >= 0.65:
            recommendation = "🟡 MODERATE CONFIDENCE - Monitor for confirmation"
        else:
            recommendation = "🔴 LOW CONFIDENCE - Wait for clearer signals"
        
        print(f"\n💡 RECOMMENDATION:")
        print(f"   {recommendation}")

def demo_prediction_pipeline():
    """Demonstrate the prediction pipeline"""
    
    print("🧪 PREDICTION PIPELINE DEMONSTRATION")
    print("=" * 45)
    
    pipeline = PredictionPipeline()
    
    # Test prediction for NYAM on August 7th
    prediction = pipeline.predict_session("NYAM", "2025_08_07")
    
    if prediction:
        print(f"\n✅ Pipeline demonstration complete")
        return prediction
    else:
        print(f"\n❌ Pipeline demonstration failed")
        return None

def main():
    """Main function for prediction pipeline"""
    
    # Run demonstration
    result = demo_prediction_pipeline()
    
    return result

if __name__ == "__main__":
    pipeline_result = main()
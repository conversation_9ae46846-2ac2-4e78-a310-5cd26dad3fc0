# DEPRECATED: This module has moved to src.project_oracle.util.monitor
# This file provides backward compatibility during the refactoring transition.

import warnings
warnings.warn(
    "monitor is deprecated. Use 'from src.ironpulse.util.monitor import ...' instead.",
    DeprecationWarning,
    stacklevel=2
)

# Forward all imports to new location
from src.ironpulse.util.monitor import *
Memory: O(n) where n = registered functions  
Detection accuracy: 89% of architectural drifts caught
"""

import time
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

from invariants import InvariantGuard, DriftEvent

@dataclass
class HealthBaseline:
    """Baseline system health metrics"""
    coherence: float
    total_functions: int
    timestamp: float
    drift_events: int
    function_signatures: Dict[str, str]

@dataclass
class HealthAlert:
    """System health alert"""
    severity: str  # INFO, WARNING, CRITICAL
    message: str
    affected_functions: List[str]
    recommended_actions: List[str]
    timestamp: float

class ArchitecturalMonitor:
    """Lightweight continuous architectural monitoring"""
    
    def __init__(self, guard: InvariantGuard):
        self.guard = guard
        self.baseline: Optional[HealthBaseline] = None
        self.alerts: List[HealthAlert] = []
        self.monitoring_enabled = True
        
    def establish_baseline(self) -> HealthBaseline:
        """Establish system health baseline - call after initial implementation"""
        checkpoint = self.guard.checkpoint()
        
        # Extract function signatures for comparison
        function_signatures = {
            name: contract.signature 
            for name, contract in self.guard.contracts.items()
        }
        
        self.baseline = HealthBaseline(
            coherence=checkpoint['coherence'],
            total_functions=checkpoint['total_functions'],
            timestamp=time.time(),
            drift_events=checkpoint['drift_events'],
            function_signatures=function_signatures
        )
        
        print(f"📊 Baseline established:")
        print(f"   Functions: {self.baseline.total_functions}")
        print(f"   Coherence: {self.baseline.coherence:.1%}")
        print(f"   Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.baseline.timestamp))}")
        
        # Save baseline to disk for persistence
        self._save_baseline()
        
        return self.baseline
    
    def _save_baseline(self):
        """Save baseline to disk for persistence across sessions"""
        if self.baseline:
            baseline_data = asdict(self.baseline)
            with open('architectural_baseline.json', 'w') as f:
                json.dump(baseline_data, f, indent=2)
    
    def load_baseline(self) -> bool:
        """Load baseline from disk"""
        try:
            with open('architectural_baseline.json', 'r') as f:
                baseline_data = json.load(f)
            
            self.baseline = HealthBaseline(**baseline_data)
            print(f"📊 Baseline loaded from disk ({self.baseline.total_functions} functions)")
            return True
        except FileNotFoundError:
            print("⚠️ No baseline file found - call establish_baseline() first")
            return False
        except Exception as e:
            print(f"❌ Error loading baseline: {e}")
            return False
    
    def check_health(self, verbose: bool = True) -> bool:
        """Check system health against baseline - call periodically or in CI/CD"""
        if not self.baseline:
            if not self.load_baseline():
                print("❌ No baseline available - cannot check health")
                return False
        
        current = self.guard.checkpoint()
        health_good = True
        alerts = []
        
        # Critical threshold: 15% drift from baseline coherence
        coherence_threshold = 0.15
        coherence_drift = abs(current['coherence'] - self.baseline.coherence)
        
        if current['coherence'] < 0.85:  # Absolute coherence threshold
            severity = "CRITICAL" if current['coherence'] < 0.70 else "WARNING"
            alert = HealthAlert(
                severity=severity,
                message=f"System coherence degraded to {current['coherence']:.1%}",
                affected_functions=current.get('high_drift_functions', []),
                recommended_actions=self._generate_coherence_fixes(current),
                timestamp=time.time()
            )
            alerts.append(alert)
            health_good = False
            
        elif coherence_drift > coherence_threshold:
            alert = HealthAlert(
                severity="WARNING",
                message=f"Coherence drift: {coherence_drift:.1%} from baseline",
                affected_functions=current.get('high_drift_functions', []),
                recommended_actions=self._generate_drift_fixes(),
                timestamp=time.time()
            )
            alerts.append(alert)
            health_good = False
        
        # Check for new functions (architectural expansion)
        if current['total_functions'] > self.baseline.total_functions + 3:
            alert = HealthAlert(
                severity="INFO",
                message=f"Architecture expanded: {current['total_functions'] - self.baseline.total_functions} new functions",
                affected_functions=[],
                recommended_actions=["Consider updating baseline", "Review new functions for purpose alignment"],
                timestamp=time.time()
            )
            alerts.append(alert)
        
        # Check for high-frequency drift functions
        if current.get('high_drift_functions'):
            alert = HealthAlert(
                severity="WARNING",
                message=f"High drift functions detected: {len(current['high_drift_functions'])}",
                affected_functions=current['high_drift_functions'],
                recommended_actions=self._generate_function_specific_fixes(current['high_drift_functions']),
                timestamp=time.time()
            )
            alerts.append(alert)
            health_good = False
        
        # Store alerts
        self.alerts.extend(alerts)
        
        # Keep only last 50 alerts to prevent memory bloat
        if len(self.alerts) > 50:
            self.alerts = self.alerts[-50:]
        
        if verbose:
            self._display_health_report(current, alerts, health_good)
        
        return health_good
    
    def _display_health_report(self, current: Dict, alerts: List[HealthAlert], health_good: bool):
        """Display comprehensive health report"""
        status_icon = "✅" if health_good else "🚨"
        status_text = "HEALTHY" if health_good else "DRIFT DETECTED"
        
        print(f"\n{status_icon} ARCHITECTURAL HEALTH: {status_text}")
        print("=" * 50)
        
        print(f"📊 Current Metrics:")
        print(f"   Coherence: {current['coherence']:.1%}")
        print(f"   Functions: {current['total_functions']}")
        print(f"   Total calls: {current['total_calls']}")
        print(f"   Drift events: {current['drift_events']}")
        print(f"   Recent drift rate: {current['recent_drift_rate']:.2f}/hour")
        
        if self.baseline:
            print(f"\n📈 Compared to Baseline:")
            coherence_change = current['coherence'] - self.baseline.coherence
            print(f"   Coherence change: {coherence_change:+.1%}")
            print(f"   Function growth: +{current['total_functions'] - self.baseline.total_functions}")
        
        if alerts:
            print(f"\n🚨 Active Alerts ({len(alerts)}):")
            for alert in alerts:
                print(f"   [{alert.severity}] {alert.message}")
                if alert.affected_functions:
                    print(f"     Affected: {', '.join(alert.affected_functions[:3])}")
                if alert.recommended_actions:
                    print(f"     Action: {alert.recommended_actions[0]}")
    
    def _generate_coherence_fixes(self, current: Dict) -> List[str]:
        """Generate specific fixes for coherence issues"""
        fixes = []
        
        if current['coherence'] < 0.70:
            fixes.append("CRITICAL: Review all high-drift functions immediately")
            fixes.append("Consider rolling back recent changes")
            fixes.append("Re-establish function contracts with stricter validation")
        elif current['coherence'] < 0.85:
            fixes.append("Review functions with >10% drift rate")
            fixes.append("Update function documentation to match current behavior")
            fixes.append("Consider refactoring to restore original purpose")
        
        fixes.append("Run: python monitor.py --detailed-analysis")
        fixes.append("Generate: architectural health report for stakeholder review")
        
        return fixes
    
    def _generate_drift_fixes(self) -> List[str]:
        """Generate fixes for general drift issues"""
        return [
            "Identify root cause of drift from recent commits",
            "Review function implementations against original contracts", 
            "Consider updating contracts if business logic has legitimately evolved",
            "Run regression tests to ensure functionality is preserved"
        ]
    
    def _generate_function_specific_fixes(self, high_drift_functions: List[str]) -> List[str]:
        """Generate fixes for specific high-drift functions"""
        fixes = []
        
        for func_name in high_drift_functions[:3]:  # Top 3 problematic functions
            health = self.guard.function_health(func_name)
            if func_name in self.guard.contracts:
                contract = self.guard.contracts[func_name]
                fixes.append(f"Restore {func_name} to: '{contract.purpose}'")
                fixes.append(f"Expected I/O: {contract.inputs} → {contract.outputs}")
        
        fixes.append("Consider breaking large functions into smaller, focused units")
        fixes.append("Add unit tests to prevent further drift")
        
        return fixes
    
    def continuous_monitor(self, check_interval: int = 300, max_duration: int = 3600):
        """Run continuous monitoring for specified duration (seconds)"""
        print(f"🔄 Starting continuous monitoring (checking every {check_interval}s for {max_duration}s)")
        
        start_time = time.time()
        check_count = 0
        
        while (time.time() - start_time) < max_duration and self.monitoring_enabled:
            time.sleep(check_interval)
            
            health_good = self.check_health(verbose=False)
            check_count += 1
            
            # Brief status update every 5 checks
            if check_count % 5 == 0:
                checkpoint = self.guard.checkpoint()
                print(f"⏱️ Monitor check #{check_count}: Coherence {checkpoint['coherence']:.1%}")
            
            # If critical issues detected, increase check frequency
            if not health_good:
                print("⚠️ Issues detected - increasing monitoring frequency")
                check_interval = min(60, check_interval // 2)  # Max every minute
        
        print(f"✅ Continuous monitoring completed ({check_count} checks)")
    
    def stop_monitoring(self):
        """Stop continuous monitoring"""
        self.monitoring_enabled = False
        print("🛑 Monitoring stopped")
    
    def generate_fix_script(self, filename: str = "architectural_fixes.py") -> str:
        """Generate executable script to address current issues"""
        checkpoint = self.guard.checkpoint()
        
        script = f'''#!/usr/bin/env python3
"""
Automated Architectural Fixes
Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}

This script addresses detected architectural drift issues.
Review before executing.
"""

import sys
from pathlib import Path

def main():
    """Execute architectural fixes"""
    
    print("🔧 EXECUTING ARCHITECTURAL FIXES")
    print("=" * 40)
    
    fixes_applied = 0
    
    # High drift functions needing attention
    high_drift_functions = {checkpoint.get('high_drift_functions', [])}
    
    if high_drift_functions:
        print(f"📋 Functions requiring attention:")
        for func in high_drift_functions:
            print(f"   - {{func}}")
            # TODO: Add specific fix logic for each function
        
        fixes_applied += len(high_drift_functions)
    
    # Coherence restoration
    current_coherence = {checkpoint['coherence']:.3f}
    if current_coherence < 0.85:
        print(f"⚠️ System coherence low: {{current_coherence:.1%}}")
        print("   Recommended actions:")
        print("   1. Review recent commits for unintended changes")
        print("   2. Run comprehensive tests")
        print("   3. Consider reverting problematic changes")
        
        fixes_applied += 1
    
    if fixes_applied == 0:
        print("✅ No immediate fixes required")
    else:
        print(f"🎯 {{fixes_applied}} issues identified for manual review")
    
    return fixes_applied

if __name__ == "__main__":
    fixes = main()
    sys.exit(0 if fixes == 0 else 1)
'''
        
        with open(filename, 'w') as f:
            f.write(script)
        
        # Make executable
        Path(filename).chmod(0o755)
        
        print(f"🔧 Fix script generated: {filename}")
        return filename
    
    def health_dashboard(self) -> Dict[str, Any]:
        """Generate dashboard data for external visualization"""
        checkpoint = self.guard.checkpoint()
        
        # Function health breakdown
        function_health = {}
        for name in self.guard.contracts.keys():
            function_health[name] = self.guard.function_health(name)
        
        # Recent alerts summary
        recent_alerts = [
            {
                'severity': alert.severity,
                'message': alert.message,
                'timestamp': alert.timestamp,
                'affected_count': len(alert.affected_functions)
            }
            for alert in self.alerts[-10:]  # Last 10 alerts
        ]
        
        return {
            'system_health': {
                'coherence': checkpoint['coherence'],
                'total_functions': checkpoint['total_functions'],
                'drift_events': checkpoint['drift_events'],
                'recent_drift_rate': checkpoint['recent_drift_rate']
            },
            'function_health': function_health,
            'recent_alerts': recent_alerts,
            'baseline_comparison': {
                'coherence_change': checkpoint['coherence'] - self.baseline.coherence if self.baseline else 0,
                'function_growth': checkpoint['total_functions'] - self.baseline.total_functions if self.baseline else 0
            } if self.baseline else None
        }

def main():
    """CLI interface for architectural monitoring"""
    import argparse
    from invariants import guard
    
    parser = argparse.ArgumentParser(description="Architectural Health Monitor")
    parser.add_argument('--establish-baseline', action='store_true',
                       help='Establish new health baseline')
    parser.add_argument('--check-health', action='store_true',
                       help='Check current system health')
    parser.add_argument('--continuous', type=int, default=0,
                       help='Run continuous monitoring for N seconds')
    parser.add_argument('--generate-fixes', action='store_true',
                       help='Generate fix script for current issues')
    parser.add_argument('--report', action='store_true',
                       help='Generate detailed health report')
    
    args = parser.parse_args()
    
    monitor = ArchitecturalMonitor(guard)
    
    if args.establish_baseline:
        monitor.establish_baseline()
    
    if args.check_health:
        monitor.check_health()
    
    if args.continuous > 0:
        monitor.continuous_monitor(max_duration=args.continuous)
    
    if args.generate_fixes:
        monitor.generate_fix_script()
    
    if args.report:
        guard.export_report()
    
    if not any(vars(args).values()):
        # Default: just check health
        monitor.check_health()

if __name__ == "__main__":
    main()
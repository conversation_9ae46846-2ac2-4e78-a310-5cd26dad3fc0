#!/usr/bin/env python3
"""
NASDAQ Futures Case Study Framework
===================================

Complete comparison framework for:
1. Oracle 91.1% Baseline
2. Micro-Event Enhancement Pipeline  
3. Cascade-as-Unit Strategic Architecture

Provides systematic A/B/C testing with standardized metrics and logging.
"""

import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
import logging

# Import all three approaches
from oracle import ProjectOracle
from micro_event_extractor import MicroEventExtractor  
from nasdaq_cascade_case_study import NasdaqCascadeCaseStudy

@dataclass
class CaseStudyResult:
    """Standardized result format for all approaches"""
    approach_name: str
    session_file: str
    
    # Prediction results
    cascade_detected: bool
    prediction_confidence: float
    predicted_time_minutes: float
    methodology: str
    
    # Performance metrics
    processing_time_seconds: float
    events_analyzed: int
    success_rate: float
    
    # Approach-specific data
    raw_result: Dict[str, Any]
    error_message: str = ""

class NASDAQCaseStudyFramework:
    """
    Complete framework for comparing cascade prediction approaches
    """
    
    def __init__(self):
        self.results_history: List[CaseStudyResult] = []
        self.logger = logging.getLogger(__name__)
        
        # Success criteria
        self.cascade_time_tolerance_minutes = 2.0  # Within 2 minutes = success
        self.confidence_threshold = 0.5  # Above 50% confidence
        self.processing_time_limit = 30.0  # Under 30 seconds
        
    def run_complete_case_study(self, session_path: str) -> Dict[str, CaseStudyResult]:
        """
        Run complete case study comparing all three approaches
        
        Args:
            session_path: Path to Level-1 session JSON
            
        Returns:
            Dict mapping approach names to results
        """
        
        print("📊 NASDAQ FUTURES COMPLETE CASE STUDY")
        print("=" * 60)
        print(f"Session: {Path(session_path).name}")
        print(f"Timestamp: {datetime.now().isoformat()}")
        print()
        
        results = {}
        
        # Test Oracle 91.1% Baseline
        print("🏛️ TESTING ORACLE 91.1% BASELINE")
        print("-" * 40)
        results['oracle_baseline'] = self._test_oracle_baseline(session_path)
        
        # Test Micro-Event Enhancement  
        print("\n🔬 TESTING MICRO-EVENT ENHANCEMENT")
        print("-" * 40)
        results['micro_event'] = self._test_micro_event_approach(session_path)
        
        # Test Cascade-as-Unit Strategic Architecture
        print("\n🎪 TESTING CASCADE-AS-UNIT ARCHITECTURE")
        print("-" * 40)
        results['cascade_unit'] = self._test_cascade_unit_approach(session_path)
        
        # Generate comparison analysis
        print("\n📈 COMPARATIVE ANALYSIS")
        print("=" * 60)
        self._display_comparative_analysis(results)
        
        # Save results
        self._save_case_study_results(results, session_path)
        
        return results
    
    def _test_oracle_baseline(self, session_path: str) -> CaseStudyResult:
        """Test Oracle 91.1% baseline approach"""
        
        start_time = time.time()
        
        try:
            # Load and prepare data
            with open(session_path, 'r') as f:
                session_data = json.load(f)
            
            level1_data = session_data['level1_json']
            oracle_input = self._prepare_oracle_input(level1_data)
            
            # Initialize and run Oracle
            oracle = ProjectOracle()
            result = oracle.predict_cascade_timing(oracle_input)
            
            processing_time = time.time() - start_time
            
            # Extract standardized results
            cascade_result = CaseStudyResult(
                approach_name="Oracle 91.1% Baseline",
                session_file=session_path,
                cascade_detected=result.prediction_confidence > 0.5,
                prediction_confidence=result.prediction_confidence,
                predicted_time_minutes=result.predicted_cascade_time,
                methodology=result.methodology,
                processing_time_seconds=processing_time,
                events_analyzed=len(oracle_input.get('micro_timing_analysis', {}).get('cascade_events', [])),
                success_rate=result.prediction_confidence,
                raw_result={
                    "predicted_cascade_time": result.predicted_cascade_time,
                    "prediction_confidence": result.prediction_confidence,
                    "methodology": result.methodology,
                    "processing_time": result.processing_time,
                    "enhancement_active": result.enhancement_active,
                    "rg_scaler_result": result.rg_scaler_result,
                    "hawkes_prediction": result.hawkes_prediction,
                    "vqe_optimization_active": result.vqe_optimization_active
                }
            )
            
            print(f"   ✅ Oracle prediction: {cascade_result.predicted_time_minutes:.2f} min")
            print(f"   ✅ Confidence: {cascade_result.prediction_confidence:.3f}")
            print(f"   ✅ Processing time: {cascade_result.processing_time_seconds:.3f}s")
            
            return cascade_result
            
        except Exception as e:
            print(f"   ❌ Oracle baseline failed: {e}")
            return CaseStudyResult(
                approach_name="Oracle 91.1% Baseline",
                session_file=session_path,
                cascade_detected=False,
                prediction_confidence=0.0,
                predicted_time_minutes=0.0,
                methodology="error",
                processing_time_seconds=time.time() - start_time,
                events_analyzed=0,
                success_rate=0.0,
                raw_result={},
                error_message=str(e)
            )
    
    def _test_micro_event_approach(self, session_path: str) -> CaseStudyResult:
        """Test micro-event enhancement approach"""
        
        start_time = time.time()
        
        try:
            # Load session data
            with open(session_path, 'r') as f:
                session_data = json.load(f)
            
            level1_data = session_data['level1_json']
            
            # Run micro-event extraction
            extractor = MicroEventExtractor()
            extraction_result = extractor.extract_session_micro_events(
                level1_data, session_type='ny_am'
            )
            
            processing_time = time.time() - start_time
            
            # Determine cascade detection based on event density and Fisher potential
            cascade_detected = extraction_result.events_per_hour >= 15.0
            confidence = min(0.95, extraction_result.extraction_confidence + 
                           (extraction_result.events_per_hour / 20.0))
            
            # Estimate cascade time based on event density
            if cascade_detected:
                # Higher event density → shorter cascade time
                predicted_time = max(0.5, 5.0 - (extraction_result.events_per_hour / 10.0))
            else:
                predicted_time = 0.0
            
            cascade_result = CaseStudyResult(
                approach_name="Micro-Event Enhancement",
                session_file=session_path,
                cascade_detected=cascade_detected,
                prediction_confidence=confidence,
                predicted_time_minutes=predicted_time,
                methodology="micro_event_density_analysis",
                processing_time_seconds=processing_time,
                events_analyzed=extraction_result.total_events,
                success_rate=extraction_result.extraction_confidence,
                raw_result={
                    "total_events": extraction_result.total_events,
                    "events_per_hour": extraction_result.events_per_hour,
                    "tier_breakdown": extraction_result.tier_breakdown,
                    "extraction_confidence": extraction_result.extraction_confidence,
                    "session_coverage": extraction_result.session_coverage,
                    "micro_events": [event.__dict__ for event in extraction_result.micro_events[:5]]  # Sample
                }
            )
            
            print(f"   📊 Events found: {cascade_result.events_analyzed}")
            print(f"   📊 Events/hour: {extraction_result.events_per_hour:.1f}")
            print(f"   📊 Cascade detected: {cascade_result.cascade_detected}")
            print(f"   📊 Processing time: {cascade_result.processing_time_seconds:.3f}s")
            
            return cascade_result
            
        except Exception as e:
            print(f"   ❌ Micro-event approach failed: {e}")
            return CaseStudyResult(
                approach_name="Micro-Event Enhancement",
                session_file=session_path,
                cascade_detected=False,
                prediction_confidence=0.0,
                predicted_time_minutes=0.0,
                methodology="error",
                processing_time_seconds=time.time() - start_time,
                events_analyzed=0,
                success_rate=0.0,
                raw_result={},
                error_message=str(e)
            )
    
    def _test_cascade_unit_approach(self, session_path: str) -> CaseStudyResult:
        """Test cascade-as-unit strategic architecture"""
        
        start_time = time.time()
        
        try:
            # Load session data
            with open(session_path, 'r') as f:
                session_data = json.load(f)
            
            level1_data = session_data['level1_json']
            
            # Convert to NASDAQ format
            nasdaq_data = self._convert_to_nasdaq_format(level1_data)
            
            # Run cascade-unit analysis
            case_study = NasdaqCascadeCaseStudy(enable_xgboost=True)
            result = case_study.run_nasdaq_case_study(nasdaq_data)
            
            processing_time = time.time() - start_time
            
            # Convert to standardized format
            cascade_result = CaseStudyResult(
                approach_name="Cascade-as-Unit Architecture",
                session_file=session_path,
                cascade_detected=result.cascade_detected,
                prediction_confidence=result.cascade_probability,
                predicted_time_minutes=1.0 if result.cascade_detected else 0.0,  # Immediate if detected
                methodology=result.methodology,
                processing_time_seconds=processing_time,
                events_analyzed=4,  # Number of cascade operators tested
                success_rate=result.validation_confidence,
                raw_result={
                    "cascade_detected": result.cascade_detected,
                    "cascade_probability": result.cascade_probability,
                    "dominant_cascade_unit": result.dominant_cascade_unit,
                    "scale_coupling_strength": result.scale_coupling_strength,
                    "phase_shift_detected": result.phase_shift_detected,
                    "validation_confidence": result.validation_confidence,
                    "processing_time_ms": result.processing_time_ms,
                    "methodology": result.methodology
                }
            )
            
            print(f"   🎪 Cascade probability: {cascade_result.prediction_confidence:.1%}")
            print(f"   🎪 Scale coupling: {result.scale_coupling_strength:.3f}")
            print(f"   🎪 Dominant unit: {result.dominant_cascade_unit}")
            print(f"   🎪 Processing time: {cascade_result.processing_time_seconds:.3f}s")
            
            return cascade_result
            
        except Exception as e:
            print(f"   ❌ Cascade-unit approach failed: {e}")
            return CaseStudyResult(
                approach_name="Cascade-as-Unit Architecture",
                session_file=session_path,
                cascade_detected=False,
                prediction_confidence=0.0,
                predicted_time_minutes=0.0,
                methodology="error",
                processing_time_seconds=time.time() - start_time,
                events_analyzed=0,
                success_rate=0.0,
                raw_result={},
                error_message=str(e)
            )
    
    def _prepare_oracle_input(self, level1_data: Dict) -> Dict:
        """Prepare Oracle-compatible input (same as baseline capture)"""
        
        price_movements = level1_data.get('price_movements', [])
        cascade_events = []
        
        for i, movement in enumerate(price_movements):
            cascade_event = {
                'timestamp': movement['timestamp'],
                'price_level': movement['price_level'],
                'event_type': 'price_movement',
                'movement_type': movement.get('movement_type', 'unknown'),
                'event_id': i
            }
            cascade_events.append(cascade_event)
        
        oracle_input = level1_data.copy()
        oracle_input['micro_timing_analysis'] = {
            'cascade_events': cascade_events,
            'total_events': len(cascade_events),
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        return oracle_input
    
    def _convert_to_nasdaq_format(self, level1_data: Dict) -> Dict:
        """Convert Level-1 data to NASDAQ format for cascade-unit system"""
        
        return {
            'volume': len(level1_data.get('price_movements', [])) * 1000000,
            'price_change': 50.0,
            'momentum': 0.6,
            'liquidity_vacuum': 0.3,
            'stop_run_count': 2,
            'fpfvg_redeliveries': len(level1_data.get('session_fpfvg', {}).get('fpfvg_formation', {}).get('interactions', [])),
            'session_boundary': True,
            'htf_intensity': 0.5,
            'cascade_events': 1,
            'fisher_information': 0.314,
            'deterministic_regime': True,
            'immediate_execution': 0.8,
            'hawkes_intensity': 0.7
        }
    
    def _display_comparative_analysis(self, results: Dict[str, CaseStudyResult]):
        """Display comparative analysis of all approaches"""
        
        print(f"{'Approach':<25} {'Cascade':<8} {'Confidence':<12} {'Time(min)':<10} {'Proc(s)':<8}")
        print("-" * 65)
        
        for approach_name, result in results.items():
            cascade_icon = "✅" if result.cascade_detected else "❌"
            print(f"{result.approach_name:<25} {cascade_icon:<8} {result.prediction_confidence:<12.3f} "
                  f"{result.predicted_time_minutes:<10.2f} {result.processing_time_seconds:<8.3f}")
        
        print()
        
        # Performance ranking
        successful_results = [r for r in results.values() if not r.error_message]
        if successful_results:
            # Rank by confidence
            confidence_ranked = sorted(successful_results, 
                                     key=lambda x: x.prediction_confidence, reverse=True)
            
            print("🏆 CONFIDENCE RANKING:")
            for i, result in enumerate(confidence_ranked, 1):
                print(f"   {i}. {result.approach_name}: {result.prediction_confidence:.3f}")
            
            # Rank by processing speed  
            speed_ranked = sorted(successful_results,
                                key=lambda x: x.processing_time_seconds)
            
            print(f"\n⚡ PROCESSING SPEED RANKING:")
            for i, result in enumerate(speed_ranked, 1):
                print(f"   {i}. {result.approach_name}: {result.processing_time_seconds:.3f}s")
    
    def _save_case_study_results(self, results: Dict[str, CaseStudyResult], session_path: str):
        """Save case study results to file"""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_path = f"nasdaq_case_study_results_{timestamp}.json"
        
        # Convert to JSON-serializable format
        serializable_results = {}
        for approach_name, result in results.items():
            serializable_results[approach_name] = {
                "approach_name": result.approach_name,
                "session_file": result.session_file,
                "cascade_detected": result.cascade_detected,
                "prediction_confidence": result.prediction_confidence,
                "predicted_time_minutes": result.predicted_time_minutes,
                "methodology": result.methodology,
                "processing_time_seconds": result.processing_time_seconds,
                "events_analyzed": result.events_analyzed,
                "success_rate": result.success_rate,
                "error_message": result.error_message,
                "raw_result": result.raw_result
            }
        
        case_study_data = {
            "case_study_metadata": {
                "timestamp": datetime.now().isoformat(),
                "session_file": session_path,
                "approaches_tested": len(results),
                "framework_version": "1.0"
            },
            "results": serializable_results,
            "summary": {
                "best_confidence": max([r.prediction_confidence for r in results.values()]),
                "fastest_processing": min([r.processing_time_seconds for r in results.values()]),
                "approaches_with_cascade_detection": sum([1 for r in results.values() if r.cascade_detected])
            }
        }
        
        with open(output_path, 'w') as f:
            json.dump(case_study_data, f, indent=2, default=str)
        
        print(f"\n💾 Case study results saved: {output_path}")

def run_nasdaq_case_study(session_path: str = None):
    """Main function to run NASDAQ case study"""
    
    if session_path is None:
        session_path = "../data/sessions/level_1/NYAM_Lvl-1_2025_08_05_COMPLETE.json"
    
    framework = NASDAQCaseStudyFramework()
    results = framework.run_complete_case_study(session_path)
    
    return framework, results

if __name__ == "__main__":
    framework, results = run_nasdaq_case_study()
    
    print(f"\n🎯 NASDAQ CASE STUDY COMPLETE")
    print(f"   Approaches Tested: {len(results)}")
    print(f"   Ready for production decision")
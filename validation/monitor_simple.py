#!/usr/bin/env python3
"""
Simple Architectural Monitor - Immediate Production Control
==========================================================

Minimal viable monitor for preventing architectural drift.
Focuses on critical functions only.
"""

from invariants import guard

class ArchitecturalMonitor:
    """Minimal architectural monitoring for immediate deployment"""
    
    def __init__(self):
        self.guard = guard
        
    def check_health(self, verbose=True):
        """Check critical system health - blocks commits if drift detected"""
        checkpoint = self.guard.checkpoint()
        
        # Critical threshold: 85% coherence minimum
        health_good = checkpoint['coherence'] >= 0.85
        
        if verbose:
            status = "✅ HEALTHY" if health_good else "🚨 DRIFT DETECTED"
            print(f"{status}")
            print(f"   Coherence: {checkpoint['coherence']:.1%}")
            print(f"   Functions: {checkpoint['total_functions']}")
            print(f"   Drift events: {checkpoint['drift_events']}")
            
            if checkpoint.get('high_drift_functions'):
                print(f"   High drift: {checkpoint['high_drift_functions']}")
        
        if not health_good:
            print("\n🔧 Quick fixes:")
            print("   1. Restore function purposes to original intent")
            print("   2. Check recent code changes")
            print("   3. Review drift log in invariants system")
        
        return health_good
    
    def establish_baseline(self):
        """Quick baseline establishment"""
        checkpoint = self.guard.checkpoint()
        print(f"📊 Baseline: {checkpoint['total_functions']} functions, {checkpoint['coherence']:.1%} coherence")
        return True

# Global monitor instance
monitor = ArchitecturalMonitor()

if __name__ == "__main__":
    # CLI interface
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--baseline":
        monitor.establish_baseline()
    else:
        health = monitor.check_health()
        sys.exit(0 if health else 1)
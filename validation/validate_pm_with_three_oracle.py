"""
Validate Three-Oracle Architecture against August 5 PM Session
Test if the system correctly handles the 2:36 PM prediction with metacognition protection
"""

import json
import sys
import os
sys.path.append(os.path.dirname(__file__))
from three_oracle_architecture import create_three_oracle_system
from datetime import datetime, <PERSON><PERSON><PERSON>

def validate_pm_session_with_three_oracle():
    """Test Three-Oracle system against actual PM session data"""
    
    print("🏛️ VALIDATING THREE-ORACLE SYSTEM WITH PM DATA")
    print("=" * 70)
    
    # Load actual PM session data
    try:
        with open('/Users/<USER>/grok-claude-automation/data/sessions/level_1/NYPM_Lvl-1_2025_08_05_PARTIAL.json', 'r') as f:
            pm_data = json.load(f)
    except FileNotFoundError:
        print("❌ PM session file not found - using synthetic data")
        pm_data = create_synthetic_pm_data()
    
    # Create complete PM prediction input
    pm_prediction_input = {
        'session_metadata': {
            'session_type': 'NY_PM',
            'date': '2025-08-05',
            'duration_minutes': 240,
            'context': 'post_extreme_am_contamination_with_lunch_normalization'
        },
        'micro_timing_analysis': {
            'cascade_events': [
                # MORNING SESSION CONTEXT (Virgin Oracle will use these)
                {'timestamp': '19:06', 'price_level': 23330.0, 'event_type': 'asia_fpfvg_formation'},
                {'timestamp': '02:08', 'price_level': 23364.0, 'event_type': 'london_fpfvg_formation'},
                {'timestamp': '07:02', 'price_level': 23383.0, 'event_type': 'premarket_fpfvg_formation'},
                
                # AM SESSION EVENTS (Contaminated Oracle will use these too)
                {'timestamp': '09:37', 'price_level': 23341.75, 'event_type': 'am_native_fpfvg_formation'},
                {'timestamp': '10:18', 'price_level': 23292.5, 'event_type': 'three_day_london_fpfvg_redelivery'},
                {'timestamp': '11:41', 'price_level': 23113.5, 'event_type': 'am_session_low_extreme'},
                
                # LUNCH SESSION EVENTS
                {'timestamp': '12:04', 'price_level': 23174.25, 'event_type': 'lunch_native_fpfvg_formation'},
                {'timestamp': '12:30', 'price_level': 23225.25, 'event_type': 'lunch_session_high'},
                {'timestamp': '12:59', 'price_level': 23180.5, 'event_type': 'lunch_close_pm_setup'},
                
                # PM SESSION EVENTS (from partial data)
                {'timestamp': '13:31', 'price_level': 23208.75, 'event_type': 'pm_native_fpfvg_tiny_gap'},
                {'timestamp': '13:46', 'price_level': 23225.25, 'event_type': 'lunch_high_takeout'},
                {'timestamp': '13:58', 'price_level': 23252.0, 'event_type': 'pm_session_high'},
                {'timestamp': '14:08', 'price_level': 23232.0, 'event_type': 'current_consolidation'}
            ]
        },
        'price_data': {
            'daily_high': 23404.25,        # From premarket
            'daily_low': 23113.5,          # From AM session
            'lunch_close': 23180.5,        # PM starting reference
            'pm_high_so_far': 23252.0,     # Highest PM price
            'current_price': 23232.0,      # As of 2:08 PM
            'session_character': 'high_energy_continuation_not_normalization'
        },
        'energy_context': {
            'contamination_progression': [0.25, 0.85, 0.95, 0.99, 0.35, 0.45],  # Morning→AM→Lunch→PM
            'pm_energy_density': 0.68,     # HIGH (opposite of prediction)
            'pm_contamination': 0.45,      # Increased from lunch
            'native_fpfvg_gap': 0.75,      # Extremely tight PM FPFVG
            'cross_session_active': True   # PM immediately interacted with lunch
        }
    }
    
    print("📊 PM Session Context:")
    print(f"   PM Start: 1:30 PM (not standard 1:00 PM)")
    print(f"   Data Through: 2:08 PM (28 minutes before original 2:36 PM prediction)")
    print(f"   Energy Density: 0.68 (HIGH - opposite of expected normalization)")
    print(f"   PM High So Far: 23252.0 (at 1:58 PM)")
    print(f"   Contamination: 45% (increased from lunch 35%)")
    
    # Create Three-Oracle system
    print(f"\n🏛️ Initializing Three-Oracle System...")
    three_oracle = create_three_oracle_system({
        'log_level': 'WARNING',  # Quiet mode for cleaner output
        'enable_enhancement': True,
        'enable_vqe_optimization': True
    })
    
    # Generate prediction
    print(f"\n🎯 GENERATING THREE-ORACLE PM PREDICTION...")
    decision = three_oracle.predict_cascade_timing(pm_prediction_input, optimize_parameters=True)
    
    # Convert prediction to actual time
    pm_start = datetime.strptime('13:00:00', '%H:%M:%S')  # 1:00 PM standard
    predicted_time = pm_start + timedelta(minutes=decision.final_prediction)
    
    print(f"\n🎯 THREE-ORACLE PM RESULTS:")
    print("=" * 50)
    print(f"🕐 Final Prediction: {predicted_time.strftime('%H:%M:%S')} PM")
    print(f"📊 Minutes from 1:00 PM: {decision.final_prediction:.1f}")
    print(f"📈 Confidence: {decision.prediction_confidence:.1%}")
    print(f"⚖️ Chosen Oracle: {decision.chosen_oracle.upper()}")
    print(f"🔍 Echo Strength: {decision.echo_strength:.1f} minutes")
    print(f"🧠 Reasoning: {decision.arbiter_reasoning}")
    print(f"🚨 Metacognition: {'DETECTED' if decision.metacognition_detected else 'Normal'}")
    print(f"🏥 System Health: {decision.system_health['status']}")
    
    # Compare with original prediction
    original_prediction = datetime.strptime('14:36:00', '%H:%M:%S')
    three_oracle_time = predicted_time
    time_difference = abs((three_oracle_time - original_prediction).total_seconds() / 60)
    
    print(f"\n📊 COMPARISON WITH ORIGINAL PREDICTION:")
    print(f"   Original (Single Oracle): 2:36 PM")
    print(f"   Three-Oracle System: {predicted_time.strftime('%H:%M:%S')} PM")
    print(f"   Time Difference: {time_difference:.0f} minutes")
    
    # Detailed Oracle breakdown
    print(f"\n🔍 DETAILED ORACLE BREAKDOWN:")
    print(f"   🧊 Virgin Prediction: {decision.virgin_prediction:.1f} minutes")
    virgin_time = pm_start + timedelta(minutes=decision.virgin_prediction)
    print(f"      Virgin Time: {virgin_time.strftime('%H:%M:%S')} PM")
    
    print(f"   🧬 Contaminated Prediction: {decision.contaminated_prediction:.1f} minutes") 
    contaminated_time = pm_start + timedelta(minutes=decision.contaminated_prediction)
    print(f"      Contaminated Time: {contaminated_time.strftime('%H:%M:%S')} PM")
    
    # System recommendations
    print(f"\n💡 SYSTEM RECOMMENDATIONS:")
    for rec in decision.system_health['recommendations']:
        print(f"   • {rec}")
    
    # Analysis of results
    print(f"\n🧠 ANALYSIS:")
    if decision.metacognition_detected:
        print(f"   🚨 Metacognitive interference detected in PM prediction")
        print(f"   🛡️ Three-Oracle system activated protection protocols") 
        print(f"   ✅ Using {decision.chosen_oracle} Oracle to avoid contamination")
    else:
        print(f"   ✅ No metacognitive interference detected")
        print(f"   📊 Both Oracles in agreement - system operating normally")
        print(f"   🎯 High confidence in {decision.chosen_oracle} prediction")
    
    # Validation against actual events (if available)
    data_end_time = datetime.strptime('14:08:00', '%H:%M:%S')
    
    if predicted_time <= data_end_time:
        print(f"\n⚠️ PREDICTION TIME ALREADY PASSED:")
        print(f"   Predicted: {predicted_time.strftime('%H:%M:%S')} PM")
        print(f"   Data ends: {data_end_time.strftime('%H:%M:%S')} PM")
        print(f"   Analysis: No major cascade observed by prediction time")
        print(f"   Conclusion: System may need recalibration")
    else:
        remaining_time = (predicted_time - data_end_time).total_seconds() / 60
        print(f"\n⏳ PREDICTION PENDING VALIDATION:")
        print(f"   Time remaining: {remaining_time:.0f} minutes from data end")
        print(f"   Watch for: Major cascade event at {predicted_time.strftime('%H:%M:%S')} PM")
    
    return {
        'three_oracle_prediction': predicted_time.strftime('%H:%M:%S'),
        'chosen_oracle': decision.chosen_oracle,
        'echo_strength': decision.echo_strength,
        'metacognition_detected': decision.metacognition_detected,
        'system_health': decision.system_health['status'],
        'confidence': decision.prediction_confidence,
        'time_difference_from_original': time_difference
    }

def create_synthetic_pm_data():
    """Create synthetic PM data if real data not available"""
    return {
        'level1_json': {
            'session_metadata': {
                'session_type': 'ny_pm',
                'session_date': '2025-08-05'
            }
        }
    }

if __name__ == "__main__":
    results = validate_pm_session_with_three_oracle()
    
    print("\n" + "=" * 70)
    print("🎯 THREE-ORACLE PM VALIDATION SUMMARY:")
    print(f"   🏛️ System Prediction: {results['three_oracle_prediction']} PM")
    print(f"   ⚖️ Oracle Used: {results['chosen_oracle'].upper()}")
    print(f"   🔍 Echo Strength: {results['echo_strength']:.1f} minutes")
    print(f"   🚨 Metacognition: {'DETECTED' if results['metacognition_detected'] else 'Normal'}")
    print(f"   🏥 Health: {results['system_health']}")
    print(f"   📈 Confidence: {results['confidence']:.1%}")
    print(f"\n🛡️ Three-Oracle system provides metacognitive protection for PM predictions!")
#!/usr/bin/env python3
"""
Complete Oracle Performance Validation Suite
============================================

Master script that runs all performance validation tests for the Oracle
mathematical architecture and generates comprehensive stakeholder reports.

AUGMENT AI TACTICAL EXECUTION INSTRUCTIONS:

This script executes the complete performance validation pipeline:

1. FFT CORRELATION OPTIMIZATION VALIDATION:
   - Validates O(n²) → O(n log n) complexity reduction
   - Statistical significance testing with 95% confidence intervals  
   - Data sizes: [100, 500, 1000, 2000, 5000] realistic Oracle sessions
   - Expected: >75% improvement for large datasets

2. HAWKES VECTORIZATION OPTIMIZATION VALIDATION:
   - Validates vectorized vs iterative Hawkes implementations
   - Memory profiling and numerical accuracy validation
   - Oracle HTF parameters: μ=0.02, α=35.51, β=0.00442
   - Expected: >40% speedup with maintained accuracy

3. MATHEMATICAL OPTIMIZATION SLI COMPLIANCE:
   - Validates 200ms P99 latency requirement
   - Realistic Oracle prediction workload simulation
   - Sequential and concurrent load testing
   - Expected: <150ms average, <200ms P99

4. COMPREHENSIVE STAKEHOLDER REPORTING:
   - Performance improvement percentages with error bounds
   - Statistical significance validation (p<0.01)
   - Memory efficiency quantification
   - Production readiness assessment

EXECUTION:
    python3 run_complete_oracle_performance_validation.py

SUCCESS CRITERIA:
- FFT correlation: >50% average improvement with statistical significance
- Hawkes vectorization: >1.4x speedup with numerical accuracy
- SLI compliance: <200ms P99 latency with >95% success rate
- Overall: All tests pass with production readiness confirmation

DELIVERABLES:
- Individual test reports (JSON format with timestamps)
- Master performance validation report
- Executive summary with tactical recommendations
- Performance metrics dashboard data
"""

import sys
import os
import json
import subprocess
import time
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import logging

import numpy as np

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class ValidationTestResult:
    """Individual validation test result"""
    test_name: str
    success: bool
    execution_time_seconds: float
    key_metrics: Dict[str, Any]
    report_path: Optional[Path]
    error_message: Optional[str] = None

@dataclass
class MasterValidationReport:
    """Master validation report combining all test results"""
    validation_suite: str
    execution_timestamp: datetime
    total_execution_time_seconds: float
    tests_executed: int
    tests_successful: int
    tests_failed: int
    overall_success: bool
    production_readiness: bool
    individual_results: List[ValidationTestResult]
    performance_summary: Dict[str, Any]
    stakeholder_recommendations: List[str]
    tactical_assessment: str

class OraclePerformanceValidationSuite:
    """Master validation suite for Oracle mathematical architecture performance"""
    
    def __init__(self, output_directory: Optional[Path] = None):
        self.output_directory = output_directory or Path("./performance_validation_results")
        self.output_directory.mkdir(exist_ok=True)
        
        # Test scripts to execute
        self.test_scripts = [
            {
                "name": "FFT_Correlation_Optimization",
                "script": "oracle_fft_correlation_benchmark.py",
                "description": "FFT vs time-domain correlation O(n²) → O(n log n) validation",
                "success_criteria": {
                    "min_avg_improvement": 50.0,  # 50% minimum improvement
                    "statistical_significance": True
                },
                "weight": 0.35  # 35% of overall score
            },
            {
                "name": "Hawkes_Vectorization_Optimization", 
                "script": "oracle_hawkes_vectorization_benchmark.py",
                "description": "Vectorized vs iterative Hawkes process optimization validation",
                "success_criteria": {
                    "min_speedup_factor": 1.4,  # 1.4x minimum speedup
                    "numerical_accuracy": True
                },
                "weight": 0.35  # 35% of overall score
            },
            {
                "name": "Mathematical_Optimization_SLI_Compliance",
                "script": "oracle_sli_compliance_test.py", 
                "description": "200ms SLI compliance validation under realistic workloads",
                "success_criteria": {
                    "max_p99_latency": 200.0,  # 200ms P99 maximum
                    "max_avg_latency": 150.0,  # 150ms average maximum
                    "min_success_rate": 95.0   # 95% minimum success rate
                },
                "weight": 0.30  # 30% of overall score
            }
        ]
        
        logger.info(f"Oracle performance validation suite initialized")
        logger.info(f"Output directory: {self.output_directory}")
        logger.info(f"Test scripts: {len(self.test_scripts)}")
    
    def execute_test_script(self, test_config: Dict[str, Any]) -> ValidationTestResult:
        """Execute individual test script and capture results"""
        
        test_name = test_config["name"]
        script_name = test_config["script"]
        
        logger.info(f"🚀 Executing {test_name}")
        logger.info(f"   Script: {script_name}")
        logger.info(f"   Description: {test_config['description']}")
        
        start_time = time.time()
        
        try:
            # Execute the test script
            result = subprocess.run(
                [sys.executable, script_name],
                capture_output=True,
                text=True,
                timeout=1800  # 30-minute timeout per test
            )
            
            execution_time = time.time() - start_time
            
            # Check if script executed successfully
            if result.returncode == 0:
                logger.info(f"✅ {test_name} completed successfully in {execution_time:.1f}s")
                
                # Look for the generated report file
                report_pattern = test_name.lower().replace("_", "_")
                report_files = list(self.output_directory.parent.glob(f"*{report_pattern}*.json"))
                
                if not report_files:
                    # Look in current directory
                    report_files = list(Path(".").glob(f"*{report_pattern}*.json"))
                
                report_path = report_files[0] if report_files else None
                
                # Extract key metrics from the report if available
                key_metrics = {}
                if report_path and report_path.exists():
                    try:
                        with open(report_path, 'r') as f:
                            report_data = json.load(f)
                            key_metrics = self._extract_key_metrics(test_name, report_data)
                    except Exception as e:
                        logger.warning(f"Failed to parse report for {test_name}: {e}")
                
                # Evaluate success based on criteria
                success = self._evaluate_test_success(test_config, key_metrics)
                
                return ValidationTestResult(
                    test_name=test_name,
                    success=success,
                    execution_time_seconds=execution_time,
                    key_metrics=key_metrics,
                    report_path=report_path,
                    error_message=None
                )
                
            else:
                logger.error(f"❌ {test_name} failed with exit code {result.returncode}")
                logger.error(f"   STDOUT: {result.stdout}")
                logger.error(f"   STDERR: {result.stderr}")
                
                return ValidationTestResult(
                    test_name=test_name,
                    success=False,
                    execution_time_seconds=time.time() - start_time,
                    key_metrics={},
                    report_path=None,
                    error_message=f"Exit code {result.returncode}: {result.stderr}"
                )
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ {test_name} timed out after 30 minutes")
            return ValidationTestResult(
                test_name=test_name,
                success=False,
                execution_time_seconds=1800,
                key_metrics={},
                report_path=None,
                error_message="Test execution timed out"
            )
            
        except Exception as e:
            logger.error(f"💥 {test_name} failed with exception: {e}")
            return ValidationTestResult(
                test_name=test_name,
                success=False,
                execution_time_seconds=time.time() - start_time,
                key_metrics={},
                report_path=None,
                error_message=str(e)
            )
    
    def _extract_key_metrics(self, test_name: str, report_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract key metrics from test report data"""
        
        metrics = {}
        
        try:
            if "FFT_Correlation" in test_name:
                # FFT correlation metrics
                exec_summary = report_data.get("executive_summary", {})
                metrics = {
                    "average_improvement_percent": exec_summary.get("average_improvement_percent", 0),
                    "maximum_improvement_percent": exec_summary.get("maximum_improvement_percent", 0),
                    "statistical_significance": exec_summary.get("statistical_significance", False),
                    "success_rate_percent": exec_summary.get("success_rate_percent", 0)
                }
                
            elif "Hawkes_Vectorization" in test_name:
                # Hawkes vectorization metrics
                exec_summary = report_data.get("executive_summary", {})
                metrics = {
                    "average_speedup_factor": exec_summary.get("average_speedup_factor", 1.0),
                    "maximum_speedup_factor": exec_summary.get("maximum_speedup_factor", 1.0),
                    "average_time_improvement_percent": exec_summary.get("average_time_improvement_percent", 0),
                    "numerical_accuracy_maintained": exec_summary.get("numerical_accuracy_maintained", False),
                    "production_ready": exec_summary.get("production_ready", False)
                }
                
            elif "SLI_Compliance" in test_name:
                # SLI compliance metrics
                exec_summary = report_data.get("executive_summary", {})
                seq_results = report_data.get("sequential_test_results", {})
                metrics = {
                    "overall_compliance": exec_summary.get("overall_compliance", False),
                    "performance_grade": exec_summary.get("performance_grade", "F"),
                    "production_ready": exec_summary.get("production_ready", False),
                    "average_latency_ms": seq_results.get("average_latency_ms", float('inf')),
                    "p99_latency_ms": seq_results.get("p99_latency_ms", float('inf')),
                    "success_rate_percent": seq_results.get("success_rate_percent", 0)
                }
                
        except Exception as e:
            logger.warning(f"Failed to extract metrics from {test_name}: {e}")
        
        return metrics
    
    def _evaluate_test_success(self, test_config: Dict[str, Any], 
                              key_metrics: Dict[str, Any]) -> bool:
        """Evaluate if test meets success criteria"""
        
        criteria = test_config.get("success_criteria", {})
        test_name = test_config["name"]
        
        try:
            if "FFT_Correlation" in test_name:
                avg_improvement = key_metrics.get("average_improvement_percent", 0)
                statistical_sig = key_metrics.get("statistical_significance", False)
                
                return (avg_improvement >= criteria["min_avg_improvement"] and
                       statistical_sig == criteria["statistical_significance"])
                       
            elif "Hawkes_Vectorization" in test_name:
                speedup = key_metrics.get("average_speedup_factor", 1.0)
                accuracy = key_metrics.get("numerical_accuracy_maintained", False)
                
                return (speedup >= criteria["min_speedup_factor"] and
                       accuracy == criteria["numerical_accuracy"])
                       
            elif "SLI_Compliance" in test_name:
                p99_latency = key_metrics.get("p99_latency_ms", float('inf'))
                avg_latency = key_metrics.get("average_latency_ms", float('inf'))
                success_rate = key_metrics.get("success_rate_percent", 0)
                
                return (p99_latency <= criteria["max_p99_latency"] and
                       avg_latency <= criteria["max_avg_latency"] and
                       success_rate >= criteria["min_success_rate"])
                       
        except Exception as e:
            logger.error(f"Failed to evaluate success criteria for {test_name}: {e}")
            
        return False
    
    def calculate_overall_performance_score(self, results: List[ValidationTestResult]) -> float:
        """Calculate weighted overall performance score"""
        
        total_score = 0.0
        total_weight = 0.0
        
        for result in results:
            # Find test configuration
            test_config = next((t for t in self.test_scripts if t["name"] == result.test_name), None)
            if not test_config:
                continue
                
            weight = test_config["weight"]
            total_weight += weight
            
            if result.success:
                # Full points for successful test
                total_score += weight * 100.0
            else:
                # Partial points based on key metrics
                partial_score = self._calculate_partial_score(result)
                total_score += weight * partial_score
        
        return total_score / total_weight if total_weight > 0 else 0.0
    
    def _calculate_partial_score(self, result: ValidationTestResult) -> float:
        """Calculate partial score for partially successful tests"""
        
        metrics = result.key_metrics
        test_name = result.test_name
        
        try:
            if "FFT_Correlation" in test_name:
                improvement = metrics.get("average_improvement_percent", 0)
                return min(100.0, improvement * 2)  # 50% improvement = 100 points
                
            elif "Hawkes_Vectorization" in test_name:
                speedup = metrics.get("average_speedup_factor", 1.0)
                return min(100.0, (speedup - 1.0) * 100)  # 1.5x speedup = 50 points
                
            elif "SLI_Compliance" in test_name:
                success_rate = metrics.get("success_rate_percent", 0)
                return success_rate  # Direct mapping
                
        except Exception:
            pass
        
        return 0.0
    
    def generate_stakeholder_recommendations(self, results: List[ValidationTestResult], 
                                           overall_score: float) -> List[str]:
        """Generate tactical recommendations for stakeholders"""
        
        recommendations = []
        
        # Overall performance assessment
        if overall_score >= 85.0:
            recommendations.append("✅ EXCELLENT: All performance objectives exceeded - ready for production deployment")
        elif overall_score >= 70.0:
            recommendations.append("🔶 GOOD: Most objectives met with minor optimizations needed")
        elif overall_score >= 50.0:
            recommendations.append("⚠️ PARTIAL: Significant optimizations required before production")
        else:
            recommendations.append("🚨 CRITICAL: Major performance issues - architecture redesign needed")
        
        # Individual test recommendations
        for result in results:
            if result.success:
                recommendations.append(f"✅ {result.test_name}: Objectives achieved")
            else:
                recommendations.append(f"❌ {result.test_name}: Optimization required - {result.error_message or 'See detailed report'}")
        
        # Specific tactical guidance
        fft_result = next((r for r in results if "FFT_Correlation" in r.test_name), None)
        if fft_result and not fft_result.success:
            recommendations.append("🔧 FFT Correlation: Consider algorithm tuning or alternative implementations")
        
        hawkes_result = next((r for r in results if "Hawkes_Vectorization" in r.test_name), None)
        if hawkes_result and not hawkes_result.success:
            recommendations.append("🔧 Hawkes Vectorization: Review NumPy optimization patterns and memory usage")
        
        sli_result = next((r for r in results if "SLI_Compliance" in r.test_name), None)
        if sli_result and not sli_result.success:
            recommendations.append("🚨 SLI Compliance: IMMEDIATE ACTION REQUIRED - production deployment blocked")
        
        return recommendations
    
    def run_complete_validation_suite(self) -> MasterValidationReport:
        """Execute complete Oracle performance validation suite"""
        
        logger.info("🚀 ORACLE COMPLETE PERFORMANCE VALIDATION SUITE")
        logger.info("=" * 60)
        logger.info(f"Tests to execute: {len(self.test_scripts)}")
        logger.info(f"Output directory: {self.output_directory}")
        
        suite_start_time = time.time()
        results = []
        
        # Execute each test
        for i, test_config in enumerate(self.test_scripts, 1):
            logger.info(f"\n📊 TEST {i}/{len(self.test_scripts)}: {test_config['name']}")
            logger.info("-" * 50)
            
            result = self.execute_test_script(test_config)
            results.append(result)
            
            # Log immediate result
            if result.success:
                logger.info(f"✅ PASSED: {result.test_name}")
            else:
                logger.error(f"❌ FAILED: {result.test_name} - {result.error_message}")
        
        total_execution_time = time.time() - suite_start_time
        
        # Calculate summary metrics
        tests_successful = sum(1 for r in results if r.success)
        tests_failed = len(results) - tests_successful
        overall_success = tests_successful == len(results)
        
        # Calculate performance score and production readiness
        overall_score = self.calculate_overall_performance_score(results)
        production_readiness = overall_success and overall_score >= 85.0
        
        # Generate performance summary
        performance_summary = {
            "overall_performance_score": overall_score,
            "tests_successful": tests_successful,
            "tests_failed": tests_failed,
            "total_execution_time_minutes": total_execution_time / 60.0,
            "individual_scores": {r.test_name: 100.0 if r.success else self._calculate_partial_score(r) for r in results}
        }
        
        # Generate recommendations
        recommendations = self.generate_stakeholder_recommendations(results, overall_score)
        
        # Tactical assessment
        if production_readiness:
            tactical_assessment = "PRODUCTION READY - All performance objectives achieved with statistical significance"
        elif overall_success:
            tactical_assessment = "CONDITIONAL APPROVAL - All tests passed but performance below optimal"
        elif overall_score >= 70.0:
            tactical_assessment = "OPTIMIZATION REQUIRED - Major issues resolved, minor tuning needed"
        else:
            tactical_assessment = "MAJOR OPTIMIZATION REQUIRED - Significant performance gaps identified"
        
        # Create master report
        master_report = MasterValidationReport(
            validation_suite="Oracle Mathematical Architecture Performance Validation",
            execution_timestamp=datetime.now(),
            total_execution_time_seconds=total_execution_time,
            tests_executed=len(results),
            tests_successful=tests_successful,
            tests_failed=tests_failed,
            overall_success=overall_success,
            production_readiness=production_readiness,
            individual_results=results,
            performance_summary=performance_summary,
            stakeholder_recommendations=recommendations,
            tactical_assessment=tactical_assessment
        )
        
        return master_report
    
    def save_master_report(self, report: MasterValidationReport, 
                          output_path: Optional[Path] = None) -> Path:
        """Save master validation report"""
        
        if output_path is None:
            timestamp = report.execution_timestamp.strftime('%Y%m%d_%H%M%S')
            output_path = self.output_directory / f"oracle_master_validation_report_{timestamp}.json"
        
        # Convert to serializable format
        report_dict = asdict(report)
        report_dict['execution_timestamp'] = report.execution_timestamp.isoformat()
        
        # Handle Path objects
        for result in report_dict['individual_results']:
            if result['report_path']:
                result['report_path'] = str(result['report_path'])
        
        with open(output_path, 'w') as f:
            json.dump(report_dict, f, indent=2)
        
        logger.info(f"Master validation report saved: {output_path}")
        return output_path
    
    def display_executive_summary(self, report: MasterValidationReport) -> None:
        """Display executive summary for immediate stakeholder review"""
        
        print(f"\n🏆 ORACLE PERFORMANCE VALIDATION EXECUTIVE SUMMARY")
        print("=" * 60)
        print(f"Validation Suite: {report.validation_suite}")
        print(f"Execution Time: {report.total_execution_time_seconds / 60:.1f} minutes")
        print(f"Tests Executed: {report.tests_executed}")
        print(f"Tests Successful: {report.tests_successful} ✅")
        print(f"Tests Failed: {report.tests_failed} ❌")
        print(f"Overall Success: {'✅ PASS' if report.overall_success else '❌ FAIL'}")
        print(f"Performance Score: {report.performance_summary['overall_performance_score']:.1f}/100")
        print(f"Production Ready: {'✅ YES' if report.production_readiness else '❌ NO'}")
        
        print(f"\n📊 INDIVIDUAL TEST RESULTS:")
        for result in report.individual_results:
            status = "✅ PASS" if result.success else "❌ FAIL"
            score = 100.0 if result.success else self._calculate_partial_score(result)
            print(f"  {result.test_name}: {status} ({score:.1f}/100)")
        
        print(f"\n🎯 TACTICAL ASSESSMENT:")
        print(f"  {report.tactical_assessment}")
        
        print(f"\n📋 KEY RECOMMENDATIONS:")
        for rec in report.stakeholder_recommendations[:5]:  # Top 5 recommendations
            print(f"  {rec}")
        
        if len(report.stakeholder_recommendations) > 5:
            print(f"  ... and {len(report.stakeholder_recommendations) - 5} additional recommendations")

def main():
    """Execute complete Oracle performance validation suite"""
    
    print("🚀 ORACLE MATHEMATICAL ARCHITECTURE COMPLETE PERFORMANCE VALIDATION")
    print("=" * 80)
    print("Executing comprehensive performance validation suite...")
    print("This includes FFT optimization, Hawkes vectorization, and SLI compliance tests")
    print("=" * 80)
    
    try:
        # Initialize validation suite
        validation_suite = OraclePerformanceValidationSuite()
        
        # Execute complete validation
        master_report = validation_suite.run_complete_validation_suite()
        
        # Save master report
        report_path = validation_suite.save_master_report(master_report)
        
        # Display executive summary
        validation_suite.display_executive_summary(master_report)
        
        print(f"\n📄 Complete validation report: {report_path}")
        
        # Return appropriate exit code
        if master_report.production_readiness:
            print(f"\n🎉 SUCCESS: Oracle mathematical architecture is production ready!")
            return 0
        elif master_report.overall_success:
            print(f"\n🔶 CONDITIONAL: All tests passed but performance optimization recommended")
            return 0
        else:
            print(f"\n⚠️ ISSUES IDENTIFIED: Performance optimization required before deployment")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️ Validation suite interrupted by user")
        return 130
    except Exception as e:
        logger.error(f"Validation suite failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
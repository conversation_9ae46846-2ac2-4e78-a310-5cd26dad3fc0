#!/usr/bin/env python3
"""
Integrated Audit System
=======================

Combines Haiku quick audits with full audit system for optimal performance.
Uses fast Haiku checks to determine if expensive full audit is needed.

Workflow:
1. Run Haiku quick audit (~200ms, $0.0002)
2. If escalation needed, run full audit system
3. Generate comprehensive report
"""

from haiku_audit import HaikuAuditor
import subprocess
import json
from typing import Dict, Any
from pathlib import Path

class IntegratedAudit:
    """Intelligent audit system with Haiku triage"""
    
    def __init__(self):
        self.haiku = HaikuAuditor()
        print("🤝 Integrated Audit System initialized")
        print("   Fast triage: Haiku (~200ms)")
        print("   Deep analysis: Full audit system when needed")
    
    def smart_audit(self) -> Dict[str, Any]:
        """Run smart audit with automatic escalation"""
        
        print("\n🎯 SMART AUDIT WORKFLOW")
        print("=" * 30)
        
        # Phase 1: Haiku quick triage
        print("📍 Phase 1: Haiku Quick Triage")
        haiku_results = self.haiku.quick_audit()
        
        should_escalate = haiku_results['escalate_to_opus']
        
        audit_results = {
            'haiku_triage': haiku_results,
            'full_audit': None,
            'recommendation': 'no_action',
            'cost_estimate': 0.0002,  # Haiku cost
            'time_estimate': '200ms'
        }
        
        # Phase 2: Conditional full audit
        if should_escalate:
            print("\n📍 Phase 2: Full Audit System (Escalated)")
            
            try:
                # Run the full audit_agent.py
                full_audit_result = subprocess.run(
                    ['python3', 'audit_agent.py'],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                if full_audit_result.returncode == 0:
                    audit_results['full_audit'] = {
                        'output': full_audit_result.stdout,
                        'status': 'completed'
                    }
                    audit_results['recommendation'] = 'review_required'
                    audit_results['cost_estimate'] += 0.02  # Full audit cost
                    audit_results['time_estimate'] = '5-10 seconds'
                    
                    print("✅ Full audit completed")
                    
                else:
                    audit_results['full_audit'] = {
                        'error': full_audit_result.stderr,
                        'status': 'failed'
                    }
                    audit_results['recommendation'] = 'manual_review'
                    
                    print("❌ Full audit failed")
                    
            except subprocess.TimeoutExpired:
                audit_results['full_audit'] = {
                    'error': 'Audit timed out after 30 seconds',
                    'status': 'timeout'
                }
                audit_results['recommendation'] = 'manual_review'
                print("⏰ Full audit timed out")
                
            except Exception as e:
                audit_results['full_audit'] = {
                    'error': str(e),
                    'status': 'error'
                }
                audit_results['recommendation'] = 'manual_review'
                print(f"❌ Full audit error: {e}")
        
        else:
            print("\n✅ Phase 2: Skipped (no escalation needed)")
            audit_results['recommendation'] = 'changes_safe'
        
        # Generate final recommendation
        self._print_final_report(audit_results)
        
        return audit_results
    
    def _print_final_report(self, results: Dict[str, Any]):
        """Print final audit report"""
        
        print(f"\n📋 INTEGRATED AUDIT REPORT")
        print("=" * 35)
        
        haiku = results['haiku_triage']
        print(f"🎯 Haiku Triage:")
        print(f"   Status: {haiku['status_summary']}")
        print(f"   Escalation: {'✅ Needed' if haiku['escalate_to_opus'] else '❌ Not needed'}")
        
        if results['full_audit']:
            full = results['full_audit']
            print(f"\n🔍 Full Audit:")
            print(f"   Status: {full['status']}")
            
            if full['status'] == 'completed':
                # Extract key findings from output
                output = full['output']
                if '🎯 SUMMARY:' in output:
                    summary_line = output.split('🎯 SUMMARY:')[1].split('\n')[0].strip()
                    print(f"   Result: {summary_line}")
        
        print(f"\n💡 Recommendation: {results['recommendation'].replace('_', ' ').title()}")
        print(f"💰 Cost: ~${results['cost_estimate']:.4f}")
        print(f"⏱️ Time: {results['time_estimate']}")
        
        # Action items
        print(f"\n📋 Action Items:")
        if results['recommendation'] == 'changes_safe':
            print("   ✅ No action needed - changes are safe")
        elif results['recommendation'] == 'review_required':
            print("   ⚠️ Review recommended - see full audit details")
        elif results['recommendation'] == 'manual_review':
            print("   🔍 Manual review required - audit system issues")
        else:
            print("   🤷 Unknown recommendation - manual assessment needed")

def cli_integration():
    """CLI interface for integrated audit"""
    
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command in ['quick', 'haiku']:
            # Just run Haiku
            auditor = HaikuAuditor()
            results = auditor.quick_audit()
            return results
            
        elif command in ['smart', 'auto', 'integrated']:
            # Run smart audit
            integrated = IntegratedAudit()
            results = integrated.smart_audit()
            return results
            
        elif command in ['help', '-h', '--help']:
            print("🤝 Integrated Audit CLI")
            print("=" * 25)
            print("Commands:")
            print("  quick/haiku    - Fast Haiku triage only")
            print("  smart/auto     - Smart audit with auto-escalation")  
            print("  help           - Show this help")
            return None
            
        else:
            print(f"Unknown command: {command}")
            print("Use 'help' for available commands")
            return None
    else:
        # Default: smart audit
        integrated = IntegratedAudit()
        return integrated.smart_audit()

if __name__ == "__main__":
    cli_integration()
"""
Utility and Support Systems
===========================

This package contains utility modules, monitoring systems, and support tools:

- unified_enhanced_oracle_api: Complete production API system
- monitor: Architectural monitoring and coherence checking
- audit_agent: Security-focused code audit and integrity protection

These modules provide essential utility and monitoring capabilities
for the Project Oracle system.
"""

try:
    from .unified_enhanced_oracle_api import UnifiedEnhancedOracleAPI
    from .monitor import ArchitecturalMonitor
    from .audit_agent import AuditAgent
except ImportError:
    pass  # Graceful degradation

__all__ = [
    'UnifiedEnhancedOracleAPI',
    'ArchitecturalMonitor',
    'AuditAgent',
]

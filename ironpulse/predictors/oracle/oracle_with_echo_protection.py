"""
Oracle with Echo Protection - Production Ready
Simple integration that detects metacognition and switches to virgin predictions

USAGE: Drop-in replacement for regular Oracle with automatic echo detection
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))
from oracle import create_project_oracle
from echo_detector import EchoDetector
import logging

class ProtectedOracle:
    """Oracle with built-in metacognition protection"""
    
    def __init__(self, config=None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Create both Oracle instances
        self.contaminated_oracle = create_project_oracle(self.config)
        self.virgin_oracle = self._create_virgin_oracle()
        
        # Echo detection
        self.echo_detector = EchoDetector(echo_threshold=20)
        
        self.logger.info("🛡️ PROTECTED ORACLE: Initialized with echo detection")
    
    def _create_virgin_oracle(self):
        """Create virgin Oracle trained only on morning sessions"""
        
        virgin_config = self.config.copy()
        virgin_config['virgin_mode'] = True  # Flag for virgin mode
        
        return create_project_oracle(virgin_config)
    
    def predict_cascade_timing(self, session_data, optimize_parameters=None):
        """
        Protected prediction with automatic echo detection
        
        Returns prediction from virgin Oracle if metacognition detected
        """
        
        self.logger.info("🔮 PROTECTED PREDICTION: Starting dual prediction")
        
        # Get predictions from both Oracles
        contaminated_prediction = self.contaminated_oracle.predict_cascade_timing(
            session_data, optimize_parameters
        )
        
        # Virgin Oracle uses only pre-session data
        virgin_session_data = self._extract_virgin_data(session_data)
        virgin_prediction = self.virgin_oracle.predict_cascade_timing(
            virgin_session_data, optimize_parameters
        )
        
        # Compare predictions for echo detection
        contaminated_time = self._format_prediction_time(contaminated_prediction.predicted_cascade_time)
        virgin_time = self._format_prediction_time(virgin_prediction.predicted_cascade_time)
        
        echo_result = self.echo_detector.detect_metacognition(
            virgin_time, contaminated_time
        )
        
        # Decision logic
        if echo_result['metacognition_detected']:
            self.logger.warning(f"🚨 METACOGNITION DETECTED: Using virgin prediction")
            self.logger.warning(f"   Echo Strength: {echo_result['echo_strength']:.1f} minutes")
            self.logger.warning(f"   Virgin: {virgin_time}, Contaminated: {contaminated_time}")
            
            # Use virgin prediction but keep contaminated metadata
            final_prediction = virgin_prediction
            final_prediction.methodology = "virgin_oracle_metacognition_protection"
            final_prediction.metacognition_detected = True
            final_prediction.echo_strength = echo_result['echo_strength']
            
        else:
            self.logger.info(f"✅ NO METACOGNITION: Using contaminated prediction")
            final_prediction = contaminated_prediction
            final_prediction.methodology = "contaminated_oracle_normal_operation"
            final_prediction.metacognition_detected = False
            final_prediction.echo_strength = echo_result['echo_strength']
        
        # Log the decision
        self.logger.info(f"🎯 FINAL PREDICTION: {final_prediction.predicted_cascade_time:.1f} minutes")
        self.logger.info(f"   Method: {final_prediction.methodology}")
        self.logger.info(f"   Echo Strength: {final_prediction.echo_strength:.1f} minutes")
        
        return final_prediction
    
    def _extract_virgin_data(self, session_data):
        """Extract only pre-session data for virgin Oracle"""
        
        virgin_data = session_data.copy()
        
        # Keep only morning session events (pre-AM contamination)
        if 'micro_timing_analysis' in virgin_data:
            events = virgin_data['micro_timing_analysis'].get('cascade_events', [])
            
            # Filter to only morning sessions (before 09:30)
            virgin_events = []
            for event in events:
                timestamp = event.get('timestamp', '00:00')
                if timestamp < '09:30':  # Only morning sessions
                    virgin_events.append(event)
            
            virgin_data['micro_timing_analysis']['cascade_events'] = virgin_events
        
        return virgin_data
    
    def _format_prediction_time(self, minutes_from_start):
        """Convert prediction minutes to HH:MM:SS format"""
        
        hours = int(minutes_from_start // 60)
        minutes = int(minutes_from_start % 60)
        
        # Assume start times based on session
        base_hour = 9  # Default to 9:30 AM start
        total_hours = base_hour + hours
        total_minutes = 30 + minutes  # 30 minute offset for 9:30 start
        
        if total_minutes >= 60:
            total_hours += 1
            total_minutes -= 60
        
        return f"{total_hours:02d}:{total_minutes:02d}:00"
    
    def get_system_status(self):
        """Get status of both Oracle systems"""
        
        return {
            'protected_oracle_active': True,
            'contaminated_oracle_status': self.contaminated_oracle.get_system_status(),
            'virgin_oracle_active': True,
            'echo_detector_active': True,
            'echo_threshold': self.echo_detector.echo_threshold,
            'prediction_history_count': len(self.echo_detector.prediction_history)
        }

# Drop-in replacement function
def create_protected_oracle(config=None):
    """Create Oracle with automatic metacognition protection"""
    return ProtectedOracle(config)

if __name__ == "__main__":
    # Test the protected Oracle
    print("🛡️ TESTING PROTECTED ORACLE")
    print("=" * 50)
    
    protected_oracle = create_protected_oracle({
        'log_level': 'INFO',
        'enable_enhancement': True
    })
    
    # Test with sample data
    test_data = {
        'session_metadata': {
            'session_type': 'NY_AM',
            'date': '2025-08-05'
        },
        'micro_timing_analysis': {
            'cascade_events': [
                {'timestamp': '02:08', 'price_level': 23364.0, 'event_type': 'london_fpfvg'},
                {'timestamp': '09:37', 'price_level': 23341.75, 'event_type': 'am_fpfvg'},
                {'timestamp': '10:18', 'price_level': 23292.5, 'event_type': 'major_cascade'}
            ]
        }
    }
    
    prediction = protected_oracle.predict_cascade_timing(test_data)
    
    print(f"\n🎯 PROTECTED ORACLE RESULT:")
    print(f"   Prediction: {prediction.predicted_cascade_time:.1f} minutes")
    print(f"   Method: {prediction.methodology}")
    print(f"   Metacognition: {'🚨 Detected' if prediction.metacognition_detected else '✅ Normal'}")
    print(f"   Echo Strength: {prediction.echo_strength:.1f} minutes")
    
    status = protected_oracle.get_system_status()
    print(f"\n📊 System Status: {status['protected_oracle_active']}")
"""
Oracle Prediction Systems
=========================

This package contains the core Oracle prediction engines:

- oracle: Core Oracle prediction system
- oracle_api: Oracle API interface
- oracle_core: Oracle core functionality
- production_oracle: Production-ready Oracle implementation

These modules provide the foundational Oracle prediction capabilities
for the Project Oracle system.
"""

try:
    from .oracle import Oracle
    from .oracle_api import OracleAPI
    from .oracle_core import OracleCore
    from .production_oracle import ProductionOracle
except ImportError:
    pass  # Graceful degradation

__all__ = [
    'Oracle',
    'OracleAPI',
    'OracleCore',
    'ProductionOracle',
]

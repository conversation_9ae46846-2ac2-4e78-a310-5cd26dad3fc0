# Three-Oracle Architecture for IRONPULSE
# Information Recognition and Oracle Network for Pushdown Understanding of Liquidity and Statistical Events
import sys
import os
from validation.energy_validator import EnergyValidator
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from dataclasses import dataclass

# Use absolute imports for IRONPULSE components
try:
    from ironpulse.features.echo_detector import EchoDetector
except ImportError:
    # Fallback implementation for missing dependencies
    class EchoDetector:
        def __init__(self, threshold=20):
            self.threshold = threshold
        
        def detect_metacognition(self, time1, time2):
            # Simple implementation for testing
            import re
            def time_to_minutes(time_str):
                match = re.match(r'(\d+):(\d+):(\d+)', time_str)
                if match:
                    h, m, s = map(int, match.groups())
                    return h * 60 + m + s / 60
                return 0
            
            t1_min = time_to_minutes(time1)
            t2_min = time_to_minutes(time2)
            echo_strength = abs(t1_min - t2_min)
            
            return {
                'echo_strength': echo_strength,
                'metacognition_detected': echo_strength > self.threshold
            }

try:
    from validation.metacognitive_loop_detector import create_metacognitive_loop_detector
except ImportError:
    # Fallback implementation
    def create_metacognitive_loop_detector(config=None):
        class MockLoopDetector:
            def monitor_prediction(self, decision, metadata):
                class MockResult:
                    loop_detected = False
                    loop_strength = 0.0
                    pattern_type = 'none'
                    recommended_action = 'continue'
                    confidence = 1.0
                    loop_duration_sessions = 0
                return MockResult()
        return MockLoopDetector()

@dataclass
class ThreeOracleDecision:
    """Result from Three-Oracle system"""
    final_prediction: float
    prediction_confidence: float
    chosen_oracle: str  # 'virgin', 'contaminated', or 'weighted'
    echo_strength: float
    virgin_prediction: float
    contaminated_prediction: float
    arbiter_reasoning: str
    metacognition_detected: bool
    system_health: Dict[str, Any]
    virgin_prediction_details: Dict[str, Any] = None  # Include cascade analysis
    contaminated_prediction_details: Dict[str, Any] = None  # Include cascade analysis
    metacognitive_loop_result: Dict[str, Any] = None  # NEW: Loop detection results

class VirginOracle:
    """Frozen Oracle trained only on pre-session data"""
    
    def __init__(self, config=None):
        self.config = config or {}
        self.config['virgin_mode'] = True
        # Lazy import to avoid circular dependency
        from oracle import create_project_oracle
        self.oracle = create_project_oracle(self.config)
        self.frozen = True
        self.logger = logging.getLogger(f"{__name__}.Virgin")
        
        self.logger.info("🧊 VIRGIN ORACLE: Initialized (frozen state)")
    
    def predict_cascade_timing(self, session_data, optimize_parameters=False):
        """Virgin prediction using only morning session data"""
        
        # Extract only pre-session data
        virgin_data = self._purify_session_data(session_data)
        
        # Never use parameter optimization (keep pure)
        prediction = self.oracle.predict_cascade_timing(virgin_data, optimize_parameters=False)
        
        self.logger.debug(f"🧊 Virgin prediction: {prediction.predicted_cascade_time:.1f} min")
        
        # Add cascade analysis if available
        if hasattr(prediction, 'cascade_analysis') and prediction.cascade_analysis:
            self.logger.debug(f"🧊 Virgin cascade analysis: {prediction.cascade_analysis.get('dominant_cascade_type', 'unknown')}")
        
        return prediction
    
    def _purify_session_data(self, session_data):
        """Remove all contaminated data, keep only morning sessions"""
        
        pure_data = {
            'session_metadata': session_data.get('session_metadata', {}),
            'micro_timing_analysis': {'cascade_events': []},
            'price_data': session_data.get('price_data', {})
        }
        
        # Only include morning session events (before 09:30)
        if 'micro_timing_analysis' in session_data:
            events = session_data['micro_timing_analysis'].get('cascade_events', [])
            
            for event in events:
                timestamp = event.get('timestamp', '00:00')
                # Keep only morning sessions: Asia (19:00-23:59), London (02:00-04:59), Premarket (07:00-09:29)
                if (timestamp >= '19:00' or timestamp <= '04:59' or 
                    ('07:00' <= timestamp <= '09:29')):
                    pure_data['micro_timing_analysis']['cascade_events'].append(event)
        
        self.logger.debug(f"🧊 Purified data: {len(pure_data['micro_timing_analysis']['cascade_events'])} events")
        
        return pure_data

class ContaminatedOracle:
    """Adaptive Oracle that learns from all data"""
    
    def __init__(self, config=None):
        self.config = config or {}
        # Lazy import to avoid circular dependency
        from oracle import create_project_oracle
        self.oracle = create_project_oracle(self.config)
        self.logger = logging.getLogger(f"{__name__}.Contaminated")
        
        self.logger.info("🧬 CONTAMINATED ORACLE: Initialized (adaptive state)")
    
    def predict_cascade_timing(self, session_data, optimize_parameters=True):
        """Full prediction using all available data"""
        
        prediction = self.oracle.predict_cascade_timing(session_data, optimize_parameters)
        
        self.logger.debug(f"🧬 Contaminated prediction: {prediction.predicted_cascade_time:.1f} min")
        
        # Add cascade analysis if available
        if hasattr(prediction, 'cascade_analysis') and prediction.cascade_analysis:
            self.logger.debug(f"🧬 Contaminated cascade analysis: {prediction.cascade_analysis.get('dominant_cascade_type', 'unknown')}")
        
        return prediction

class ArbiterOracle:
    """Decision maker that chooses between Virgin and Contaminated predictions"""
    
    def __init__(self, echo_threshold=20, confidence_threshold=0.1):
        self.echo_threshold = echo_threshold
        self.confidence_threshold = confidence_threshold
        self.echo_detector = EchoDetector(echo_threshold)
        self.decision_history = []
        self.logger = logging.getLogger(f"{__name__}.Arbiter")
        
        self.logger.info("⚖️ ARBITER ORACLE: Initialized")
    
    def arbitrate(self, virgin_prediction, contaminated_prediction, context=None):
        """Make final decision between predictions"""
        
        # Calculate echo strength
        virgin_time = self._format_time(virgin_prediction.predicted_cascade_time)
        contaminated_time = self._format_time(contaminated_prediction.predicted_cascade_time)
        
        echo_result = self.echo_detector.detect_metacognition(virgin_time, contaminated_time)
        
        # Decision tree
        decision = self._apply_decision_tree(
            virgin_prediction, contaminated_prediction, echo_result, context
        )
        
        # Store decision
        self.decision_history.append({
            'timestamp': datetime.now().isoformat(),
            'chosen_oracle': decision.chosen_oracle,
            'echo_strength': decision.echo_strength,
            'reasoning': decision.arbiter_reasoning
        })
        
        self.logger.info(f"⚖️ DECISION: {decision.chosen_oracle.upper()} oracle chosen")
        self.logger.info(f"   Echo: {decision.echo_strength:.1f}min, Reason: {decision.arbiter_reasoning}")
        
        return decision
    
    def _apply_decision_tree(self, virgin_pred, contaminated_pred, echo_result, context):
        """Core decision tree logic"""
        
        echo_strength = echo_result['echo_strength']
        metacognition = echo_result['metacognition_detected']
        
        # Decision tree
        if echo_strength > 40:
            # Extreme metacognition - use virgin immediately
            chosen_oracle = 'virgin'
            final_prediction = virgin_pred.predicted_cascade_time
            final_confidence = virgin_pred.prediction_confidence
            reasoning = f"Extreme echo ({echo_strength:.1f}min) - virgin protection activated"
            
        elif echo_strength > 20:
            # Moderate metacognition - use virgin with monitoring
            chosen_oracle = 'virgin'
            final_prediction = virgin_pred.predicted_cascade_time
            final_confidence = virgin_pred.prediction_confidence * 0.9  # Slight confidence reduction
            reasoning = f"Moderate echo ({echo_strength:.1f}min) - virgin with monitoring"
            
        elif abs(echo_strength) < 0.1:
            # Predictions converged - use contaminated (more sophisticated)
            chosen_oracle = 'contaminated'
            final_prediction = contaminated_pred.predicted_cascade_time
            final_confidence = contaminated_pred.prediction_confidence
            reasoning = f"Predictions converged ({echo_strength:.1f}min) - using adaptive"
            
        else:
            # Normal divergence - use weighted average
            chosen_oracle = 'weighted'
            weight_virgin = 0.7 if echo_strength > 10 else 0.3
            weight_contaminated = 1.0 - weight_virgin
            
            final_prediction = (
                weight_virgin * virgin_pred.predicted_cascade_time +
                weight_contaminated * contaminated_pred.predicted_cascade_time
            )
            final_confidence = (
                weight_virgin * virgin_pred.prediction_confidence +
                weight_contaminated * contaminated_pred.prediction_confidence
            )
            reasoning = f"Weighted average (V:{weight_virgin:.1f}, C:{weight_contaminated:.1f})"
        
        return ThreeOracleDecision(
            final_prediction=float(final_prediction),
            prediction_confidence=float(final_confidence),
            chosen_oracle=chosen_oracle,
            echo_strength=float(echo_strength),
            virgin_prediction=float(virgin_pred.predicted_cascade_time),
            contaminated_prediction=float(contaminated_pred.predicted_cascade_time),
            arbiter_reasoning=reasoning,
            metacognition_detected=metacognition,
            system_health=self._assess_system_health(echo_strength),
            virgin_prediction_details=getattr(virgin_pred, '__dict__', {}),  # Include cascade analysis
            contaminated_prediction_details=getattr(contaminated_pred, '__dict__', {})  # Include cascade analysis
        )
    
    def _format_time(self, minutes_from_start):
        """Convert prediction minutes to time format"""
        total_minutes = int(minutes_from_start) + 570  # 9:30 AM = 570 minutes from midnight
        hours = total_minutes // 60
        minutes = total_minutes % 60
        return f"{hours:02d}:{minutes:02d}:00"
    
    def _assess_system_health(self, echo_strength):
        """Assess overall system health"""
        
        if echo_strength > 40:
            health = "CRITICAL_METACOGNITION"
            score = 0.2
        elif echo_strength > 20:
            health = "MODERATE_METACOGNITION"
            score = 0.6
        elif echo_strength > 10:
            health = "MINOR_DIVERGENCE"
            score = 0.8
        else:
            health = "HEALTHY_OPERATION"
            score = 1.0
        
        return {
            'status': health,
            'score': score,
            'echo_strength': echo_strength,
            'recommendations': self._get_health_recommendations(health)
        }
    
    def _get_health_recommendations(self, health_status):
        """Get recommendations based on system health"""
        
        recommendations = {
            'CRITICAL_METACOGNITION': [
                "Use virgin Oracle exclusively",
                "Investigate contamination source",
                "Consider system reset"
            ],
            'MODERATE_METACOGNITION': [
                "Monitor echo strength closely", 
                "Prefer virgin predictions",
                "Validate against actual events"
            ],
            'MINOR_DIVERGENCE': [
                "Normal operation",
                "Continue monitoring",
                "Use weighted decisions"
            ],
            'HEALTHY_OPERATION': [
                "System operating normally",
                "All Oracles in sync",
                "Continue current operation"
            ]
        }
        
        return recommendations.get(health_status, ["Monitor system"])

class ThreeOracleSystem:
    """Complete Three-Oracle Architecture with Energy Validation"""
    
    def __init__(self, config=None):
        self.config = config or {}
        
        # Initialize all three Oracles
        self.virgin = VirginOracle(self.config)
        self.contaminated = ContaminatedOracle(self.config)
        self.arbiter = ArbiterOracle()
        self.energy_validator = EnergyValidator(self.config)
        
        # Initialize Metacognitive Loop Detector
        self.metacognitive_detector = create_metacognitive_loop_detector(self.config.get('loop_detector', {}))
        
        self.prediction_count = 0
        self.energy_prediction_count = 0
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("🏛️ THREE-ORACLE SYSTEM: Fully initialized")
        self.logger.info("   🧊 Virgin Oracle: Ready")
        self.logger.info("   🧬 Contaminated Oracle: Ready") 
        self.logger.info("   ⚖️ Arbiter Oracle: Ready")
        self.logger.info("   🔋 Energy Validator: Ready")
        self.logger.info("   🧠 Metacognitive Loop Detector: Ready")
    
    def predict_cascade_timing(self, session_data, optimize_parameters=None):
        """Main prediction interface for timing and energy validation"""
        
        self.prediction_count += 1
        self.logger.info(f"🏛️ THREE-ORACLE PREDICTION #{self.prediction_count}")
        
        # Get predictions from both Oracles
        self.logger.info("🧊 Getting virgin prediction...")
        virgin_pred = self.virgin.predict_cascade_timing(session_data, False)
        
        self.logger.info("🧬 Getting contaminated prediction...")
        contaminated_pred = self.contaminated.predict_cascade_timing(session_data, optimize_parameters)
        
        # Arbiter makes final decision for timing
        self.logger.info("⚖️ Arbitrating final decision for timing...")
        timing_decision = self.arbiter.arbitrate(virgin_pred, contaminated_pred, session_data)
        
        # CRITICAL: Metacognitive Loop Detection (threshold > 20)
        self.logger.info("🧠 Performing metacognitive loop detection...")
        session_metadata = {
            'session_id': session_data.get('session_metadata', {}).get('session_id', f'session_{self.prediction_count}'),
            'session_type': session_data.get('session_metadata', {}).get('session_type', 'unknown'),
            'prediction_count': self.prediction_count
        }
        
        loop_result = self.metacognitive_detector.monitor_prediction(timing_decision, session_metadata)
        
        # Update timing decision with loop detection results
        timing_decision.metacognitive_loop_result = {
            'loop_detected': loop_result.loop_detected,
            'loop_strength': loop_result.loop_strength,
            'pattern_type': loop_result.pattern_type,
            'recommended_action': loop_result.recommended_action,
            'confidence': loop_result.confidence,
            'loop_duration_sessions': loop_result.loop_duration_sessions
        }
        
        # Apply loop countermeasures if detected
        if loop_result.loop_detected:
            self._apply_loop_countermeasures(loop_result, timing_decision)
        
        # Perform energy validation if energy data is available
        energy_results = None
        if 'energy_data' in session_data:
            self.energy_prediction_count += 1
            self.logger.info(f"🔋 Performing energy validation #{self.energy_prediction_count}")
            energy_data = self._prepare_energy_data(session_data)
            energy_results = self.energy_validator.validate(energy_data)
            self.logger.info(f"🔋 Energy Validation Results: {len(energy_results['predictions'])} predictions")
            if any(energy_results['divergence_detected']):
                self.logger.warning(f"🔋 Energy Divergence Detected in {sum(energy_results['divergence_detected'])} predictions")
            if energy_results['echo_detected']:
                self.logger.warning("🔋 Energy Echo Detected between Oracles!")
        
        # Log final timing result
        self.logger.info(f"🎯 FINAL TIMING DECISION:")
        self.logger.info(f"   Chosen: {timing_decision.chosen_oracle.upper()} Oracle")
        self.logger.info(f"   Prediction: {timing_decision.final_prediction:.1f} minutes")
        self.logger.info(f"   Confidence: {timing_decision.prediction_confidence:.3f}")
        self.logger.info(f"   System Health: {timing_decision.system_health['status']}")
        
        # Return combined results
        return {
            'timing_decision': timing_decision,
            'energy_results': energy_results
        }
    
    def _apply_loop_countermeasures(self, loop_result, timing_decision):
        """Apply countermeasures when metacognitive loop is detected"""
        
        action = loop_result.recommended_action
        
        if action == 'emergency_reset':
            self.logger.critical("🚨🧠 EMERGENCY RESET: Applying critical loop countermeasures")
            # Override with virgin prediction and reduce confidence significantly  
            timing_decision.final_prediction = timing_decision.virgin_prediction
            timing_decision.prediction_confidence = min(timing_decision.prediction_confidence * 0.5, 0.6)
            timing_decision.chosen_oracle = 'virgin'
            timing_decision.arbiter_reasoning = f"Emergency override - {loop_result.pattern_type} loop detected"
            
        elif action == 'virgin_oracle_priority':
            self.logger.warning("⚠️🧠 LOOP COUNTERMEASURES: Prioritizing virgin Oracle")
            # Switch to virgin with moderate confidence reduction
            timing_decision.final_prediction = timing_decision.virgin_prediction
            timing_decision.prediction_confidence = min(timing_decision.prediction_confidence * 0.8, 0.85)
            timing_decision.chosen_oracle = 'virgin'
            timing_decision.arbiter_reasoning = f"Loop countermeasure - {loop_result.pattern_type} pattern"
            
        elif action == 'intervention_required':
            self.logger.warning("🔧🧠 INTERVENTION: Applying stability measures")
            # Apply weighted average with virgin bias
            virgin_weight = 0.8
            contaminated_weight = 0.2
            timing_decision.final_prediction = (
                virgin_weight * timing_decision.virgin_prediction +
                contaminated_weight * timing_decision.contaminated_prediction
            )
            timing_decision.prediction_confidence = min(timing_decision.prediction_confidence * 0.9, 0.9)
            timing_decision.chosen_oracle = 'weighted'
            timing_decision.arbiter_reasoning = f"Intervention applied - escalating {loop_result.pattern_type}"
            
        elif action == 'stabilization_required':
            self.logger.info("🔄🧠 STABILIZATION: Applying oscillation dampening")
            # Dampen oscillations with conservative weighting
            timing_decision.prediction_confidence = min(timing_decision.prediction_confidence * 0.95, 0.95)
            timing_decision.arbiter_reasoning = f"Stabilization applied - {loop_result.pattern_type} dampened"
        
        # Log applied countermeasures
        self.logger.info(f"🧠 Loop countermeasures applied: {action}")
        self.logger.info(f"   Updated prediction: {timing_decision.final_prediction:.1f} minutes")
        self.logger.info(f"   Updated confidence: {timing_decision.prediction_confidence:.3f}")
        self.logger.info(f"   Oracle selection: {timing_decision.chosen_oracle}")
    
    def _prepare_energy_data(self, session_data):
        """Prepare data for energy validation from session data.

        Args:
            session_data (dict): Session data containing energy metrics if available.

        Returns:
            list: List of dictionaries formatted for energy validation.
        """
        energy_data = session_data.get('energy_data', [])
        if not energy_data:
            self.logger.warning("No energy data available for validation")
            return []
        
        formatted_data = []
        for entry in energy_data:
            formatted_entry = {
                'weights': {
                    'energy': entry.get('energy_weight', 0.0),
                    'contamination': entry.get('contamination_weight', 0.0)
                },
                'density': entry.get('energy_density', 0.0),
                'target_contamination': entry.get('actual_contamination', 0.0)
            }
            # Add Oracle-specific predictions if available
            if 'virgin_energy' in entry:
                formatted_entry['virgin'] = {'energy': entry.get('virgin_energy', 0.0)}
            if 'contaminated_energy' in entry:
                formatted_entry['contaminated'] = {'energy': entry.get('contaminated_energy', 0.0)}
            if 'arbiter_energy' in entry:
                formatted_entry['arbiter'] = {'energy': entry.get('arbiter_energy', 0.0)}
            formatted_data.append(formatted_entry)
        
        self.logger.info(f"Prepared {len(formatted_data)} energy data points for validation")
        return formatted_data
    
    def get_system_status(self):
        """Comprehensive system status"""
        
        return {
            'system_active': True,
            'prediction_count': self.prediction_count,
            'oracles': {
                'virgin': {'active': True, 'frozen': self.virgin.frozen},
                'contaminated': {'active': True, 'adaptive': True},
                'arbiter': {
                    'active': True, 
                    'echo_threshold': self.arbiter.echo_threshold,
                    'decisions_made': len(self.arbiter.decision_history)
                }
            },
            'recent_decisions': self.arbiter.decision_history[-5:] if self.arbiter.decision_history else []
        }

# Factory function
def create_three_oracle_system(config=None):
    """Create complete Three-Oracle system"""
    return ThreeOracleSystem(config)

if __name__ == "__main__":
    # Test the complete system
    print("🏛️ TESTING THREE-ORACLE ARCHITECTURE")
    print("=" * 60)
    
    system = create_three_oracle_system({
        'log_level': 'INFO',
        'enable_enhancement': True
    })
    
    # Test with August 5 data
    test_data = {
        'session_metadata': {
            'session_type': 'NY_AM',
            'date': '2025-08-05'
        },
        'micro_timing_analysis': {
            'cascade_events': [
                # Morning sessions (virgin will use these)
                {'timestamp': '02:08', 'price_level': 23364.0, 'event_type': 'london_fpfvg'},
                {'timestamp': '07:02', 'price_level': 23383.0, 'event_type': 'premarket_fpfvg'},
                
                # AM session data (contaminated will use these too)
                {'timestamp': '09:37', 'price_level': 23341.75, 'event_type': 'am_fpfvg'},
                {'timestamp': '10:18', 'price_level': 23292.5, 'event_type': 'major_cascade'}
            ]
        }
    }
    
    result = system.predict_cascade_timing(test_data, optimize_parameters=True)
    decision = result['timing_decision']
    energy_results = result['energy_results']
    
    print(f"\n🎯 THREE-ORACLE RESULT:")
    print(f"   Final Timing Prediction: {decision.final_prediction:.1f} minutes")
    print(f"   Chosen Oracle: {decision.chosen_oracle.upper()}")
    print(f"   Echo Strength: {decision.echo_strength:.1f} minutes")
    print(f"   Reasoning: {decision.arbiter_reasoning}")
    print(f"   Metacognition: {'🚨 Detected' if decision.metacognition_detected else '✅ Normal'}")
    if energy_results:
        print(f"   Energy Validation: {len(energy_results['predictions'])} predictions")
        if any(energy_results['divergence_detected']):
            print(f"   Energy Divergence: Detected in {sum(energy_results['divergence_detected'])} predictions")
        if energy_results['echo_detected']:
            print(f"   Energy Echo: Detected between Oracles")
    else:
        print(f"   Energy Validation: No energy data available")
    
    status = system.get_system_status()
    print(f"\n📊 System Status:")
    print(f"   Total Predictions: {status['prediction_count']}")
    print(f"   All Oracles Active: {all(o['active'] for o in status['oracles'].values())}")
    print(f"   System Health: {decision.system_health['status']}")
    
    print("\n🎉 Three-Oracle Architecture fully operational!")
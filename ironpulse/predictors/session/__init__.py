"""
Session Prediction Systems
==========================

This package contains session-specific prediction algorithms:

- next_event_predictor: Predicts next events in session sequences
- fresh_session_predictor: Real-world session validation predictor

These modules provide session-level prediction capabilities
for the Project Oracle system.
"""

try:
    from .next_event_predictor import NextEventPredictor
    from .fresh_session_predictor import FreshSessionPredictor
except ImportError:
    pass  # Graceful degradation

__all__ = [
    'NextEventPredictor',
    'FreshSessionPredictor',
]

"""
Cascade Prediction Systems
==========================

This package contains all cascade prediction algorithms and systems:

- automated_cascade_detector: Real-time cascade detection and monitoring
- cascade_predictor: Core cascade prediction engine
- production_cascade_predictor: Production-ready cascade prediction
- live_cascade_predictor: Live trading cascade prediction
- dual_cascade_prediction_system: Hybrid PDA + XGBoost system

These modules provide comprehensive cascade prediction capabilities
for the Project Oracle system.
"""

try:
    from .automated_cascade_detector import AutomatedCascadeDetector
    from .cascade_predictor import CascadePredictor
    from .production_cascade_predictor import ProductionCascadePredictor
    from .live_cascade_predictor import LiveCascadePredictor
    from .dual_cascade_prediction_system import DualCascadePredictionSystem
except ImportError:
    pass  # Graceful degradation

__all__ = [
    'AutomatedCascadeDetector',
    'CascadePredictor',
    'ProductionCascadePredictor',
    'LiveCascadePredictor',
    'DualCascadePredictionSystem',
]

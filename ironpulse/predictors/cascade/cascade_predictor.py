# IRONPULSE Cascade Predictor
# High-confidence pattern matching for financial cascade timing
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from pathlib import Path

class CascadePredictor:
    def __init__(self):
        """Initialize with 18 high-confidence production patterns."""
        self.high_confidence_patterns = [
            {
                'pattern': ['CONSOLIDATION', 'EXPANSION', 'REDELIVERY'],
                'cascade_type': 'classical_cascade',
                'probability': 0.93,
                'time_window': 20,
                'description': 'Primary consolidation-expansion cascade'
            },
            {
                'pattern': ['FPFVG_FORMATION', 'INTERACTION', 'REDELIVERY'],
                'cascade_type': 'fpfvg_cascade',
                'probability': 0.92,
                'time_window': 18,
                'description': 'FPFVG redelivery cascade'
            },
            {
                'pattern': ['EXPANSION_HIGH', 'REVERSAL'],
                'cascade_type': 'momentum_reversal',
                'probability': 0.89,
                'time_window': 15,
                'description': 'High momentum reversal'
            },
            {
                'pattern': ['OPEN', 'CONSOLIDATION', 'EXPANSION'],
                'cascade_type': 'session_opening_cascade',
                'probability': 0.87,
                'time_window': 25,
                'description': 'Session opening expansion'
            },
            {
                'pattern': ['REDELIVERY', 'EXPANSION', 'TAKEOUT'],
                'cascade_type': 'liquidity_cascade',
                'probability': 0.86,
                'time_window': 22,
                'description': 'Liquidity takeout cascade'
            },
            {
                'pattern': ['INTERACTION', 'TAKEOUT'],
                'cascade_type': 'level_break_cascade',
                'probability': 0.85,
                'time_window': 12,
                'description': 'Level break cascade'
            },
            {
                'pattern': ['CONSOLIDATION', 'FPFVG_FORMATION'],
                'cascade_type': 'fpfvg_formation_cascade',
                'probability': 0.84,
                'time_window': 20,
                'description': 'FPFVG formation from consolidation'
            },
            {
                'pattern': ['EXPANSION_LOW', 'REBALANCE'],
                'cascade_type': 'rebalance_cascade',
                'probability': 0.83,
                'time_window': 16,
                'description': 'Low expansion rebalance'
            },
            {
                'pattern': ['OPEN', 'EXPANSION_HIGH'],
                'cascade_type': 'opening_expansion',
                'probability': 0.82,
                'time_window': 18,
                'description': 'Opening high expansion'
            },
            {
                'pattern': ['REDELIVERY', 'REBALANCE'],
                'cascade_type': 'redelivery_rebalance',
                'probability': 0.81,
                'time_window': 14,
                'description': 'Redelivery to rebalance'
            },
            {
                'pattern': ['CONSOLIDATION', 'REVERSAL'],
                'cascade_type': 'consolidation_reversal',
                'probability': 0.80,
                'time_window': 17,
                'description': 'Consolidation reversal break'
            },
            {
                'pattern': ['FPFVG_FORMATION', 'INTERACTION'],
                'cascade_type': 'fpfvg_interaction',
                'probability': 0.80,
                'time_window': 19,
                'description': 'FPFVG interaction cascade'
            },
            {
                'pattern': ['EXPANSION', 'CONSOLIDATION'],
                'cascade_type': 'expansion_consolidation',
                'probability': 0.80,
                'time_window': 21,
                'description': 'Expansion to consolidation'
            },
            {
                'pattern': ['TAKEOUT', 'EXPANSION_HIGH'],
                'cascade_type': 'takeout_expansion',
                'probability': 0.80,
                'time_window': 13,
                'description': 'Takeout expansion cascade'
            },
            {
                'pattern': ['REBALANCE', 'EXPANSION_LOW'],
                'cascade_type': 'rebalance_expansion',
                'probability': 0.80,
                'time_window': 15,
                'description': 'Rebalance to expansion'
            },
            {
                'pattern': ['REVERSAL', 'CONSOLIDATION'],
                'cascade_type': 'reversal_consolidation',
                'probability': 0.80,
                'time_window': 16,
                'description': 'Reversal to consolidation'
            },
            {
                'pattern': ['INTERACTION', 'REDELIVERY'],
                'cascade_type': 'interaction_redelivery',
                'probability': 0.80,
                'time_window': 14,
                'description': 'Interaction to redelivery'
            },
            {
                'pattern': ['LIQUIDITY_GRAB', 'EXPANSION'],
                'cascade_type': 'liquidity_expansion',
                'probability': 0.80,
                'time_window': 18,
                'description': 'Liquidity grab expansion'
            }
        ]
        self.active_sequences = []
        
    def load_session(self, filepath: str) -> Dict:
        """Load an enhanced session file."""
        try:
            with open(filepath, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"❌ Session file not found: {filepath}")
            return {}
        except json.JSONDecodeError:
            print(f"❌ Invalid JSON in file: {filepath}")
            return {}
    
    def extract_live_events(self, session: Dict) -> List[Dict]:
        """Extract the most recent event sequence from session."""
        events = []
        
        # Extract from grammatical intelligence layer
        if 'grammatical_intelligence' in session:
            gram_intel = session['grammatical_intelligence']
            
            # Get event classifications
            event_classifications = gram_intel.get('event_classification', [])
            for event in event_classifications[-10:]:  # Last 10 events
                events.append({
                    'type': event.get('event_type', 'UNKNOWN'),
                    'time': event.get('timestamp', ''),
                    'price': event.get('price', 0),
                    'session_time': event.get('session_time_minutes', 0)
                })
            
            # Also extract from event sequences
            event_sequences = gram_intel.get('event_sequences', [])
            for seq in event_sequences[-3:]:  # Last 3 sequences
                sequence_events = seq.get('events', [])
                for event_type in sequence_events:
                    events.append({
                        'type': event_type,
                        'time': seq.get('time_window', {}).get('start_time', ''),
                        'price': 0,  # Price not available in sequences
                        'session_time': 0
                    })
        
        # Extract from level1_json layer as backup
        if not events and 'level1_json' in session:
            level1 = session['level1_json']
            
            # Extract from price movements
            price_movements = level1.get('price_movements', [])
            for movement in price_movements[-10:]:  # Last 10 movements
                movement_type = movement.get('movement_type', '')
                
                # Map movement types to grammatical events
                event_type = self._map_movement_to_event(movement_type)
                
                events.append({
                    'type': event_type,
                    'time': movement.get('timestamp', ''),
                    'price': movement.get('price_level', 0),
                    'session_time': 0
                })
            
            # Extract from FPFVG data
            session_fpfvg = level1.get('session_fpfvg', {})
            if session_fpfvg.get('fpfvg_present'):
                fpfvg_formation = session_fpfvg.get('fpfvg_formation', {})
                events.append({
                    'type': 'FPFVG_FORMATION',
                    'time': fpfvg_formation.get('formation_time', ''),
                    'price': fpfvg_formation.get('premium_high', 0),
                    'session_time': 0
                })
                
                # Extract interactions
                interactions = fpfvg_formation.get('interactions', [])
                for interaction in interactions[-5:]:  # Last 5 interactions
                    interaction_type = interaction.get('interaction_type', '').upper()
                    events.append({
                        'type': interaction_type,
                        'time': interaction.get('interaction_time', ''),
                        'price': interaction.get('price_level', 0),
                        'session_time': 0
                    })
        
        # Remove duplicates and sort by time
        unique_events = []
        seen_times = set()
        
        for event in events:
            event_key = (event['type'], event['time'])
            if event_key not in seen_times and event['type'] != 'UNKNOWN':
                unique_events.append(event)
                seen_times.add(event_key)
        
        # Sort by time and return last 8 events
        try:
            unique_events.sort(key=lambda x: x.get('time', ''))
        except:
            pass  # Keep original order if sorting fails
        
        return unique_events[-8:]
    
    def _map_movement_to_event(self, movement_type: str) -> str:
        """Map price movement types to grammatical events."""
        movement_type = movement_type.lower()
        
        if 'open' in movement_type:
            return 'OPEN'
        elif 'expansion' in movement_type:
            if 'high' in movement_type:
                return 'EXPANSION_HIGH'
            elif 'low' in movement_type:
                return 'EXPANSION_LOW'
            else:
                return 'EXPANSION'
        elif 'consolidation' in movement_type:
            return 'CONSOLIDATION'
        elif 'redelivery' in movement_type:
            return 'REDELIVERY'
        elif 'rebalance' in movement_type:
            return 'REBALANCE'
        elif 'fpfvg' in movement_type:
            return 'FPFVG_FORMATION'
        elif 'takeout' in movement_type:
            return 'TAKEOUT'
        elif 'reversal' in movement_type:
            return 'REVERSAL'
        elif 'interaction' in movement_type:
            return 'INTERACTION'
        elif 'liquidity' in movement_type:
            return 'LIQUIDITY_GRAB'
        else:
            return 'UNKNOWN'
    
    def check_pattern_match(self, event_sequence: List[str], pattern: List[str]) -> float:
        """Check if current events match a pattern prefix."""
        if not event_sequence or not pattern:
            return 0.0
            
        # Check for subsequence match (pattern can be anywhere in sequence)
        for i in range(len(event_sequence) - len(pattern) + 1):
            subseq = event_sequence[i:i+len(pattern)]
            if subseq == pattern:
                return 1.0  # Complete match
        
        # Check for partial match at the end
        for pattern_len in range(1, len(pattern)):
            if len(event_sequence) >= pattern_len:
                recent_events = event_sequence[-pattern_len:]
                pattern_prefix = pattern[:pattern_len]
                if recent_events == pattern_prefix:
                    return pattern_len / len(pattern)
        
        return 0.0
    
    def predict_cascade(self, session_data: Dict) -> Dict:
        """Main prediction function."""
        events = self.extract_live_events(session_data)
        event_types = [e['type'] for e in events if e['type'] != 'UNKNOWN']
        
        if not event_types:
            return {
                'timestamp': datetime.now().isoformat(),
                'session_id': 'unknown',
                'current_events': [],
                'cascade_predictions': [],
                'highest_probability': None,
                'recommendation': 'NO_EVENTS_DETECTED'
            }
        
        predictions = []
        
        for pattern_def in self.high_confidence_patterns:
            pattern = pattern_def['pattern']
            completion = self.check_pattern_match(event_types, pattern)
            
            if completion > 0:
                # Calculate which events are remaining
                if completion == 1.0:
                    remaining = []
                    status = 'PATTERN_COMPLETE'
                else:
                    matched_length = int(completion * len(pattern))
                    remaining = pattern[matched_length:]
                    status = remaining[0] if remaining else 'PATTERN_COMPLETE'
                
                # Calculate adjusted probability based on completion
                adjusted_probability = pattern_def['probability'] * (completion ** 0.5)
                
                predictions.append({
                    'cascade_type': pattern_def['cascade_type'],
                    'description': pattern_def['description'],
                    'probability': adjusted_probability,
                    'base_probability': pattern_def['probability'],
                    'pattern_completion': f"{int(completion * 100)}%",
                    'completion_decimal': completion,
                    'matched_pattern': pattern[:int(completion * len(pattern))],
                    'full_pattern': pattern,
                    'current_sequence': event_types[-len(pattern):] if len(event_types) >= len(pattern) else event_types,
                    'expecting_next': status,
                    'remaining_events': remaining,
                    'time_window': pattern_def['time_window'],
                    'confidence': self._calculate_confidence(completion, adjusted_probability)
                })
        
        # Sort by adjusted probability
        predictions.sort(key=lambda x: x['probability'], reverse=True)
        
        # Get session metadata
        session_id = 'unknown'
        if 'level1_json' in session_data:
            metadata = session_data['level1_json'].get('session_metadata', {})
            session_type = metadata.get('session_type', 'unknown')
            session_date = metadata.get('session_date', 'unknown')
            session_id = f"{session_type.upper()}_{session_date}"
        
        return {
            'timestamp': datetime.now().isoformat(),
            'session_id': session_id,
            'current_events': event_types,
            'event_count': len(event_types),
            'cascade_predictions': predictions[:5],  # Top 5 predictions
            'highest_probability': predictions[0] if predictions else None,
            'recommendation': self.generate_recommendation(predictions),
            'analysis_depth': len(events),
            'pattern_matches_found': len(predictions)
        }
    
    def _calculate_confidence(self, completion: float, probability: float) -> str:
        """Calculate confidence level based on completion and probability."""
        score = (completion * 0.6) + (probability * 0.4)
        
        if score >= 0.8:
            return 'VERY_HIGH'
        elif score >= 0.7:
            return 'HIGH'
        elif score >= 0.5:
            return 'MEDIUM'
        elif score >= 0.3:
            return 'LOW'
        else:
            return 'VERY_LOW'
    
    def generate_recommendation(self, predictions: List[Dict]) -> str:
        """Generate trading recommendation based on predictions."""
        if not predictions:
            return "🟢 NO_CASCADE_PATTERN_DETECTED - Continue monitoring"
        
        top = predictions[0]
        prob = top['probability']
        completion = top['completion_decimal']
        
        if prob > 0.85 and completion > 0.8:
            return f"🚨 CRITICAL CASCADE ALERT: {top['cascade_type']} pattern {top['pattern_completion']} complete. Expecting {top['expecting_next']} within {top['time_window']} minutes. HIGH PROBABILITY: {prob:.1%}"
        elif prob > 0.75:
            return f"⚠️ HIGH CASCADE RISK: {top['cascade_type']} pattern detected. Current completion: {top['pattern_completion']}. Next: {top['expecting_next']} within {top['time_window']} minutes."
        elif prob > 0.6:
            return f"🔶 MODERATE CASCADE POTENTIAL: Monitoring {top['cascade_type']} pattern ({top['pattern_completion']} complete). Watch for {top['expecting_next']}"
        elif prob > 0.4:
            return f"🔵 LOW CASCADE RISK: Early {top['cascade_type']} pattern forming. Completion: {top['pattern_completion']}"
        else:
            return "🟢 MINIMAL CASCADE RISK: No significant patterns detected"
    
    def run_live_prediction(self, session_path: str):
        """Run prediction on a specific session file."""
        print("=" * 70)
        print("🎯 CASCADE PREDICTION SYSTEM - LIVE ANALYSIS")
        print("=" * 70)
        
        if not os.path.exists(session_path):
            print(f"❌ Session file not found: {session_path}")
            return None
        
        try:
            session = self.load_session(session_path)
            if not session:
                return None
                
            result = self.predict_cascade(session)
            
            print(f"\n📊 SESSION ANALYSIS:")
            print(f"   Session: {result['session_id']}")
            print(f"   Analysis Time: {result['timestamp']}")
            print(f"   Events Analyzed: {result['event_count']}")
            print(f"   Pattern Matches: {result['pattern_matches_found']}")
            
            if result['current_events']:
                print(f"\n📈 CURRENT EVENT SEQUENCE:")
                if len(result['current_events']) >= 3:
                    print(f"   Recent: {' → '.join(result['current_events'][-3:])}")
                    print(f"   Full: {' → '.join(result['current_events'])}")
                else:
                    print(f"   Events: {' → '.join(result['current_events'])}")
            
            if result['highest_probability']:
                top = result['highest_probability']
                print(f"\n🎯 PRIMARY CASCADE PREDICTION:")
                print(f"   Type: {top['cascade_type'].replace('_', ' ').title()}")
                print(f"   Description: {top['description']}")
                print(f"   Pattern: {' → '.join(top['full_pattern'])}")
                print(f"   Matched: {' → '.join(top['matched_pattern'])} ({top['pattern_completion']})")
                print(f"   Cascade Probability: {top['probability']:.1%}")
                print(f"   Base Probability: {top['base_probability']:.1%}")
                print(f"   Next Expected: {top['expecting_next']}")
                if top['remaining_events']:
                    print(f"   Remaining: {' → '.join(top['remaining_events'])}")
                print(f"   Time Window: {top['time_window']} minutes")
                print(f"   Confidence: {top['confidence']}")
            
            print(f"\n💡 RECOMMENDATION:")
            print(f"   {result['recommendation']}")
            
            if len(result['cascade_predictions']) > 1:
                print(f"\n📋 ALTERNATIVE PATTERNS:")
                for i, pred in enumerate(result['cascade_predictions'][1:4], 1):  # Show top 3 alternatives
                    print(f"   {i}. {pred['cascade_type'].replace('_', ' ').title()}: {pred['probability']:.1%} "
                          f"({pred['pattern_completion']}) → {pred['expecting_next']}")
            
            print(f"\n" + "=" * 70)
            
            return result
            
        except Exception as e:
            print(f"❌ Error processing session: {e}")
            import traceback
            traceback.print_exc()
            return None

def find_latest_session() -> Optional[str]:
    """Find the most recent enhanced session file."""
    enhanced_dir = Path("enhanced_sessions")
    
    if not enhanced_dir.exists():
        # Try current directory
        enhanced_files = list(Path(".").glob("enhanced_*.json"))
        if enhanced_files:
            return str(max(enhanced_files, key=lambda f: f.stat().st_mtime))
        return None
    
    enhanced_files = list(enhanced_dir.glob("enhanced_*.json"))
    if not enhanced_files:
        return None
    
    # Return most recently modified file
    latest_file = max(enhanced_files, key=lambda f: f.stat().st_mtime)
    return str(latest_file)

def demo_prediction():
    """Demonstration function showing cascade prediction capabilities."""
    predictor = CascadePredictor()
    
    print("🧪 DEMONSTRATION: Live Cascade Prediction System")
    print("=" * 60)
    print("Initializing with 18 high-confidence patterns from statistical analysis...")
    print(f"Patterns loaded: {len(predictor.high_confidence_patterns)}")
    
    # Create mock session data for demonstration
    mock_session = {
        "level1_json": {
            "session_metadata": {
                "session_type": "nyam",
                "session_date": "2025-08-07",
                "session_start": "09:30:00"
            },
            "price_movements": [
                {"timestamp": "09:30:00", "price_level": 23500.0, "movement_type": "open"},
                {"timestamp": "09:35:00", "price_level": 23485.0, "movement_type": "consolidation"},
                {"timestamp": "09:42:00", "price_level": 23520.0, "movement_type": "expansion_high"},
            ]
        },
        "grammatical_intelligence": {
            "event_classification": [
                {"event_type": "OPEN", "timestamp": "09:30:00", "price": 23500.0},
                {"event_type": "CONSOLIDATION", "timestamp": "09:35:00", "price": 23485.0},
                {"event_type": "EXPANSION_HIGH", "timestamp": "09:42:00", "price": 23520.0}
            ]
        }
    }
    
    print("\n🔍 Analyzing mock session with pattern: OPEN → CONSOLIDATION → EXPANSION_HIGH")
    result = predictor.predict_cascade(mock_session)
    
    if result and result['highest_probability']:
        top = result['highest_probability']
        print(f"\n✅ Pattern detected: {top['cascade_type']}")
        print(f"   Probability: {top['probability']:.1%}")
        print(f"   Next expected: {top['expecting_next']}")
        print(f"   Time window: {top['time_window']} minutes")
    
    return result

def main():
    """Main function for running cascade predictions."""
    predictor = CascadePredictor()
    
    # Try to find and analyze latest session
    latest_session = find_latest_session()
    
    if latest_session:
        print(f"📁 Found latest session: {latest_session}")
        result = predictor.run_live_prediction(latest_session)
        return result
    else:
        print("📁 No enhanced session files found. Running demonstration...")
        return demo_prediction()

if __name__ == "__main__":
    # Run cascade prediction
    prediction_result = main()
"""
Three Oracle Architecture
=========================

This package contains the Three Oracle architecture components:

- three_oracle_architecture: Core Three Oracle system
- three_oracle_maximizer: Performance optimization system

The Three Oracle architecture combines:
1. Virgin Oracle (Pure grammatical intelligence)
2. Contaminated Oracle (Grammar Bridge enhanced ML)
3. Arbiter Oracle (Consensus decision system)
"""

try:
    from .three_oracle_architecture import ThreeOracleArchitecture
    from .three_oracle_maximizer import ThreeOracleMaximizer
except ImportError:
    pass  # Graceful degradation

__all__ = [
    'ThreeOracleArchitecture',
    'ThreeOracleMaximizer',
]

"""
Project Oracle Predictors Package
=================================

This package contains all prediction modules organized by type:

- cascade: Cascade prediction systems
- three_oracle: Three Oracle architecture components
- session: Session-specific predictors
- nyam: NYAM (New York AM) session predictors
- pattern: Pattern completion and analysis predictors
- ensemble: Ensemble prediction systems
- oracle: Core Oracle prediction engines

Each subpackage contains specialized prediction algorithms and their supporting
infrastructure for the Project Oracle system.
"""

# Import key predictor classes for convenience
try:
    from .cascade.automated_cascade_detector import AutomatedCascadeDetector
    from .cascade.cascade_predictor import CascadePredictor
    from .cascade.production_cascade_predictor import ProductionCascadePredictor
    from .cascade.live_cascade_predictor import LiveCascadePredictor
    from .cascade.dual_cascade_prediction_system import DualCascadePredictionSystem
except ImportError:
    pass  # Graceful degradation if modules not available

try:
    from .three_oracle.three_oracle_architecture import ThreeOracleArchitecture
    from .three_oracle.three_oracle_maximizer import ThreeOracleMaximizer
except ImportError:
    pass

try:
    from .session.next_event_predictor import NextEventPredictor
    from .session.fresh_session_predictor import FreshSessionPredictor
except ImportError:
    pass

try:
    from .nyam.nyam_prediction_algorithm import NYAMPredictionAlgorithm
    from .nyam.nyam_prediction_protocol import NYAMPredictionProtocol
except ImportError:
    pass

try:
    from .pattern.pattern_completion_predictor import PatternCompletionPredictor
except ImportError:
    pass

try:
    from .ensemble.ensemble_production_predictor import EnsembleProductionPredictor
    from .ensemble.ensemble_variance_reducer import EnsembleVarianceReducer
except ImportError:
    pass

try:
    from .oracle.oracle import Oracle
    from .oracle.oracle_api import OracleAPI
    from .oracle.oracle_core import OracleCore
    from .oracle.production_oracle import ProductionOracle
except ImportError:
    pass

__all__ = [
    # Cascade predictors
    'AutomatedCascadeDetector',
    'CascadePredictor',
    'ProductionCascadePredictor',
    'LiveCascadePredictor',
    'DualCascadePredictionSystem',

    # Three Oracle architecture
    'ThreeOracleArchitecture',
    'ThreeOracleMaximizer',

    # Session predictors
    'NextEventPredictor',
    'FreshSessionPredictor',

    # NYAM predictors
    'NYAMPredictionAlgorithm',
    'NYAMPredictionProtocol',

    # Pattern predictors
    'PatternCompletionPredictor',

    # Ensemble predictors
    'EnsembleProductionPredictor',
    'EnsembleVarianceReducer',

    # Oracle predictors
    'Oracle',
    'OracleAPI',
    'OracleCore',
    'ProductionOracle',
]

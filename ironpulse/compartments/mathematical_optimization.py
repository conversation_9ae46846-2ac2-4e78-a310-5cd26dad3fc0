"""
Mathematical Optimization Compartment
====================================

Integrates the mathematical layers architecture with the Oracle compartments system.
Provides mathematical model optimization, validation, and performance monitoring
as a compartment within the Oracle processing pipeline.

Key Features:
- Integration with existing Oracle compartment system
- Mathematical model lifecycle management
- Performance optimization and monitoring
- Automated model validation and testing
- Hooks integration for real-time monitoring
"""

import os
import sys
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

# Ensure core_predictor is importable as a package
CORE_PREDICTOR_DIR = os.path.join(os.path.dirname(__file__), '..', 'core_predictor')
if CORE_PREDICTOR_DIR not in sys.path:
    sys.path.insert(0, CORE_PREDICTOR_DIR)

from ironpulse.core.mathematical_layers import (
    MathematicalModelRegistry,
    ModelChain,
    create_oracle_prediction_chain
)
from ironpulse.core.mathematical_hooks import (
    create_oracle_hook_manager,
    HookType,
    HookContext
)
from ironpulse.core.scaling_patterns import create_adaptive_scaling_system
from ironpulse.core.mathematical_layers.validation_framework import (
    create_validation_framework,
    ValidationLevel
)

# Oracle compartments base imports
try:
    from .base import CompartmentBase, CompartmentStatus, ProcessingResult
except ImportError:
    # Fallback base classes if Oracle compartments not available
    from enum import Enum
    
    class CompartmentStatus(Enum):
        PENDING = "pending"
        RUNNING = "running"
        COMPLETED = "completed"
        FAILED = "failed"
    
    class ProcessingResult:
        def __init__(self, status: CompartmentStatus, data: Dict[str, Any], metrics: Dict[str, Any]):
            self.status = status
            self.data = data
            self.metrics = metrics
    
    class CompartmentBase:
        def __init__(self, compartment_id: str):
            self.compartment_id = compartment_id
            self.status = CompartmentStatus.PENDING
        
        def process(self, input_data: Dict[str, Any]) -> ProcessingResult:
            raise NotImplementedError

logger = logging.getLogger(__name__)

class MathematicalOptimizationCompartment(CompartmentBase):
    """
    Mathematical optimization compartment for the Oracle system.
    
    Responsibilities:
    - Mathematical model initialization and configuration
    - Real-time performance monitoring
    - Automated model validation
    - Parameter optimization
    - Mathematical predictions integration
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("mathematical_optimization")
        
        self.config = config or self._load_default_config()
        self.model_registry: Optional[MathematicalModelRegistry] = None
        self.hook_manager = None
        self.scaling_system = None
        self.validation_framework = None
        self.processing_metrics = {
            "total_executions": 0,
            "successful_executions": 0,
            "average_execution_time_ms": 0.0,
            "last_validation_timestamp": None
        }
        
        # Initialize mathematical components
        self._initialize_mathematical_components()
    
    def _load_default_config(self) -> Dict[str, Any]:
        """Load default configuration for mathematical optimization"""
        
        return {
            "oracle_integration": True,
            "validation_level": "standard",
            "enable_hooks": True,
            "enable_scaling": True,
            "performance_sli_ms": 200,  # Oracle SLI: 200ms
            "accuracy_threshold": 0.91,  # Oracle accuracy requirement
            "mathematical_models": {
                "hawkes_process": {
                    "enabled": True,
                    "precision": 30,
                    "vectorized": True
                },
                "htf_coupling": {
                    "enabled": True,
                    "activation_threshold": 0.5
                },
                "three_oracle_consensus": {
                    "enabled": True,
                    "confidence_threshold": 0.8
                }
            },
            "hooks": {
                "parameter_drift_threshold": 0.1,
                "performance_degradation_threshold": 500.0,
                "enable_invariant_validation": True
            },
            "scaling": {
                "max_horizontal_workers": 8,
                "memory_limit_mb": 1000,
                "adaptive_strategy_selection": True
            }
        }
    
    def _initialize_mathematical_components(self):
        """Initialize mathematical components for the compartment"""
        
        try:
            # Initialize model registry with Oracle integration
            oracle_integration = self.config.get("oracle_integration", True)
            self.model_registry = MathematicalModelRegistry(oracle_integration=oracle_integration)
            
            # Initialize hooks if enabled
            if self.config.get("enable_hooks", True):
                self.hook_manager = create_oracle_hook_manager()
                logger.info("Mathematical hooks system initialized")
            
            # Initialize scaling system if enabled
            if self.config.get("enable_scaling", True):
                self.scaling_system = create_adaptive_scaling_system()
                logger.info("Adaptive scaling system initialized")
            
            # Initialize validation framework
            validation_level_str = self.config.get("validation_level", "standard")
            validation_level = ValidationLevel(validation_level_str)
            self.validation_framework = create_validation_framework(validation_level)
            logger.info(f"Validation framework initialized with level: {validation_level.value}")
            
            self.status = CompartmentStatus.PENDING
            logger.info("Mathematical optimization compartment initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize mathematical components: {e}")
            self.status = CompartmentStatus.FAILED
    
    def process(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """
        Process mathematical optimization for Oracle data.
        
        Expected input_data structure:
        {
            "operation": "predict" | "validate" | "optimize" | "status",
            "session_data": {...},  # Oracle session data
            "model_type": "hawkes_process" | "htf_coupling" | "three_oracle_consensus",
            "parameters": {...},     # Optional model parameters
            "performance_target": {...}  # Performance requirements
        }
        """
        
        start_time = datetime.now()
        self.status = CompartmentStatus.RUNNING
        
        try:
            # Validate input
            operation = input_data.get("operation", "predict")
            
            if operation == "predict":
                result = self._process_prediction(input_data)
            elif operation == "validate":
                result = self._process_validation(input_data)
            elif operation == "optimize":
                result = self._process_optimization(input_data)
            elif operation == "status":
                result = self._process_status_check(input_data)
            else:
                raise ValueError(f"Unknown operation: {operation}")
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # Update processing metrics
            self.processing_metrics["total_executions"] += 1
            if result.status == CompartmentStatus.COMPLETED:
                self.processing_metrics["successful_executions"] += 1
            
            # Update average execution time
            current_avg = self.processing_metrics["average_execution_time_ms"]
            total_execs = self.processing_metrics["total_executions"]
            self.processing_metrics["average_execution_time_ms"] = (
                (current_avg * (total_execs - 1) + execution_time) / total_execs
            )
            
            # Trigger performance monitoring hooks
            if self.hook_manager and result.status == CompartmentStatus.COMPLETED:
                hook_context = HookContext(
                    hook_type=HookType.POST_COMPUTATION,
                    model_id="mathematical_optimization_compartment",
                    timestamp=datetime.now(),
                    data={
                        "performance_metrics": {
                            "execution_time_ms": execution_time,
                            "memory_usage_mb": result.metrics.get("memory_usage_mb", 0),
                            "accuracy": result.metrics.get("accuracy", 1.0)
                        },
                        "operation": operation,
                        "success": True
                    }
                )
                
                # Run hooks asynchronously (fire and forget for compartment processing)
                import asyncio
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    hook_results = loop.run_until_complete(
                        self.hook_manager.trigger_hooks(hook_context)
                    )
                    result.metrics["hook_results"] = [
                        {k: v for k, v in hr.items() if k not in ["hook_id", "execution_time_seconds"]}
                        for hr in hook_results
                    ]
                except Exception as hook_error:
                    logger.warning(f"Hook execution failed: {hook_error}")
            
            result.metrics["execution_time_ms"] = execution_time
            self.status = result.status
            
            return result
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            logger.error(f"Mathematical optimization processing failed: {e}")
            self.status = CompartmentStatus.FAILED
            
            return ProcessingResult(
                status=CompartmentStatus.FAILED,
                data={"error": str(e), "operation": input_data.get("operation", "unknown")},
                metrics={
                    "execution_time_ms": execution_time,
                    "error_type": type(e).__name__
                }
            )
    
    def _process_prediction(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """Process mathematical prediction request"""
        
        session_data = input_data.get("session_data", {})
        model_type = input_data.get("model_type", "hawkes_process")
        parameters = input_data.get("parameters", {})
        performance_target = input_data.get("performance_target", {
            "max_execution_time_seconds": 5.0,
            "min_accuracy": 0.85
        })
        
        if not session_data:
            raise ValueError("session_data is required for prediction")
        
        # Create or get prediction chain
        if model_type == "oracle_standard":
            # Use pre-configured Oracle prediction chain
            chain_spec = {
                "chain_id": "oracle_standard_prediction",
                "description": "Standard Oracle cascade prediction pipeline",
                "steps": [
                    {
                        "type": "hawkes_prediction",
                        "model_id": "oracle_hawkes_process" if self.model_registry else "hawkes_process",
                        "parameters": parameters
                    }
                ]
            }
        else:
            # Single model prediction
            chain_spec = {
                "chain_id": f"{model_type}_prediction",
                "description": f"Single {model_type} prediction",
                "steps": [
                    {
                        "type": "generic",
                        "model_id": model_type,
                        "parameters": parameters
                    }
                ]
            }
        
        # Create and execute prediction chain
        chain = self.model_registry.create_model_chain(chain_spec)
        
        # Execute with scaling if available
        if self.scaling_system:
            import asyncio
            
            def prediction_function(data, params):
                return self.model_registry.execute_prediction_pipeline(data, chain.chain_id)
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            scaling_result = loop.run_until_complete(
                self.scaling_system.execute_with_adaptive_scaling(
                    prediction_function,
                    session_data,
                    performance_target
                )
            )
            
            if scaling_result["success"]:
                prediction_result = scaling_result["result"]
                execution_metrics = {
                    "scaling_strategy": scaling_result["scaling_strategy"],
                    "execution_time_ms": scaling_result["execution_time_seconds"] * 1000,
                    "data_throughput": scaling_result["performance_metrics"]["data_throughput"]
                }
            else:
                raise RuntimeError(f"Scaled prediction failed: {scaling_result}")
        else:
            # Direct execution without scaling
            prediction_result = self.model_registry.execute_prediction_pipeline(
                session_data, chain.chain_id
            )
            execution_metrics = {
                "scaling_strategy": "none",
                "execution_time_ms": prediction_result.get("total_duration_ms", 0)
            }
        
        return ProcessingResult(
            status=CompartmentStatus.COMPLETED,
            data={
                "prediction_result": prediction_result,
                "model_type": model_type,
                "chain_id": chain.chain_id
            },
            metrics=execution_metrics
        )
    
    def _process_validation(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """Process mathematical model validation request"""
        
        model_type = input_data.get("model_type", "hawkes_process")
        validation_data = input_data.get("validation_data", {})
        
        if not self.validation_framework:
            raise RuntimeError("Validation framework not initialized")
        
        # Get model from registry
        if model_type not in self.model_registry.models:
            raise ValueError(f"Model {model_type} not registered")
        
        model = self.model_registry.models[model_type]
        
        # Run comprehensive validation
        validation_results = self.validation_framework.comprehensive_validation(
            model, model_type, validation_data
        )
        
        # Generate validation report
        validation_report = self.validation_framework.generate_validation_report(
            validation_results
        )
        
        # Calculate overall validation status
        total_tests = sum(suite.get_summary()["total"] for suite in validation_results.values())
        total_passed = sum(suite.get_summary()["pass"] for suite in validation_results.values())
        overall_success_rate = total_passed / total_tests if total_tests > 0 else 0.0
        
        validation_status = "PASS" if overall_success_rate >= 0.8 else "FAIL"
        
        # Update processing metrics
        self.processing_metrics["last_validation_timestamp"] = datetime.now().isoformat()
        
        return ProcessingResult(
            status=CompartmentStatus.COMPLETED,
            data={
                "validation_status": validation_status,
                "validation_report": validation_report,
                "validation_results": {
                    name: suite.get_summary() for name, suite in validation_results.items()
                },
                "overall_success_rate": overall_success_rate
            },
            metrics={
                "total_tests": total_tests,
                "total_passed": total_passed,
                "validation_suites": len(validation_results)
            }
        )
    
    def _process_optimization(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """Process mathematical model optimization request"""
        
        model_type = input_data.get("model_type", "hawkes_process")
        training_data = input_data.get("training_data", {})
        optimization_target = input_data.get("optimization_target", "accuracy")
        
        if not training_data:
            raise ValueError("training_data is required for optimization")
        
        # Get model from registry
        if model_type not in self.model_registry.models:
            raise ValueError(f"Model {model_type} not registered")
        
        model = self.model_registry.models[model_type]
        
        # Extract events from Oracle training data format
        if isinstance(training_data, dict) and "events" in training_data:
            events_data = []
            for event in training_data["events"]:
                if isinstance(event, dict) and "timestamp" in event:
                    events_data.append(event["timestamp"])
                elif isinstance(event, (int, float)):
                    events_data.append(event)
            training_events = np.array(events_data) if events_data else np.array([])
        elif isinstance(training_data, (list, np.ndarray)):
            training_events = np.array(training_data)
        else:
            raise ValueError("Invalid training_data format")
        
        if len(training_events) == 0:
            raise ValueError("No valid training events found")
        
        # Run parameter optimization
        optimization_result = model.optimize_parameters(training_events)
        
        # Validate optimized model if validation framework available
        validation_summary = None
        if self.validation_framework:
            try:
                # Update model with optimized parameters
                optimized_params = {
                    k: v for k, v in optimization_result.items()
                    if k in ["mu", "alpha", "beta"] and isinstance(v, (int, float))
                }
                
                # Quick validation
                invariant_suite = self.validation_framework.validate_mathematical_invariants(
                    model, model_type
                )
                validation_summary = invariant_suite.get_summary()
                
            except Exception as e:
                logger.warning(f"Optimization validation failed: {e}")
        
        return ProcessingResult(
            status=CompartmentStatus.COMPLETED,
            data={
                "optimization_result": optimization_result,
                "model_type": model_type,
                "training_events_count": len(training_events),
                "validation_summary": validation_summary
            },
            metrics={
                "optimization_success": optimization_result.get("optimization_success", False),
                "final_objective": optimization_result.get("final_objective", None),
                "method": optimization_result.get("method", "unknown")
            }
        )
    
    def _process_status_check(self, input_data: Dict[str, Any]) -> ProcessingResult:
        """Process status check request"""
        
        status_data = {
            "compartment_status": self.status.value,
            "processing_metrics": self.processing_metrics.copy(),
            "component_status": {
                "model_registry": "initialized" if self.model_registry else "not_initialized",
                "hook_manager": "initialized" if self.hook_manager else "not_initialized",
                "scaling_system": "initialized" if self.scaling_system else "not_initialized",
                "validation_framework": "initialized" if self.validation_framework else "not_initialized"
            },
            "configuration": self.config.copy()
        }
        
        # Get model registry status if available
        if self.model_registry:
            integration_status = self.model_registry.get_integration_status()
            performance_summary = self.model_registry.get_model_performance_summary()
            
            status_data["model_registry_status"] = {
                "integration_status": {k: v.value for k, v in integration_status.items()},
                "performance_summary": performance_summary,
                "registered_models": list(self.model_registry.models.keys()),
                "prediction_chains": list(self.model_registry.chains.keys())
            }
        
        # Get hook manager status if available
        if self.hook_manager:
            hook_summary = self.hook_manager.get_hook_performance_summary()
            active_alerts = self.hook_manager.get_active_alerts()
            
            status_data["hook_manager_status"] = {
                "performance_summary": hook_summary,
                "active_alerts_count": len(active_alerts),
                "active_alerts": [
                    {
                        "alert_id": alert.alert_id,
                        "level": alert.level.value,
                        "model_id": alert.model_id,
                        "message": alert.message
                    }
                    for alert in active_alerts[:5]  # Limit to first 5 alerts
                ]
            }
        
        # Get scaling system status if available
        if self.scaling_system:
            scaling_summary = self.scaling_system.get_performance_summary()
            status_data["scaling_system_status"] = scaling_summary
        
        return ProcessingResult(
            status=CompartmentStatus.COMPLETED,
            data=status_data,
            metrics={
                "status_check_timestamp": datetime.now().isoformat(),
                "components_initialized": sum(1 for v in status_data["component_status"].values() if v == "initialized")
            }
        )

def create_mathematical_optimization_compartment(config_path: Optional[str] = None) -> MathematicalOptimizationCompartment:
    """Create mathematical optimization compartment with optional custom configuration"""
    
    config = None
    if config_path and os.path.exists(config_path):
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            logger.info(f"Loaded configuration from {config_path}")
        except Exception as e:
            logger.warning(f"Failed to load configuration from {config_path}: {e}")
    
    return MathematicalOptimizationCompartment(config)

# Integration with Oracle compartments system
def register_mathematical_optimization_compartment():
    """Register mathematical optimization compartment with Oracle system"""
    
    # This would be called by the Oracle compartments system
    # to register this compartment for processing
    
    compartment_info = {
        "compartment_id": "mathematical_optimization",
        "compartment_class": MathematicalOptimizationCompartment,
        "description": "Mathematical model optimization and validation",
        "dependencies": [],  # No dependencies on other compartments
        "supported_operations": [
            "predict",      # Mathematical predictions
            "validate",     # Model validation
            "optimize",     # Parameter optimization
            "status"        # Status check
        ],
        "performance_sli_ms": 200,  # Oracle SLI requirement
        "gates": {
            "accuracy_threshold": 0.91,
            "max_execution_time_ms": 5000
        }
    }
    
    return compartment_info

if __name__ == "__main__":
    print("🧮 MATHEMATICAL OPTIMIZATION COMPARTMENT TESTING")
    print("=" * 60)
    
    # Create mathematical optimization compartment
    compartment = create_mathematical_optimization_compartment()
    
    print(f"Compartment Status: {compartment.status.value}")
    print(f"Components Initialized:")
    print(f"  Model Registry: {'✅' if compartment.model_registry else '❌'}")
    print(f"  Hook Manager: {'✅' if compartment.hook_manager else '❌'}")
    print(f"  Scaling System: {'✅' if compartment.scaling_system else '❌'}")
    print(f"  Validation Framework: {'✅' if compartment.validation_framework else '❌'}")
    
    # Test status check operation
    print(f"\n📊 STATUS CHECK TEST:")
    status_input = {"operation": "status"}
    status_result = compartment.process(status_input)
    
    print(f"  Status Check: {'✅' if status_result.status == CompartmentStatus.COMPLETED else '❌'}")
    if status_result.status == CompartmentStatus.COMPLETED:
        components = status_result.data["component_status"]
        initialized_count = sum(1 for v in components.values() if v == "initialized")
        print(f"  Components Initialized: {initialized_count}/{len(components)}")
        
        if "model_registry_status" in status_result.data:
            registry_status = status_result.data["model_registry_status"]
            models_count = len(registry_status["registered_models"])
            print(f"  Registered Models: {models_count}")
    
    # Test prediction operation
    print(f"\n🔮 PREDICTION TEST:")
    prediction_input = {
        "operation": "predict",
        "model_type": "hawkes_process",
        "session_data": {
            "events": [
                {"timestamp": 1.0, "type": "liquidity_sweep"},
                {"timestamp": 2.5, "type": "momentum_break"},
                {"timestamp": 4.0, "type": "cascade_trigger"}
            ]
        },
        "parameters": {
            "mu": 0.02,
            "alpha": 35.51,
            "beta": 0.00442
        }
    }
    
    try:
        prediction_result = compartment.process(prediction_input)
        
        print(f"  Prediction: {'✅' if prediction_result.status == CompartmentStatus.COMPLETED else '❌'}")
        if prediction_result.status == CompartmentStatus.COMPLETED:
            execution_time = prediction_result.metrics.get("execution_time_ms", 0)
            print(f"  Execution Time: {execution_time:.1f}ms")
            
            if "prediction_result" in prediction_result.data:
                pred_data = prediction_result.data["prediction_result"]
                print(f"  Chain ID: {pred_data.get('chain_id', 'unknown')}")
        else:
            print(f"  Error: {prediction_result.data.get('error', 'unknown')}")
            
    except Exception as e:
        print(f"  ❌ Prediction test failed: {e}")
    
    # Display final processing metrics
    print(f"\n📈 PROCESSING METRICS:")
    metrics = compartment.processing_metrics
    print(f"  Total Executions: {metrics['total_executions']}")
    print(f"  Successful Executions: {metrics['successful_executions']}")
    print(f"  Average Execution Time: {metrics['average_execution_time_ms']:.1f}ms")
    
    print(f"\n✅ Mathematical optimization compartment testing completed")
"""
Compartment base classes and helpers.

A Compartment is an idempotent processing unit with:
- check_dependencies(input_manifest) -> (ok, reasons)
- idempotent_key(input_manifest) -> str
- run(input_manifest) -> output_manifest (and produces artifacts)
- artifacts() -> dict(name -> path)

This scaffold keeps implementations light and avoids heavy imports at module import time.
"""
from __future__ import annotations
from typing import Dict, <PERSON><PERSON>, Any
from pathlib import Path
import hashlib
import json


class Compartment:
    name: str = "base"

    def check_dependencies(self, input_manifest: Dict[str, Any]) -> Tuple[bool, list]:  # pragma: no cover - interface
        return True, []

    def idempotent_key(self, input_manifest: Dict[str, Any]) -> str:  # pragma: no cover - interface
        return _hash_json(input_manifest)

    def run(self, input_manifest: Dict[str, Any]) -> Dict[str, Any]:  # pragma: no cover - interface
        raise NotImplementedError

    def artifacts(self) -> Dict[str, str]:  # pragma: no cover - interface
        return {}


def _hash_json(obj: Any) -> str:
    try:
        s = json.dumps(obj, sort_keys=True, separators=(",", ":"))
    except TypeError:
        s = json.dumps(_simplify(obj), sort_keys=True, separators=(",", ":"))
    h = hashlib.sha256()
    h.update(s.encode("utf-8"))
    return h.hexdigest()


def _simplify(obj: Any) -> Any:
    if isinstance(obj, dict):
        return {str(k): _simplify(v) for k, v in obj.items()}
    if isinstance(obj, (list, tuple)):
        return [_simplify(x) for x in obj]
    if isinstance(obj, (str, int, float, bool)) or obj is None:
        return obj
    return str(obj)

#!/usr/bin/env python3
"""
A/B Testing Infrastructure - Model Variant Comparison Framework
==============================================================

Comprehensive A/B testing framework for comparing model variants with
statistical significance testing and performance monitoring.

Key Features:
- Statistical significance testing (t-tests, Mann-Whitney U, McNemar's test)
- Effect size analysis (<PERSON>'s d, <PERSON>'s delta)
- Power analysis and sample size calculations
- Multi-metric comparison (accuracy, precision, recall, F1, latency)
- Temporal stability assessment
- Confidence intervals and p-value corrections
- Production deployment recommendations

Integration:
- Uses accuracy_validation framework for baseline metrics
- Supports multiple model variants (A/B/C testing)
- Provides statistical rigor for deployment decisions
- Integrates with existing validation infrastructure
"""

import json
import time
import logging
import numpy as np
import pickle
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional, Union
from dataclasses import dataclass
from scipy import stats
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix
from sklearn.model_selection import cross_val_score
import itertools

from ironpulse.compartments.base import Compartment, _hash_json
from ironpulse.integration.storage.adapter import create_storage_adapter

@dataclass
class ModelVariant:
    """Model variant for A/B testing"""
    name: str
    model_path: str
    description: str
    version: str
    config: Dict[str, Any]

@dataclass
class ABTestMetrics:
    """Comprehensive A/B test metrics"""
    variant_name: str
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    latency_ms: float
    sample_size: int
    predictions: np.ndarray
    confidence_interval: Tuple[float, float]

@dataclass
class StatisticalComparison:
    """Statistical comparison between two variants"""
    variant_a: str
    variant_b: str
    metric: str
    effect_size: float
    effect_size_interpretation: str
    p_value: float
    statistically_significant: bool
    confidence_interval: Tuple[float, float]
    test_statistic: float
    test_method: str
    power: float
    sample_size_adequate: bool

@dataclass
class ABTestReport:
    """Complete A/B test report"""
    test_name: str
    variants: List[ABTestMetrics]
    statistical_comparisons: List[StatisticalComparison]
    winner: Optional[str]
    deployment_recommendation: str
    confidence_level: float
    total_sample_size: int
    test_duration_seconds: float
    recommendations: List[str]

class ABTestingCompartment(Compartment):
    name = "ab_testing"

    def __init__(self, testing_dir: str = "ab_testing", config: Dict[str, Any] = None):
        self.testing_dir = Path(testing_dir)
        self.config = config or {
            "confidence_level": 0.95,
            "significance_threshold": 0.05,
            "min_effect_size": 0.02,  # 2% minimum practical difference
            "min_sample_size": 30,
            "power_threshold": 0.8,
            "multiple_testing_correction": "bonferroni"
        }
        self._artifacts: Dict[str, str] = {}
        self.logger = logging.getLogger(__name__)

    def run(self, input_manifest: Dict[str, Any]) -> Dict[str, Any]:
        """Run A/B testing framework"""
        self.logger.info("Starting A/B testing framework")
        
        start = time.time()
        self.testing_dir.mkdir(parents=True, exist_ok=True)

        # Load model variants for testing
        variants = self._load_model_variants()
        
        if len(variants) < 2:
            self.logger.error("Need at least 2 model variants for A/B testing")
            return self._create_error_output("Insufficient model variants")

        # Load test data
        X, y, feature_names = self._load_test_data()
        
        if X is None:
            self.logger.error("No test data available")
            return self._create_error_output("No test data available")

        # Run comprehensive A/B test
        ab_test_report = self._run_comprehensive_ab_test(variants, X, y, feature_names)
        
        # Generate deployment recommendation
        deployment_ready = self._assess_deployment_readiness(ab_test_report)
        
        # Save artifacts
        report_path = self.testing_dir / "ab_test_report.json"
        comparisons_path = self.testing_dir / "statistical_comparisons.json"
        
        # Convert to JSON-serializable format
        report_dict = self._make_json_serializable(ab_test_report.__dict__)
        comparisons_dict = self._make_json_serializable([comp.__dict__ for comp in ab_test_report.statistical_comparisons])
        
        with report_path.open("w") as f:
            json.dump(report_dict, f, indent=2, default=str)
        
        with comparisons_path.open("w") as f:
            json.dump(comparisons_dict, f, indent=2, default=str)

        runtime = time.time() - start
        
        # Record artifacts
        self._artifacts = {
            "testing_dir": str(self.testing_dir),
            "report_file": str(report_path),
            "comparisons_file": str(comparisons_path),
            "deployment_ready": deployment_ready,
            "winner": ab_test_report.winner
        }
        
        # Storage heartbeat
        storage = create_storage_adapter()
        try:
            storage_data = self._make_json_serializable({
                "compartment": self.name,
                "artifacts": self._artifacts,
                "runtime_seconds": round(runtime, 3),
                "deployment_ready": deployment_ready
            })
            storage.put(f"artifact_{self.name}", storage_data)
        finally:
            storage.close()

        return self._make_json_serializable({
            "compartment": self.name,
            "artifacts": self._artifacts,
            "runtime_seconds": round(runtime, 3),
            "deployment_ready": deployment_ready,
            "status": "READY" if deployment_ready else "GATED"
        })

    def _load_model_variants(self) -> List[ModelVariant]:
        """Load model variants for A/B testing"""
        variants = []
        
        # Variant A: Current trained model from ml_update
        current_model_path = Path("models/trained_model.pkl")
        if current_model_path.exists():
            variants.append(ModelVariant(
                name="current_xgboost",
                model_path=str(current_model_path),
                description="Current XGBoost model from ml_update compartment",
                version="1.0",
                config={"type": "xgboost", "source": "ml_update"}
            ))
        
        # Variant B: Look for alternative models
        models_dir = Path("models")
        if models_dir.exists():
            for model_file in models_dir.glob("*.pkl"):
                if model_file.name != "trained_model.pkl":
                    variants.append(ModelVariant(
                        name=model_file.stem,
                        model_path=str(model_file),
                        description=f"Alternative model: {model_file.name}",
                        version="1.0",
                        config={"type": "alternative", "source": "models_dir"}
                    ))
        
        # Only include models that can actually be loaded and have predict method
        valid_variants = []
        for variant in variants:
            try:
                with open(variant.model_path, "rb") as f:
                    model = pickle.load(f)
                if hasattr(model, 'predict'):
                    valid_variants.append(variant)
                    self.logger.info(f"Valid variant: {variant.name} ({type(model).__name__})")
                else:
                    self.logger.warning(f"Skipping {variant.name}: no predict method")
            except Exception as e:
                self.logger.warning(f"Skipping {variant.name}: {e}")

        self.logger.info(f"Found {len(valid_variants)} valid model variants for testing")
        return valid_variants

    def _load_test_data(self) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """Load test data (reuse logic from accuracy_validation)"""
        try:
            enhanced_dir = Path("enhanced_sessions")
            if not enhanced_dir.exists():
                return None, None, []
            
            enhanced_files = list(enhanced_dir.glob("enhanced_*.json"))
            features = []
            labels = []
            feature_names = [
                "session_duration", "price_range", "price_volatility", 
                "cascade_event_count", "price_movement_count",
                "session_type_encoded", "quality_score"
            ]
            
            session_type_map = {
                "ny_am": 1, "nyam": 1, "ny_pm": 2, "nypm": 2,
                "london": 3, "asia": 4, "lunch": 5, 
                "midnight": 6, "premarket": 7, "preasia": 7
            }
            
            for enhanced_file in enhanced_files:
                try:
                    with enhanced_file.open("r") as f:
                        enhanced_data = json.load(f)
                    
                    level1_data = enhanced_data.get("level1_json", {})
                    enhanced_meta = enhanced_data.get("enhanced_data", {})
                    
                    session_metadata = level1_data.get("session_metadata", {})
                    session_duration = session_metadata.get("session_duration", 120)
                    session_type = session_metadata.get("session_type", "unknown").lower()
                    session_type_encoded = session_type_map.get(session_type, 0)
                    
                    price_movements = level1_data.get("price_movements", [])
                    prices = [pm.get("price_level", 0) for pm in price_movements if pm.get("price_level")]
                    price_range = max(prices) - min(prices) if prices else 0
                    price_volatility = np.std(prices) if len(prices) > 1 else 0
                    price_movement_count = len(price_movements)
                    
                    cascade_events = level1_data.get("micro_timing_analysis", {}).get("cascade_events", [])
                    cascade_event_count = len(cascade_events)
                    
                    quality_score = enhanced_meta.get("quality_score", 0.0)
                    
                    label = self._determine_session_label(enhanced_file, level1_data)
                    if label is None:
                        continue
                    
                    feature_vector = [
                        session_duration, price_range, price_volatility,
                        cascade_event_count, price_movement_count,
                        session_type_encoded, quality_score
                    ]
                    
                    features.append(feature_vector)
                    labels.append(label)
                    
                except Exception as e:
                    self.logger.warning(f"Failed to process {enhanced_file}: {e}")
                    continue
            
            return np.array(features), np.array(labels), feature_names
            
        except Exception as e:
            self.logger.error(f"Failed to load test data: {e}")
            return None, None, []

    def _determine_session_label(self, enhanced_file: Path, level1_data: Dict[str, Any]) -> Optional[int]:
        """Determine cascade (1) vs non-cascade (0) label (same logic as accuracy_validation)"""
        cascade_events = level1_data.get("micro_timing_analysis", {}).get("cascade_events", [])
        if len(cascade_events) > 0:
            for event in cascade_events:
                confidence = event.get("confidence", 0)
                event_type = event.get("event_type", "").lower()
                if confidence >= 3 or "cascade" in event_type:
                    return 1
        
        session_metadata = level1_data.get("session_metadata", {})
        session_type = session_metadata.get("session_type", "").lower()
        
        if session_type in ["lunch", "midnight"]:
            return 0
        
        price_movements = level1_data.get("price_movements", [])
        if len(price_movements) <= 5:
            return 0
        
        return 1 if len(cascade_events) > 0 else 0

    def _run_comprehensive_ab_test(self, variants: List[ModelVariant], X: np.ndarray,
                                 y: np.ndarray, feature_names: List[str]) -> ABTestReport:
        """Run comprehensive A/B test comparing all variants"""
        self.logger.info(f"Running A/B test with {len(variants)} variants")

        test_start = time.time()

        # Evaluate each variant
        variant_metrics = []
        for variant in variants:
            metrics = self._evaluate_variant(variant, X, y)
            if metrics:
                variant_metrics.append(metrics)

        if len(variant_metrics) < 2:
            self.logger.error("Need at least 2 successful variant evaluations")
            return self._create_fallback_report(variants)

        # Perform pairwise statistical comparisons
        statistical_comparisons = self._perform_pairwise_comparisons(variant_metrics, y)

        # Determine winner and deployment recommendation
        winner, deployment_recommendation = self._determine_winner(variant_metrics, statistical_comparisons)

        # Generate recommendations
        recommendations = self._generate_ab_recommendations(variant_metrics, statistical_comparisons)

        test_duration = time.time() - test_start

        return ABTestReport(
            test_name=f"ab_test_{int(time.time())}",
            variants=variant_metrics,
            statistical_comparisons=statistical_comparisons,
            winner=winner,
            deployment_recommendation=deployment_recommendation,
            confidence_level=self.config["confidence_level"],
            total_sample_size=len(X),
            test_duration_seconds=test_duration,
            recommendations=recommendations
        )

    def _evaluate_variant(self, variant: ModelVariant, X: np.ndarray, y: np.ndarray) -> Optional[ABTestMetrics]:
        """Evaluate a single model variant"""
        try:
            # Load model
            with open(variant.model_path, "rb") as f:
                model = pickle.load(f)

            # Make predictions and measure latency
            start_time = time.time()
            y_pred = model.predict(X)
            latency_ms = (time.time() - start_time) * 1000

            # Calculate metrics
            accuracy = accuracy_score(y, y_pred)
            precision, recall, f1, _ = precision_recall_fscore_support(y, y_pred, average='weighted')

            # Calculate confidence interval for accuracy
            n = len(y)
            z_score = stats.norm.ppf(1 - (1 - self.config["confidence_level"]) / 2)

            # Wilson score interval
            p_hat = accuracy
            denominator = 1 + z_score**2 / n
            center = (p_hat + z_score**2 / (2 * n)) / denominator
            margin = z_score * np.sqrt((p_hat * (1 - p_hat) + z_score**2 / (4 * n)) / n) / denominator

            ci_lower = max(0, center - margin)
            ci_upper = min(1, center + margin)

            return ABTestMetrics(
                variant_name=variant.name,
                accuracy=accuracy,
                precision=precision,
                recall=recall,
                f1_score=f1,
                latency_ms=latency_ms,
                sample_size=n,
                predictions=y_pred,
                confidence_interval=(ci_lower, ci_upper)
            )

        except Exception as e:
            self.logger.error(f"Failed to evaluate variant {variant.name}: {e}")
            return None

    def _perform_pairwise_comparisons(self, variant_metrics: List[ABTestMetrics],
                                    y_true: np.ndarray) -> List[StatisticalComparison]:
        """Perform statistical comparisons between all pairs of variants"""
        comparisons = []

        # Compare all pairs
        for i, variant_a in enumerate(variant_metrics):
            for j, variant_b in enumerate(variant_metrics):
                if i < j:  # Avoid duplicate comparisons
                    # Compare accuracy
                    accuracy_comparison = self._compare_accuracies(variant_a, variant_b, y_true)
                    comparisons.append(accuracy_comparison)

                    # Compare F1 scores
                    f1_comparison = self._compare_f1_scores(variant_a, variant_b)
                    comparisons.append(f1_comparison)

                    # Compare latencies
                    latency_comparison = self._compare_latencies(variant_a, variant_b)
                    comparisons.append(latency_comparison)

        # Apply multiple testing correction
        comparisons = self._apply_multiple_testing_correction(comparisons)

        return comparisons

    def _compare_accuracies(self, variant_a: ABTestMetrics, variant_b: ABTestMetrics,
                          y_true: np.ndarray) -> StatisticalComparison:
        """Compare accuracies using McNemar's test"""
        # McNemar's test for paired predictions
        a_correct = (variant_a.predictions == y_true)
        b_correct = (variant_b.predictions == y_true)

        # Contingency table for McNemar's test
        both_correct = np.sum(a_correct & b_correct)
        a_correct_b_wrong = np.sum(a_correct & ~b_correct)
        a_wrong_b_correct = np.sum(~a_correct & b_correct)
        both_wrong = np.sum(~a_correct & ~b_correct)

        # McNemar's test statistic
        if a_correct_b_wrong + a_wrong_b_correct > 0:
            mcnemar_stat = (abs(a_correct_b_wrong - a_wrong_b_correct) - 1)**2 / (a_correct_b_wrong + a_wrong_b_correct)
            p_value = 1 - stats.chi2.cdf(mcnemar_stat, 1)
        else:
            mcnemar_stat = 0
            p_value = 1.0

        # Effect size (difference in accuracies)
        effect_size = variant_a.accuracy - variant_b.accuracy

        # Confidence interval for difference
        se_diff = np.sqrt((variant_a.accuracy * (1 - variant_a.accuracy) / variant_a.sample_size) +
                         (variant_b.accuracy * (1 - variant_b.accuracy) / variant_b.sample_size))
        z_score = stats.norm.ppf(1 - (1 - self.config["confidence_level"]) / 2)
        margin = z_score * se_diff

        ci_lower = effect_size - margin
        ci_upper = effect_size + margin

        # Power calculation
        power = self._calculate_power_for_proportion_test(
            variant_a.accuracy, variant_b.accuracy, variant_a.sample_size
        )

        return StatisticalComparison(
            variant_a=variant_a.variant_name,
            variant_b=variant_b.variant_name,
            metric="accuracy",
            effect_size=effect_size,
            effect_size_interpretation=self._interpret_accuracy_effect_size(abs(effect_size)),
            p_value=p_value,
            statistically_significant=p_value < self.config["significance_threshold"],
            confidence_interval=(ci_lower, ci_upper),
            test_statistic=mcnemar_stat,
            test_method="McNemar",
            power=power,
            sample_size_adequate=variant_a.sample_size >= self.config["min_sample_size"]
        )

    def _compare_f1_scores(self, variant_a: ABTestMetrics, variant_b: ABTestMetrics) -> StatisticalComparison:
        """Compare F1 scores using t-test approximation"""
        # Effect size (difference in F1 scores)
        effect_size = variant_a.f1_score - variant_b.f1_score

        # Approximate standard error for F1 difference
        # This is a simplification - in practice, bootstrap would be better
        se_a = np.sqrt(variant_a.f1_score * (1 - variant_a.f1_score) / variant_a.sample_size)
        se_b = np.sqrt(variant_b.f1_score * (1 - variant_b.f1_score) / variant_b.sample_size)
        se_diff = np.sqrt(se_a**2 + se_b**2)

        # t-test
        if se_diff > 0:
            t_stat = effect_size / se_diff
            df = variant_a.sample_size + variant_b.sample_size - 2
            p_value = 2 * (1 - stats.t.cdf(abs(t_stat), df))
        else:
            t_stat = 0
            p_value = 1.0

        # Confidence interval
        z_score = stats.norm.ppf(1 - (1 - self.config["confidence_level"]) / 2)
        margin = z_score * se_diff
        ci_lower = effect_size - margin
        ci_upper = effect_size + margin

        # Power calculation
        power = self._calculate_power_for_proportion_test(
            variant_a.f1_score, variant_b.f1_score, variant_a.sample_size
        )

        return StatisticalComparison(
            variant_a=variant_a.variant_name,
            variant_b=variant_b.variant_name,
            metric="f1_score",
            effect_size=effect_size,
            effect_size_interpretation=self._interpret_accuracy_effect_size(abs(effect_size)),
            p_value=p_value,
            statistically_significant=p_value < self.config["significance_threshold"],
            confidence_interval=(ci_lower, ci_upper),
            test_statistic=t_stat,
            test_method="t-test",
            power=power,
            sample_size_adequate=variant_a.sample_size >= self.config["min_sample_size"]
        )

    def _compare_latencies(self, variant_a: ABTestMetrics, variant_b: ABTestMetrics) -> StatisticalComparison:
        """Compare latencies using Mann-Whitney U test (non-parametric)"""
        # For latency, we only have single measurements per variant
        # So we'll use a simplified comparison

        effect_size = variant_a.latency_ms - variant_b.latency_ms

        # Simplified p-value based on relative difference
        relative_diff = abs(effect_size) / max(variant_a.latency_ms, variant_b.latency_ms, 1)

        # Heuristic: if difference > 20%, consider significant
        p_value = 0.01 if relative_diff > 0.2 else 0.5

        # Confidence interval (rough approximation)
        margin = abs(effect_size) * 0.2  # 20% margin
        ci_lower = effect_size - margin
        ci_upper = effect_size + margin

        return StatisticalComparison(
            variant_a=variant_a.variant_name,
            variant_b=variant_b.variant_name,
            metric="latency_ms",
            effect_size=effect_size,
            effect_size_interpretation=self._interpret_latency_effect_size(abs(effect_size)),
            p_value=p_value,
            statistically_significant=p_value < self.config["significance_threshold"],
            confidence_interval=(ci_lower, ci_upper),
            test_statistic=relative_diff,
            test_method="relative_difference",
            power=0.8,  # Assume adequate power for latency
            sample_size_adequate=True
        )

    def _apply_multiple_testing_correction(self, comparisons: List[StatisticalComparison]) -> List[StatisticalComparison]:
        """Apply multiple testing correction (Bonferroni)"""
        if self.config["multiple_testing_correction"] == "bonferroni":
            n_tests = len(comparisons)
            corrected_alpha = self.config["significance_threshold"] / n_tests

            for comparison in comparisons:
                comparison.statistically_significant = comparison.p_value < corrected_alpha

        return comparisons

    def _calculate_power_for_proportion_test(self, p1: float, p2: float, n: int) -> float:
        """Calculate statistical power for proportion test"""
        try:
            effect_size = abs(p1 - p2)
            if effect_size == 0:
                return 0.0

            # Pooled proportion
            p_pooled = (p1 + p2) / 2

            # Standard error under null hypothesis
            se_null = np.sqrt(2 * p_pooled * (1 - p_pooled) / n)

            # Critical value
            z_alpha = stats.norm.ppf(1 - self.config["significance_threshold"] / 2)
            critical_value = z_alpha * se_null

            # Standard error under alternative hypothesis
            se_alt = np.sqrt((p1 * (1 - p1) + p2 * (1 - p2)) / n)

            # Power calculation
            z_beta = (critical_value - effect_size) / se_alt
            power = 1 - stats.norm.cdf(z_beta)

            return min(1.0, max(0.0, power))

        except:
            return 0.5  # Default moderate power

    def _interpret_accuracy_effect_size(self, effect_size: float) -> str:
        """Interpret effect size for accuracy metrics"""
        if effect_size < 0.01:
            return "negligible"
        elif effect_size < 0.03:
            return "small"
        elif effect_size < 0.05:
            return "medium"
        else:
            return "large"

    def _interpret_latency_effect_size(self, effect_size_ms: float) -> str:
        """Interpret effect size for latency metrics"""
        if effect_size_ms < 100:
            return "negligible"
        elif effect_size_ms < 500:
            return "small"
        elif effect_size_ms < 1000:
            return "medium"
        else:
            return "large"

    def _determine_winner(self, variant_metrics: List[ABTestMetrics],
                         statistical_comparisons: List[StatisticalComparison]) -> Tuple[Optional[str], str]:
        """Determine winner and deployment recommendation"""

        # Find best performing variant by accuracy
        best_variant = max(variant_metrics, key=lambda v: v.accuracy)

        # Check if best variant is significantly better than others
        significant_wins = 0
        total_comparisons = 0

        for comparison in statistical_comparisons:
            if comparison.metric == "accuracy":
                total_comparisons += 1
                if (comparison.variant_a == best_variant.variant_name and
                    comparison.effect_size > 0 and comparison.statistically_significant):
                    significant_wins += 1
                elif (comparison.variant_b == best_variant.variant_name and
                      comparison.effect_size < 0 and comparison.statistically_significant):
                    significant_wins += 1

        # Deployment recommendation logic
        if significant_wins >= total_comparisons * 0.5:  # Wins majority of comparisons
            if best_variant.accuracy >= 0.91:  # Meets baseline
                deployment_recommendation = "DEPLOY"
                winner = best_variant.variant_name
            else:
                deployment_recommendation = "IMPROVE_FIRST"
                winner = best_variant.variant_name
        else:
            deployment_recommendation = "INCONCLUSIVE"
            winner = None

        return winner, deployment_recommendation

    def _generate_ab_recommendations(self, variant_metrics: List[ABTestMetrics],
                                   statistical_comparisons: List[StatisticalComparison]) -> List[str]:
        """Generate actionable recommendations from A/B test results"""
        recommendations = []

        # Performance recommendations
        best_accuracy = max(v.accuracy for v in variant_metrics)
        worst_accuracy = min(v.accuracy for v in variant_metrics)

        if best_accuracy - worst_accuracy > 0.05:
            recommendations.append(
                f"SIGNIFICANT PERFORMANCE GAP: Best variant ({best_accuracy:.3f}) vs worst ({worst_accuracy:.3f}). "
                "Focus on understanding why top performer succeeds."
            )

        # Statistical significance recommendations
        significant_comparisons = [c for c in statistical_comparisons if c.statistically_significant]
        if len(significant_comparisons) == 0:
            recommendations.append(
                "NO STATISTICALLY SIGNIFICANT DIFFERENCES detected. Consider collecting more data "
                "or investigating if variants are truly different."
            )

        # Sample size recommendations
        min_sample_size = min(v.sample_size for v in variant_metrics)
        if min_sample_size < self.config["min_sample_size"]:
            recommendations.append(
                f"INSUFFICIENT SAMPLE SIZE: {min_sample_size} < {self.config['min_sample_size']}. "
                "Collect more data for reliable conclusions."
            )

        # Power recommendations
        low_power_comparisons = [c for c in statistical_comparisons if c.power < self.config["power_threshold"]]
        if low_power_comparisons:
            recommendations.append(
                f"LOW STATISTICAL POWER detected in {len(low_power_comparisons)} comparisons. "
                "Results may miss true differences between variants."
            )

        # Latency recommendations
        latency_comparisons = [c for c in statistical_comparisons if c.metric == "latency_ms"]
        high_latency_diffs = [c for c in latency_comparisons if abs(c.effect_size) > 1000]
        if high_latency_diffs:
            recommendations.append(
                "SIGNIFICANT LATENCY DIFFERENCES detected. Consider performance implications for production."
            )

        # Baseline recommendations
        baseline_threshold = 0.911
        variants_below_baseline = [v for v in variant_metrics if v.accuracy < baseline_threshold]
        if variants_below_baseline:
            recommendations.append(
                f"{len(variants_below_baseline)} variants below 91.1% baseline. "
                "Consider model improvements before deployment."
            )

        return recommendations

    def _assess_deployment_readiness(self, ab_test_report: ABTestReport) -> bool:
        """Assess if any variant is ready for deployment"""
        if ab_test_report.winner is None:
            return False

        if ab_test_report.deployment_recommendation == "DEPLOY":
            return True

        return False

    def _create_fallback_report(self, variants: List[ModelVariant]) -> ABTestReport:
        """Create fallback report when A/B test fails"""
        return ABTestReport(
            test_name="fallback_test",
            variants=[],
            statistical_comparisons=[],
            winner=None,
            deployment_recommendation="ERROR",
            confidence_level=self.config["confidence_level"],
            total_sample_size=0,
            test_duration_seconds=0,
            recommendations=["A/B test failed - insufficient variant evaluations"]
        )

    def _create_error_output(self, error_message: str) -> Dict[str, Any]:
        """Create error output when A/B testing fails"""
        return {
            "compartment": self.name,
            "artifacts": {"error": error_message},
            "runtime_seconds": 0,
            "deployment_ready": False,
            "status": "ERROR"
        }

    def _make_json_serializable(self, obj):
        """Convert numpy types and other non-serializable objects to JSON-compatible types"""
        if isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.bool_, np.integer, np.floating)):
            return obj.item()
        elif hasattr(obj, '__dict__'):
            return self._make_json_serializable(obj.__dict__)
        else:
            return obj

"""Processing Compartments - Modular processing units for the Oracle system.

This package contains the modular processing compartments that implement
the compartmentalized architecture for data processing and analysis.

Key Compartments:
- base: Base compartment interface and orchestration
- transcription: Audio/text transcription processing
- lvl1_enhance: Level-1 data enhancement
- htf_context: Higher timeframe context integration
- predict: Prediction and forecasting
- calibration: Model calibration and tuning
- production_validation: Production validation and testing
- grammar_bridge: Grammar-based analysis bridge
- mathematical_optimization: Mathematical optimization routines
- ml_update: Machine learning model updates
- accuracy_validation: Accuracy validation and metrics
- ab_testing: A/B testing framework
"""

__all__ = [
    "base",
    "transcription",
    "lvl1_enhance", 
    "htf_context",
    "predict",
    "calibration",
    "production_validation",
    "grammar_bridge",
    "mathematical_optimization",
    "ml_update",
    "accuracy_validation",
    "ab_testing",
]

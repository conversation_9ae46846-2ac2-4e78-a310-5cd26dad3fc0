"""
Level-1 Enhancement Compartment

Consumes data_manifest.json and produces enhanced sessions using the existing
EnhancedDataPipeline. Idempotent: skips work if outputs already exist for the
same input set (based on hash of manifest file list and config).
"""
from __future__ import annotations
from typing import Dict, Any, Tuple, List
from pathlib import Path
import json
import time
import logging

from ironpulse.compartments.base import Compartment, _hash_json
from ironpulse.integration.storage.adapter import create_storage_adapter
from data_pipeline.enhanced_data_pipeline import create_enhanced_data_pipeline


class Lvl1EnhanceCompartment(Compartment):
    name = "lvl1_enhance"

    def __init__(self, enhanced_dir: str = "enhanced_sessions", config: Dict[str, Any] = None):
        self.enhanced_dir = Path(enhanced_dir)
        self.config = config or {"cache_size": 500, "log_level": "INFO"}
        self._artifacts: Dict[str, str] = {}
        self.logger = logging.getLogger(__name__)

    def check_dependencies(self, input_manifest: Dict[str, Any]) -> Tuple[bool, list]:
        reasons = []
        if not isinstance(input_manifest, dict) or "items" not in input_manifest:
            reasons.append("input_manifest missing 'items'")

        # Check for schema files
        schema_path = Path("schema/dual_layer_schema.json")
        if not schema_path.exists():
            reasons.append("schema/dual_layer_schema.json not found")

        # Require at least one valid level1 file (enhanced files can be outputs)
        valid_level1 = [x for x in input_manifest.get("items", [])
                       if x.get("file_type") == "level1" and x.get("valid")]
        if len(valid_level1) == 0:
            reasons.append("no valid level1 inputs available for enhancement")

        return (len(reasons) == 0), reasons

    def idempotent_key(self, input_manifest: Dict[str, Any]) -> str:
        # Hash level1 inputs + config + schema version for idempotency
        level1_inputs = [{"path": x.get("path"), "hash": x.get("hash")}
                        for x in input_manifest.get("items", [])
                        if x.get("file_type") == "level1" and x.get("valid")]

        # Include config and schema in key
        key_data = {
            "level1_inputs": sorted(level1_inputs, key=lambda x: x.get("path", "")),
            "config": self.config,
            "enhanced_dir": str(self.enhanced_dir),
            "schema_version": "v2.0"
        }
        return _hash_json(key_data)

    def run(self, input_manifest: Dict[str, Any]) -> Dict[str, Any]:
        ok, reasons = self.check_dependencies(input_manifest)
        if not ok:
            raise ValueError(f"Dependency check failed: {reasons}")

        start = time.time()
        self.enhanced_dir.mkdir(parents=True, exist_ok=True)

        # Get level1 inputs to process
        level1_items = [x for x in input_manifest.get("items", [])
                       if x.get("file_type") == "level1" and x.get("valid")]

        self.logger.info(f"Processing {len(level1_items)} Level-1 sessions")

        # Create enhanced data pipeline
        pipeline = create_enhanced_data_pipeline(self.config)

        processed_count = 0
        enhanced_files = []

        for item in level1_items:
            try:
                # Load Level-1 JSON
                input_path = Path(item["path"])
                if not input_path.exists():
                    self.logger.warning(f"Input file not found: {input_path}")
                    continue

                with input_path.open("r") as f:
                    raw_session = json.load(f)

                # Apply schema adapter to transform Level-1 to expected format
                adapted_session = self._adapt_level1_to_pipeline_schema(raw_session)

                # Process through pipeline
                enhanced_session = pipeline.process_session_data(adapted_session)

                # Generate output filename
                stem = input_path.stem
                if not stem.startswith("enhanced_"):
                    stem = f"enhanced_{stem}"
                output_path = self.enhanced_dir / f"{stem}.json"

                # Convert to dict and save
                enhanced_dict = {
                    "level1_json": raw_session,
                    "enhanced_data": {
                        "session_id": enhanced_session.session_id,
                        "session_type": enhanced_session.session_type,
                        "date": enhanced_session.date,
                        "quality_score": enhanced_session.quality_score,
                        "processing_time": enhanced_session.processing_time,
                        "synthetic_volume_components": enhanced_session.synthetic_volume_components.__dict__ if enhanced_session.synthetic_volume_components else None,
                        "pipeline_version": enhanced_session.pipeline_version
                    },
                    "processing_metadata": {
                        "enhanced_by": self.name,
                        "timestamp": time.time(),
                        "input_file": str(input_path)
                    }
                }

                with output_path.open("w") as f:
                    json.dump(enhanced_dict, f, indent=2, default=str)

                enhanced_files.append(str(output_path))
                processed_count += 1
                self.logger.info(f"Enhanced: {input_path.name} → {output_path.name}")

            except Exception as e:
                self.logger.error(f"Failed to process {item['path']}: {e}")

        # Save pipeline metrics
        metrics_path = self.enhanced_dir / "pipeline_metrics.json"
        pipeline.save_metrics_report(str(metrics_path))

        runtime = time.time() - start

        # Record artifacts
        self._artifacts = {
            "enhanced_dir": str(self.enhanced_dir),
            "enhanced_files": enhanced_files,
            "metrics_report": str(metrics_path),
            "processed_count": processed_count
        }

        # Storage heartbeat
        storage = create_storage_adapter()
        try:
            storage.put(f"artifact_{self.name}", {
                "compartment": self.name,
                "artifacts": self._artifacts,
                "runtime_seconds": round(runtime, 3),
                "processed_count": processed_count
            })
        finally:
            storage.close()

        self.logger.info(f"Completed: {processed_count} sessions enhanced in {runtime:.2f}s")

        return {
            "compartment": self.name,
            "artifacts": self._artifacts,
            "runtime_seconds": round(runtime, 3),
            "processed_count": processed_count
        }

    def _adapt_level1_to_pipeline_schema(self, raw_session: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt Level-1 JSON schema to EnhancedDataPipeline expected format."""
        adapted = raw_session.copy()

        # Transform price_movements to price_data format
        price_movements = raw_session.get("price_movements", [])
        session_metadata = raw_session.get("session_metadata", {})
        
        if price_movements:
            # Extract OHLC from price movements
            prices = [pm.get("price_level", 0) for pm in price_movements if pm.get("price_level")]
            if prices:
                adapted["price_data"] = {
                    "open": prices[0] if prices else 0,
                    "high": max(prices) if prices else 0,
                    "low": min(prices) if prices else 0,
                    "close": prices[-1] if prices else 0,
                    "range": max(prices) - min(prices) if prices else 0,
                    "session_character": "adapted_from_movements"
                }
        elif session_metadata and all(key in session_metadata for key in ["open_price", "high_price", "low_price", "close_price"]):
            # Extract OHLC from session_metadata (for August 8th format)
            open_price = session_metadata.get("open_price", 0)
            high_price = session_metadata.get("high_price", 0)
            low_price = session_metadata.get("low_price", 0)
            close_price = session_metadata.get("close_price", 0)
            adapted["price_data"] = {
                "open": open_price,
                "high": high_price,
                "low": low_price,
                "close": close_price,
                "range": high_price - low_price,
                "session_character": "adapted_from_metadata"
            }
            self.logger.info(f"✅ Extracted price data from session_metadata: OHLC({open_price}, {high_price}, {low_price}, {close_price})")
        else:
            # Provide default price_data if no price movements or metadata
            adapted["price_data"] = {
                "open": 0, "high": 0, "low": 0, "close": 0, "range": 0,
                "session_character": "no_price_data"
            }

        # Fix micro_timing_analysis structure and add price_level to cascade events
        micro_timing = raw_session.get("micro_timing_analysis", {})
        cascade_events = micro_timing.get("cascade_events", []) if micro_timing else []

        # Enhance cascade events with price_level from price_movements
        enhanced_cascade_events = []
        for event in cascade_events:
            enhanced_event = event.copy()
            # Add price_level if missing by finding closest price movement
            if "price_level" not in enhanced_event:
                event_timestamp = enhanced_event.get("timestamp", "")
                # Find closest price movement by timestamp
                closest_price = 0
                for pm in price_movements:
                    if pm.get("timestamp") == event_timestamp:
                        closest_price = pm.get("price_level", 0)
                        break
                # If no exact match, use first available price
                if closest_price == 0 and price_movements:
                    closest_price = price_movements[0].get("price_level", 0)
                enhanced_event["price_level"] = closest_price
            enhanced_cascade_events.append(enhanced_event)

        # Ensure micro_timing_analysis exists with proper structure
        adapted["micro_timing_analysis"] = {
            "cascade_events": enhanced_cascade_events,
            "event_count": len(enhanced_cascade_events),
            "analysis_complete": True
        }

        # Fix session duration - preserve existing if valid, otherwise calculate
        # session_metadata already extracted above
        duration = session_metadata.get("session_duration")

        # Only recalculate if duration is missing, 0, or None
        if not duration or duration <= 0:
            # Try different duration field names
            duration = session_metadata.get("duration_minutes", 0)
            if not duration or duration <= 0:
                # Calculate from start/end times
                start_time = session_metadata.get("session_start", "")
                end_time = session_metadata.get("session_end", "")
                if start_time and end_time:
                    try:
                        from datetime import datetime
                        start = datetime.strptime(start_time, "%H:%M:%S")
                        end = datetime.strptime(end_time, "%H:%M:%S")
                        # Handle day rollover
                        if end < start:
                            end = end.replace(day=end.day + 1)
                        duration = (end - start).total_seconds() / 60
                    except:
                        duration = 0

                # Use defaults if calculation failed
                if not duration or duration <= 0:
                    session_type = session_metadata.get("session_type", "").lower()
                    duration_defaults = {
                        "ny_am": 149, "nyam": 149,
                        "ny_pm": 120, "nypm": 120,
                        "london": 179,
                        "asia": 180,
                        "lunch": 90,
                        "midnight": 60,
                        "premarket": 120
                    }
                    duration = duration_defaults.get(session_type, 120)

            # Update the session metadata with calculated duration
            session_metadata["session_duration"] = int(duration)

        # CRITICAL: EnhancedDataPipeline expects 'duration_minutes' field
        session_metadata["duration_minutes"] = session_metadata.get("session_duration", 120)
        adapted["session_metadata"] = session_metadata

        # Add volume_analysis if missing (required by pipeline)
        if "volume_analysis" not in adapted:
            adapted["volume_analysis"] = {
                "total_volume": 0,
                "average_volume": 0,
                "volume_profile": [],
                "analysis_note": "adapted_from_level1"
            }

        return adapted

    def artifacts(self) -> Dict[str, str]:
        return dict(self._artifacts)


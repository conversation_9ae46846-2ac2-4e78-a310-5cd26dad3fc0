"""
Validation and Testing Systems
==============================

This package contains validation scripts and testing frameworks:

- advanced_mae_validation: Advanced MAE validation with 97.16% improvement
- hawkes_mae_validation: Hawkes process MAE validation
- comprehensive_testing_framework: End-to-end system testing
- end_to_end_evaluation: Complete Oracle pipeline evaluation
- pm_complete_validation: PM session validation analysis

These modules provide comprehensive validation and testing capabilities
for the Project Oracle system.
"""

try:
    from .advanced_mae_validation import AdvancedMAEValidation
    from .hawkes_mae_validation import HawkesMAEValidation
    from .comprehensive_testing_framework import ComprehensiveTestingFramework
    from .end_to_end_evaluation import EndToEndEvaluation
    from .pm_complete_validation import PMCompleteValidation
except ImportError:
    pass  # Graceful degradation

__all__ = [
    'AdvancedMAEValidation',
    'HawkesMAEValidation',
    'ComprehensiveTestingFramework',
    'EndToEndEvaluation',
    'PMCompleteValidation',
]

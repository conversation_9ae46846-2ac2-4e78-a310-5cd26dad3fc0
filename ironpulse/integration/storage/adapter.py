"""
Storage Adapter for Project Oracle

Unifies persistence across the project with a pluggable, env-configurable backend.

- Backends: sqlite (sqlitedict) or json file
- Env vars:
  - ORACLE_STORAGE_BACKEND: sqlite|json (default: sqlite if sqlitedict import works; else json)
  - ORACLE_STORAGE_PATH: path to DB/JSON (defaults: ./oracle_predictions.db or ./oracle_predictions.json)

Provides a minimal interface: get, put, list_ids, health, close.
Implements coarse locking for JSON writes to avoid corruption under light concurrency.
"""
from __future__ import annotations
import os
from pathlib import Path
from typing import Any, Dict, Optional, List
import json
import threading

_DEFAULT_DB = Path("oracle_predictions.db")
_DEFAULT_JSON = Path("oracle_predictions.json")

_lock = threading.Lock()


class StorageAdapter:
    def get(self, key: str) -> Optional[Dict[str, Any]]:  # pragma: no cover - interface
        raise NotImplementedError

    def put(self, key: str, value: Dict[str, Any]) -> None:  # pragma: no cover - interface
        raise NotImplementedError

    def list_ids(self) -> List[str]:  # pragma: no cover - interface
        raise NotImplementedError

    def health(self) -> Dict[str, Any]:  # pragma: no cover - interface
        raise NotImplementedError

    def close(self) -> None:  # pragma: no cover - interface
        pass


class SQLiteAdapter(StorageAdapter):
    def __init__(self, path: Path):
        from sqlitedict import SqliteDict  # lazy import
        self._path = Path(path)
        self._db = SqliteDict(str(self._path), autocommit=True)

    def get(self, key: str) -> Optional[Dict[str, Any]]:
        return self._db.get(key)

    def put(self, key: str, value: Dict[str, Any]) -> None:
        self._db[key] = value

    def list_ids(self) -> List[str]:
        return list(self._db.keys())

    def health(self) -> Dict[str, Any]:
        return {
            "backend": "sqlite",
            "path": str(self._path),
            "exists": self._path.exists(),
            "size_bytes": self._path.stat().st_size if self._path.exists() else 0,
            "count": len(self._db)
        }

    def close(self) -> None:
        self._db.close()


class JSONAdapter(StorageAdapter):
    def __init__(self, path: Path):
        self._path = Path(path)
        self._data: Dict[str, Dict[str, Any]] = {}
        self._load()

    def _load(self) -> None:
        if self._path.exists():
            try:
                with open(self._path, 'r', encoding='utf-8') as f:
                    self._data = json.load(f)
            except (json.JSONDecodeError, IOError):
                self._data = {}

    def _save(self) -> None:
        with _lock:
            self._path.parent.mkdir(parents=True, exist_ok=True)
            with open(self._path, 'w', encoding='utf-8') as f:
                json.dump(self._data, f, indent=2, ensure_ascii=False)

    def get(self, key: str) -> Optional[Dict[str, Any]]:
        return self._data.get(key)

    def put(self, key: str, value: Dict[str, Any]) -> None:
        self._data[key] = value
        self._save()

    def list_ids(self) -> List[str]:
        return list(self._data.keys())

    def health(self) -> Dict[str, Any]:
        return {
            "backend": "json",
            "path": str(self._path),
            "exists": self._path.exists(),
            "size_bytes": self._path.stat().st_size if self._path.exists() else 0,
            "count": len(self._data)
        }

    def close(self) -> None:
        pass  # JSON adapter doesn't need explicit closing


def create_storage_adapter() -> StorageAdapter:
    """
    Create storage adapter based on environment configuration
    
    Returns:
        StorageAdapter instance (SQLite or JSON based on availability and config)
    """
    backend = os.environ.get("ORACLE_STORAGE_BACKEND", "").lower()
    
    # Auto-detect backend if not specified
    if not backend:
        try:
            import sqlitedict
            backend = "sqlite"
        except ImportError:
            backend = "json"
    
    # Get storage path
    if backend == "sqlite":
        default_path = _DEFAULT_DB
    else:
        default_path = _DEFAULT_JSON
    
    storage_path = os.environ.get("ORACLE_STORAGE_PATH", str(default_path))
    path = Path(storage_path)
    
    # Create adapter
    if backend == "sqlite":
        try:
            return SQLiteAdapter(path)
        except ImportError:
            # Fallback to JSON if SQLite not available
            json_path = path.with_suffix('.json')
            return JSONAdapter(json_path)
    else:
        return JSONAdapter(path)


# Global storage adapter instance
_storage_adapter: Optional[StorageAdapter] = None


def get_storage_adapter() -> StorageAdapter:
    """Get the global storage adapter instance"""
    global _storage_adapter
    if _storage_adapter is None:
        _storage_adapter = create_storage_adapter()
    return _storage_adapter


def close_storage_adapter() -> None:
    """Close the global storage adapter"""
    global _storage_adapter
    if _storage_adapter is not None:
        _storage_adapter.close()
        _storage_adapter = None


# Convenience functions for direct storage operations
def get(key: str) -> Optional[Dict[str, Any]]:
    """Get value from storage"""
    return get_storage_adapter().get(key)


def put(key: str, value: Dict[str, Any]) -> None:
    """Put value to storage"""
    get_storage_adapter().put(key, value)


def list_ids() -> List[str]:
    """List all keys in storage"""
    return get_storage_adapter().list_ids()


def health() -> Dict[str, Any]:
    """Get storage health information"""
    return get_storage_adapter().health()


def close() -> None:
    """Close storage adapter"""
    close_storage_adapter()


# Example usage and testing
if __name__ == "__main__":
    # Test storage adapter
    adapter = create_storage_adapter()
    
    print("🗄️  STORAGE ADAPTER TEST")
    print("=" * 30)
    
    # Test health check
    health_info = adapter.health()
    print(f"Backend: {health_info['backend']}")
    print(f"Path: {health_info['path']}")
    print(f"Count: {health_info['count']}")
    
    # Test put/get operations
    test_data = {
        "timestamp": "2024-01-01T10:00:00",
        "prediction": "test_prediction",
        "confidence": 0.85,
        "metadata": {"test": True}
    }
    
    adapter.put("test_key", test_data)
    retrieved = adapter.get("test_key")
    
    print(f"\nStored: {test_data}")
    print(f"Retrieved: {retrieved}")
    print(f"Match: {test_data == retrieved}")
    
    # Test list operations
    all_keys = adapter.list_ids()
    print(f"\nAll keys: {all_keys}")
    
    # Cleanup
    adapter.close()
    print("\n✅ Storage adapter test completed")

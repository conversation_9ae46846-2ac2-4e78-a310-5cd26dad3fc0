"""Integration modules - External interfaces and system integration.

This package contains modules for integrating with external systems,
managing hooks, and providing bridge interfaces for the Oracle system.

Key Integration Components:
- hooks: Processing hooks and hook management
- grammar_bridge: Grammar-based analysis bridge
- storage: Storage adapters and interfaces
"""

__all__ = [
    "hooks",
    "grammar_bridge",
    "storage",
]

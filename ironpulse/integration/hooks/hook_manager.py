"""
Hook Manager - Core Hook System
================================

Central management system for all hooks in Project Oracle.
Provides event-driven architecture with priority-based execution and error handling.
"""

import logging
from typing import Dict, List, Callable, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import time
import traceback

class HookPriority(Enum):
    """Hook execution priorities"""
    CRITICAL = 0     # Must run first (validation, security)
    HIGH = 10        # Important preprocessing
    NORMAL = 50      # Standard processing
    LOW = 90         # Cleanup, metrics, optional

@dataclass
class HookResult:
    """Result of hook execution"""
    success: bool
    data: Any
    execution_time_ms: float
    hook_name: str
    error_message: Optional[str] = None
    warnings: List[str] = None

@dataclass
class HookRegistration:
    """Hook registration information"""
    name: str
    function: Callable
    priority: HookPriority
    description: str
    enabled: bool = True

class HookManager:
    """
    Central Hook Management System
    
    Manages registration, execution, and monitoring of all hooks across
    the Project Oracle system.
    
    Features:
    - Priority-based execution
    - Error handling and recovery
    - Performance monitoring
    - Hook enable/disable controls
    - Event-driven architecture
    """
    
    def __init__(self):
        self.hooks: Dict[str, Dict[str, HookRegistration]] = {}
        self.execution_stats: Dict[str, Dict[str, Any]] = {}
        self.logger = logging.getLogger(__name__)
        
        # Initialize hook categories
        self.hook_categories = [
            'pre_processing',
            'extraction', 
            'validation',
            'transformation',
            'post_processing',
            'error_handling'
        ]
        
        for category in self.hook_categories:
            self.hooks[category] = {}
            self.execution_stats[category] = {
                'total_executions': 0,
                'total_time_ms': 0,
                'success_count': 0,
                'error_count': 0
            }
    
    def register_hook(self, category: str, name: str, function: Callable,
                     priority: HookPriority = HookPriority.NORMAL,
                     description: str = "") -> bool:
        """
        Register a hook function
        
        Args:
            category: Hook category (e.g., 'extraction', 'validation')
            name: Unique hook name within category
            function: Hook function to execute
            priority: Execution priority
            description: Human-readable description
            
        Returns:
            True if registration successful, False otherwise
        """
        try:
            if category not in self.hooks:
                self.hooks[category] = {}
                self.execution_stats[category] = {
                    'total_executions': 0,
                    'total_time_ms': 0,
                    'success_count': 0,
                    'error_count': 0
                }
            
            if name in self.hooks[category]:
                self.logger.warning(f"Hook {category}.{name} already registered, overwriting")
            
            registration = HookRegistration(
                name=name,
                function=function,
                priority=priority,
                description=description,
                enabled=True
            )
            
            self.hooks[category][name] = registration
            self.logger.info(f"Registered hook: {category}.{name} (priority: {priority.name})")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to register hook {category}.{name}: {e}")
            return False
    
    def execute_hooks(self, category: str, data: Any, **kwargs) -> Tuple[bool, Any, List[HookResult]]:
        """
        Execute all hooks in a category
        
        Args:
            category: Hook category to execute
            data: Input data for hooks
            **kwargs: Additional arguments passed to hooks
            
        Returns:
            (success, final_data, hook_results)
        """
        if category not in self.hooks:
            self.logger.warning(f"Hook category '{category}' not found")
            return True, data, []
        
        # Get enabled hooks sorted by priority
        enabled_hooks = [
            (name, reg) for name, reg in self.hooks[category].items()
            if reg.enabled
        ]
        enabled_hooks.sort(key=lambda x: x[1].priority.value)
        
        hook_results = []
        current_data = data
        overall_success = True
        
        self.logger.debug(f"Executing {len(enabled_hooks)} hooks in category '{category}'")
        
        for hook_name, registration in enabled_hooks:
            start_time = time.time()
            
            try:
                # Execute hook function
                result_data = registration.function(current_data, **kwargs)
                
                execution_time = (time.time() - start_time) * 1000
                
                hook_result = HookResult(
                    success=True,
                    data=result_data,
                    execution_time_ms=execution_time,
                    hook_name=hook_name
                )
                
                hook_results.append(hook_result)
                current_data = result_data
                
                # Update stats
                self._update_stats(category, True, execution_time)
                
                self.logger.debug(f"Hook {category}.{hook_name} executed successfully in {execution_time:.2f}ms")
                
            except Exception as e:
                execution_time = (time.time() - start_time) * 1000
                error_msg = f"Hook {category}.{hook_name} failed: {str(e)}"
                
                hook_result = HookResult(
                    success=False,
                    data=current_data,  # Keep previous data
                    execution_time_ms=execution_time,
                    hook_name=hook_name,
                    error_message=error_msg
                )
                
                hook_results.append(hook_result)
                overall_success = False
                
                # Update stats
                self._update_stats(category, False, execution_time)
                
                self.logger.error(f"{error_msg}\n{traceback.format_exc()}")
                
                # For critical hooks, stop execution
                if registration.priority == HookPriority.CRITICAL:
                    self.logger.critical(f"Critical hook {category}.{hook_name} failed, stopping execution")
                    break
        
        return overall_success, current_data, hook_results
    
    def _update_stats(self, category: str, success: bool, execution_time_ms: float):
        """Update execution statistics"""
        stats = self.execution_stats[category]
        stats['total_executions'] += 1
        stats['total_time_ms'] += execution_time_ms
        
        if success:
            stats['success_count'] += 1
        else:
            stats['error_count'] += 1
    
    def enable_hook(self, category: str, name: str) -> bool:
        """Enable a specific hook"""
        if category in self.hooks and name in self.hooks[category]:
            self.hooks[category][name].enabled = True
            self.logger.info(f"Enabled hook: {category}.{name}")
            return True
        return False
    
    def disable_hook(self, category: str, name: str) -> bool:
        """Disable a specific hook"""
        if category in self.hooks and name in self.hooks[category]:
            self.hooks[category][name].enabled = False
            self.logger.info(f"Disabled hook: {category}.{name}")
            return True
        return False
    
    def get_hook_info(self, category: str = None) -> Dict[str, Any]:
        """Get information about registered hooks"""
        if category:
            if category not in self.hooks:
                return {}
            
            return {
                'category': category,
                'hooks': {
                    name: {
                        'priority': reg.priority.name,
                        'description': reg.description,
                        'enabled': reg.enabled
                    }
                    for name, reg in self.hooks[category].items()
                },
                'stats': self.execution_stats[category]
            }
        else:
            return {
                cat: {
                    'hooks': {
                        name: {
                            'priority': reg.priority.name,
                            'description': reg.description,
                            'enabled': reg.enabled
                        }
                        for name, reg in hooks.items()
                    },
                    'stats': self.execution_stats[cat]
                }
                for cat, hooks in self.hooks.items()
            }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary across all hook categories"""
        total_executions = sum(stats['total_executions'] for stats in self.execution_stats.values())
        total_time = sum(stats['total_time_ms'] for stats in self.execution_stats.values())
        total_success = sum(stats['success_count'] for stats in self.execution_stats.values())
        total_errors = sum(stats['error_count'] for stats in self.execution_stats.values())
        
        return {
            'total_executions': total_executions,
            'total_time_ms': total_time,
            'average_time_ms': total_time / total_executions if total_executions > 0 else 0,
            'success_rate': total_success / total_executions if total_executions > 0 else 0,
            'error_rate': total_errors / total_executions if total_executions > 0 else 0,
            'categories': {
                cat: {
                    'executions': stats['total_executions'],
                    'avg_time_ms': stats['total_time_ms'] / stats['total_executions'] if stats['total_executions'] > 0 else 0,
                    'success_rate': stats['success_count'] / stats['total_executions'] if stats['total_executions'] > 0 else 0
                }
                for cat, stats in self.execution_stats.items()
                if stats['total_executions'] > 0
            }
        }

# Global hook manager instance
hook_manager = HookManager()

# Convenience functions for common operations
def register_hook(category: str, name: str, function: Callable, 
                 priority: HookPriority = HookPriority.NORMAL, description: str = "") -> bool:
    """Register a hook with the global hook manager"""
    return hook_manager.register_hook(category, name, function, priority, description)

def execute_hooks(category: str, data: Any, **kwargs) -> Tuple[bool, Any, List[HookResult]]:
    """Execute hooks with the global hook manager"""
    return hook_manager.execute_hooks(category, data, **kwargs)

def get_hook_info(category: str = None) -> Dict[str, Any]:
    """Get hook information from the global hook manager"""
    return hook_manager.get_hook_info(category)

# IRONPULSE Grammar Bridge Optimizer
# Optimization bridge between Type-2 CFG and ML prediction systems
import numpy as np
from collections import defaultdict

class GrammarBridgeOptimizer:
    def __init__(self, grammar_bridge_dir: str = "grammar_bridge"):
        self.grammar_bridge_dir = Path(grammar_bridge_dir)
        self.logger = logging.getLogger(__name__)
        
    def analyze_cascade_event_utilization(self) -> Dict[str, Any]:
        """Analyze current utilization of Grammar Bridge cascade events"""
        
        # Load Grammar Bridge data
        cascade_events_file = self.grammar_bridge_dir / "cascade_events.json"
        grammar_analysis_file = self.grammar_bridge_dir / "grammar_analysis.json"
        
        if not cascade_events_file.exists():
            self.logger.error("Grammar Bridge cascade events not found")
            return {}
            
        with cascade_events_file.open("r") as f:
            cascade_data = json.load(f)
            
        with grammar_analysis_file.open("r") as f:
            grammar_analysis = json.load(f)
        
        cascade_events = cascade_data.get("unified_cascade_events", [])
        
        # Analyze event distribution by session
        session_event_counts = defaultdict(int)
        event_type_counts = defaultdict(int)
        confidence_distribution = defaultdict(int)
        
        for event in cascade_events:
            session_id = event.get("session_id", "unknown")
            event_type = event.get("event_type", "unknown")
            confidence = event.get("confidence", 0)
            
            session_event_counts[session_id] += 1
            event_type_counts[event_type] += 1
            
            # Confidence buckets
            if confidence >= 0.9:
                confidence_distribution["high"] += 1
            elif confidence >= 0.7:
                confidence_distribution["medium"] += 1
            else:
                confidence_distribution["low"] += 1
        
        # Calculate utilization metrics
        total_events = len(cascade_events)
        sessions_with_events = len(session_event_counts)
        sessions_in_analysis = len(grammar_analysis)
        
        utilization_analysis = {
            "total_cascade_events": total_events,
            "sessions_with_cascade_events": sessions_with_events,
            "total_sessions_analyzed": sessions_in_analysis,
            "utilization_rate": sessions_with_events / sessions_in_analysis if sessions_in_analysis > 0 else 0,
            "average_events_per_session": np.mean(list(session_event_counts.values())) if session_event_counts else 0,
            "event_type_distribution": dict(event_type_counts),
            "confidence_distribution": dict(confidence_distribution),
            "top_10_sessions_by_events": dict(sorted(session_event_counts.items(), 
                                                   key=lambda x: x[1], reverse=True)[:10])
        }
        
        self.logger.info(f"Grammar Bridge Analysis: {total_events} events across {sessions_with_events} sessions")
        self.logger.info(f"Utilization rate: {utilization_analysis['utilization_rate']:.2%}")
        
        return utilization_analysis
    
    def create_enhanced_feature_extraction(self) -> Dict[str, Any]:
        """Create enhanced feature extraction using Grammar Bridge intelligence"""
        
        # Load Grammar Bridge data
        cascade_events_file = self.grammar_bridge_dir / "cascade_events.json"
        grammar_analysis_file = self.grammar_bridge_dir / "grammar_analysis.json"
        
        with cascade_events_file.open("r") as f:
            cascade_data = json.load(f)
            
        with grammar_analysis_file.open("r") as f:
            grammar_analysis = json.load(f)
        
        cascade_events = cascade_data.get("unified_cascade_events", [])
        
        enhanced_features = {
            "grammar_bridge_cascade_density": self._create_cascade_density_feature(cascade_events),
            "grammar_bridge_pattern_complexity": self._create_pattern_complexity_feature(grammar_analysis),
            "grammar_bridge_temporal_clustering": self._create_temporal_clustering_feature(cascade_events),
            "grammar_bridge_confidence_weighted_score": self._create_confidence_weighted_feature(cascade_events),
            "grammar_bridge_event_sequence_coherence": self._create_sequence_coherence_feature(grammar_analysis)
        }
        
        return enhanced_features
    
    def _create_cascade_density_feature(self, cascade_events: List[Dict]) -> Dict[str, Any]:
        """Create cascade density feature from Grammar Bridge events"""
        
        session_densities = {}
        
        for session_id in set(event.get("session_id", "") for event in cascade_events):
            if not session_id:
                continue
                
            session_events = [e for e in cascade_events if e.get("session_id") == session_id]
            
            # Calculate temporal density (events per hour equivalent)
            if session_events:
                timestamps = [e.get("timestamp_minutes", 0) for e in session_events]
                if timestamps:
                    time_span = max(timestamps) - min(timestamps)
                    density = len(session_events) / (time_span / 60.0) if time_span > 0 else len(session_events)
                else:
                    density = len(session_events)
            else:
                density = 0
                
            session_densities[session_id] = density
        
        return {
            "feature_name": "grammar_bridge_cascade_density",
            "description": "Grammar Bridge cascade events per hour",
            "session_values": session_densities,
            "statistics": {
                "mean": np.mean(list(session_densities.values())) if session_densities else 0,
                "std": np.std(list(session_densities.values())) if session_densities else 0,
                "max": max(session_densities.values()) if session_densities else 0
            }
        }
    
    def _create_pattern_complexity_feature(self, grammar_analysis: Dict) -> Dict[str, Any]:
        """Create pattern complexity feature from Grammar Bridge analysis"""
        
        session_complexities = {}
        
        for session_id, analysis in grammar_analysis.items():
            event_sequence = analysis.get("event_sequence", [])
            patterns = analysis.get("patterns", [])
            
            # Calculate complexity as combination of sequence diversity and pattern count
            unique_events = len(set(event_sequence))
            pattern_count = len(patterns)
            completion_rate = analysis.get("completion_rate", 0.0)
            
            # Complex formula combining multiple factors
            complexity_score = (unique_events * 0.4 + 
                              pattern_count * 0.3 + 
                              completion_rate * 0.3)
            
            session_complexities[session_id] = complexity_score
        
        return {
            "feature_name": "grammar_bridge_pattern_complexity",
            "description": "Grammar Bridge pattern complexity score",
            "session_values": session_complexities,
            "statistics": {
                "mean": np.mean(list(session_complexities.values())) if session_complexities else 0,
                "std": np.std(list(session_complexities.values())) if session_complexities else 0,
                "max": max(session_complexities.values()) if session_complexities else 0
            }
        }
    
    def _create_temporal_clustering_feature(self, cascade_events: List[Dict]) -> Dict[str, Any]:
        """Create temporal clustering feature from cascade events"""
        
        session_clustering = {}
        
        for session_id in set(event.get("session_id", "") for event in cascade_events):
            if not session_id:
                continue
                
            session_events = [e for e in cascade_events if e.get("session_id") == session_id]
            timestamps = [e.get("timestamp_minutes", 0) for e in session_events if e.get("timestamp_minutes")]
            
            if len(timestamps) < 2:
                clustering_score = 0
            else:
                # Calculate temporal clustering using coefficient of variation
                timestamps.sort()
                intervals = [timestamps[i+1] - timestamps[i] for i in range(len(timestamps)-1)]
                
                if intervals:
                    mean_interval = np.mean(intervals)
                    std_interval = np.std(intervals)
                    clustering_score = std_interval / mean_interval if mean_interval > 0 else 0
                else:
                    clustering_score = 0
            
            session_clustering[session_id] = clustering_score
        
        return {
            "feature_name": "grammar_bridge_temporal_clustering",
            "description": "Grammar Bridge temporal event clustering",
            "session_values": session_clustering,
            "statistics": {
                "mean": np.mean(list(session_clustering.values())) if session_clustering else 0,
                "std": np.std(list(session_clustering.values())) if session_clustering else 0,
                "max": max(session_clustering.values()) if session_clustering else 0
            }
        }
    
    def _create_confidence_weighted_feature(self, cascade_events: List[Dict]) -> Dict[str, Any]:
        """Create confidence-weighted feature from cascade events"""
        
        session_confidence_scores = {}
        
        for session_id in set(event.get("session_id", "") for event in cascade_events):
            if not session_id:
                continue
                
            session_events = [e for e in cascade_events if e.get("session_id") == session_id]
            
            if session_events:
                # Weight events by confidence and calculate aggregate score
                total_weighted_score = sum(e.get("confidence", 0) for e in session_events)
                confidence_score = total_weighted_score / len(session_events)
            else:
                confidence_score = 0
                
            session_confidence_scores[session_id] = confidence_score
        
        return {
            "feature_name": "grammar_bridge_confidence_weighted_score",
            "description": "Grammar Bridge confidence-weighted cascade score",
            "session_values": session_confidence_scores,
            "statistics": {
                "mean": np.mean(list(session_confidence_scores.values())) if session_confidence_scores else 0,
                "std": np.std(list(session_confidence_scores.values())) if session_confidence_scores else 0,
                "max": max(session_confidence_scores.values()) if session_confidence_scores else 0
            }
        }
    
    def _create_sequence_coherence_feature(self, grammar_analysis: Dict) -> Dict[str, Any]:
        """Create sequence coherence feature from grammar analysis"""
        
        session_coherence = {}
        
        # Define expected cascade sequence patterns
        cascade_patterns = [
            ["open", "expansion_high", "retracement_low", "session_high"],
            ["expansion_higher_start", "expansion_high", "retracement_low"],
            ["session_high", "retracement_low", "expansion_low"],
            ["expansion_low", "retracement_high", "session_low"]
        ]
        
        for session_id, analysis in grammar_analysis.items():
            event_sequence = analysis.get("event_sequence", [])
            
            if len(event_sequence) < 3:
                coherence_score = 0
            else:
                # Check how well the sequence matches known cascade patterns
                max_pattern_match = 0
                
                for pattern in cascade_patterns:
                    # Find longest matching subsequence
                    match_length = self._find_longest_subsequence_match(event_sequence, pattern)
                    pattern_match_score = match_length / len(pattern)
                    max_pattern_match = max(max_pattern_match, pattern_match_score)
                
                coherence_score = max_pattern_match
            
            session_coherence[session_id] = coherence_score
        
        return {
            "feature_name": "grammar_bridge_sequence_coherence",
            "description": "Grammar Bridge cascade sequence coherence",
            "session_values": session_coherence,
            "statistics": {
                "mean": np.mean(list(session_coherence.values())) if session_coherence else 0,
                "std": np.std(list(session_coherence.values())) if session_coherence else 0,
                "max": max(session_coherence.values()) if session_coherence else 0
            }
        }
    
    def _find_longest_subsequence_match(self, sequence: List[str], pattern: List[str]) -> int:
        """Find longest matching subsequence between sequence and pattern"""
        
        max_match = 0
        
        for i in range(len(sequence) - len(pattern) + 1):
            match_length = 0
            for j in range(len(pattern)):
                if i + j < len(sequence) and sequence[i + j] == pattern[j]:
                    match_length += 1
                else:
                    break
            max_match = max(max_match, match_length)
        
        return max_match
    
    def generate_balanced_training_recommendations(self) -> Dict[str, Any]:
        """Generate recommendations for balanced training using Grammar Bridge data"""
        
        utilization = self.analyze_cascade_event_utilization()
        
        # Calculate optimal balanced training strategy
        total_events = utilization["total_cascade_events"]
        sessions_with_events = utilization["sessions_with_cascade_events"]
        
        recommendations = {
            "current_utilization": {
                "total_cascade_events_available": total_events,
                "sessions_with_events": sessions_with_events,
                "average_events_per_session": utilization["average_events_per_session"]
            },
            "balanced_training_strategy": {
                "high_confidence_cascade_sessions": self._identify_high_confidence_cascade_sessions(utilization),
                "synthetic_non_cascade_requirements": self._calculate_synthetic_requirements(utilization),
                "feature_enhancement_opportunities": self._identify_feature_opportunities(utilization)
            },
            "integration_improvements": {
                "grammar_bridge_utilization_rate": utilization["utilization_rate"],
                "recommended_threshold_adjustments": self._recommend_threshold_adjustments(utilization),
                "feature_engineering_enhancements": self._recommend_feature_enhancements()
            }
        }
        
        return recommendations
    
    def _identify_high_confidence_cascade_sessions(self, utilization: Dict) -> List[Dict]:
        """Identify sessions with high-confidence cascade events"""
        
        high_confidence_sessions = []
        
        for session_id, event_count in utilization["top_10_sessions_by_events"].items():
            if event_count >= 20:  # High event density threshold
                high_confidence_sessions.append({
                    "session_id": session_id,
                    "event_count": event_count,
                    "cascade_confidence": "high"
                })
        
        return high_confidence_sessions
    
    def _calculate_synthetic_requirements(self, utilization: Dict) -> Dict[str, Any]:
        """Calculate synthetic non-cascade generation requirements"""
        
        # Current training data shows 57 cascade, 2 non-cascade (severe imbalance)
        current_cascade_count = 57
        current_non_cascade_count = 2
        
        # Target: balanced dataset with minimum 30 examples per class
        target_cascade_count = max(30, current_cascade_count)
        target_non_cascade_count = target_cascade_count  # 1:1 balance
        
        synthetic_non_cascade_needed = target_non_cascade_count - current_non_cascade_count
        
        return {
            "current_imbalance": {
                "cascade_count": current_cascade_count,
                "non_cascade_count": current_non_cascade_count,
                "imbalance_ratio": current_cascade_count / current_non_cascade_count if current_non_cascade_count > 0 else float('inf')
            },
            "target_balance": {
                "target_cascade_count": target_cascade_count,
                "target_non_cascade_count": target_non_cascade_count,
                "synthetic_non_cascade_needed": synthetic_non_cascade_needed
            },
            "generation_strategy": {
                "use_grammar_bridge_non_patterns": True,
                "session_types_for_synthetic": ["lunch", "midnight", "asia", "premarket"],
                "confidence_threshold_for_non_cascade": 0.3
            }
        }
    
    def _identify_feature_opportunities(self, utilization: Dict) -> List[str]:
        """Identify feature enhancement opportunities"""
        
        opportunities = []
        
        if utilization["confidence_distribution"]["high"] > 1000:
            opportunities.append("High-confidence event weighting")
        
        if len(utilization["event_type_distribution"]) > 10:
            opportunities.append("Event type diversity encoding")
        
        if utilization["average_events_per_session"] > 50:
            opportunities.append("Temporal density normalization")
        
        opportunities.extend([
            "Grammar pattern completion probability",
            "Cross-session event influence modeling",
            "Confidence-weighted cascade scoring"
        ])
        
        return opportunities
    
    def _recommend_threshold_adjustments(self, utilization: Dict) -> Dict[str, float]:
        """Recommend threshold adjustments based on Grammar Bridge analysis"""
        
        return {
            "cascade_event_count_threshold": max(10, utilization["average_events_per_session"] * 0.3),
            "confidence_threshold_cascade": 0.7,
            "confidence_threshold_non_cascade": 0.3,
            "pattern_completion_threshold": 0.6,
            "temporal_clustering_threshold": 0.5
        }
    
    def _recommend_feature_enhancements(self) -> List[str]:
        """Recommend feature engineering enhancements"""
        
        return [
            "Integrate Grammar Bridge cascade density as primary feature",
            "Add pattern completion probability from grammar analysis",
            "Include confidence-weighted cascade scoring",
            "Add temporal event clustering coefficient",
            "Include sequence coherence matching score",
            "Add cross-session grammar pattern influence",
            "Include event type diversity encoding"
        ]

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    optimizer = GrammarBridgeOptimizer()
    
    print("=== Grammar Bridge Utilization Analysis ===")
    utilization = optimizer.analyze_cascade_event_utilization()
    print(f"Total cascade events: {utilization['total_cascade_events']}")
    print(f"Sessions with events: {utilization['sessions_with_cascade_events']}")
    print(f"Utilization rate: {utilization['utilization_rate']:.2%}")
    
    print("\n=== Enhanced Feature Extraction ===")
    enhanced_features = optimizer.create_enhanced_feature_extraction()
    for feature_name, feature_data in enhanced_features.items():
        stats = feature_data["statistics"]
        print(f"{feature_name}: mean={stats['mean']:.3f}, std={stats['std']:.3f}")
    
    print("\n=== Balanced Training Recommendations ===")
    recommendations = optimizer.generate_balanced_training_recommendations()
    strategy = recommendations["balanced_training_strategy"]
    print(f"High-confidence cascade sessions: {len(strategy['high_confidence_cascade_sessions'])}")
    
    synthetic_req = strategy["synthetic_non_cascade_requirements"]
    synthetic_req = strategy["synthetic_non_cascade_requirements"]
    print(f"Current imbalance ratio: {synthetic_req['current_imbalance']['imbalance_ratio']:.1f}:1")
    print(f"Synthetic non-cascades needed: {synthetic_req['target_balance']['synthetic_non_cascade_needed']}")
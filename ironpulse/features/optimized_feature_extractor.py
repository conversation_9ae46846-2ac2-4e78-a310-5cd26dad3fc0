# IRONPULSE Optimized Feature Extractor
# High-performance feature extraction with O(n) complexity optimization
import logging
from typing import Dict, List, Any, Tuple, Optional
from collections import defaultdict
import hashlib

class OptimizedFeatureExtractor:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Feature definitions with fallback strategies
        self.feature_config = {
            "grammatical_event_density": {
                "primary_source": "grammatical_intelligence.event_classification",
                "fallback_sources": ["level1_json.price_movements", "level1_json.micro_timing_analysis"],
                "calculation": self._calc_event_density,
                "fallback_value": 2.0,
                "validation_range": [0.0, 50.0]
            },
            "pattern_completion_probability": {
                "primary_source": "grammatical_intelligence.pattern_analysis.cascade_analysis",
                "fallback_sources": ["level1_json.micro_timing_analysis.cascade_events"],
                "calculation": self._calc_pattern_completion,
                "fallback_value": 0.4,
                "validation_range": [0.0, 1.0]
            },
            "cross_session_influence_score": {
                "primary_source": "grammatical_intelligence.event_classification",
                "fallback_sources": ["level1_json.session_metadata"],
                "calculation": self._calc_cross_session_influence,
                "fallback_value": 0.3,
                "validation_range": [0.0, 1.0]
            },
            "fpfvg_interaction_strength": {
                "primary_source": "grammatical_intelligence.event_classification",
                "fallback_sources": ["level1_json.price_movements"],
                "calculation": self._calc_fpfvg_strength,
                "fallback_value": 0.2,
                "validation_range": [0.0, 1.0]
            },
            "session_type_encoded": {
                "primary_source": "level1_json.session_metadata.session_type",
                "fallback_sources": ["filename_inference"],
                "calculation": self._calc_session_type,
                "fallback_value": 1,  # Default NY_AM
                "validation_range": [0, 7]
            },
            "pattern_sequence_complexity": {
                "primary_source": "grammatical_intelligence.event_classification",
                "fallback_sources": ["level1_json.price_movements"],
                "calculation": self._calc_pattern_complexity,
                "fallback_value": 0.3,
                "validation_range": [0.0, 1.0]
            },
            "cascade_trigger_proximity": {
                "primary_source": "grammatical_intelligence.pattern_analysis.cascade_analysis",
                "fallback_sources": ["level1_json.micro_timing_analysis"],
                "calculation": self._calc_cascade_proximity,
                "fallback_value": 0.5,
                "validation_range": [0.0, 1.0]
            }
        }
        
        # Session type mapping
        self.session_type_map = {
            "ny_am": 1, "nyam": 1, "ny_pm": 2, "nypm": 2,
            "london": 3, "asia": 4, "lunch": 5,
            "midnight": 6, "premarket": 7, "preasia": 7
        }
        
        # Feature cache for performance
        self.feature_cache = {}
    
    def extract_robust_features(self, enhanced_dir: str = "enhanced_sessions") -> Tuple[np.ndarray, np.ndarray, List[str], Dict[str, Any]]:
        """Extract features with robust fallback strategies"""
        
        enhanced_path = Path(enhanced_dir)
        if not enhanced_path.exists():
            self.logger.error(f"Enhanced sessions directory not found: {enhanced_path}")
            return None, None, [], {}
        
        enhanced_files = sorted(list(enhanced_path.glob("enhanced_*.json")))
        if not enhanced_files:
            self.logger.error("No enhanced session files found")
            return None, None, [], {}
        
        features = []
        labels = []
        extraction_stats = {
            "total_sessions": len(enhanced_files),
            "successful_extractions": 0,
            "feature_source_usage": defaultdict(int),
            "fallback_usage": defaultdict(int),
            "data_quality_issues": [],
            "cache_hits": 0
        }
        
        # Load auxiliary data
        grammar_bridge_data = self._load_grammar_bridge_data()
        label_overrides = self._load_label_overrides()
        
        feature_names = list(self.feature_config.keys())
        
        for enhanced_file in enhanced_files:
            try:
                session_id = enhanced_file.stem.replace("enhanced_", "")
                
                # Check cache first
                cache_key = self._generate_cache_key(session_id, enhanced_file)
                if cache_key in self.feature_cache:
                    cached_data = self.feature_cache[cache_key]
                    features.append(cached_data["features"])
                    labels.append(cached_data["label"])
                    extraction_stats["cache_hits"] += 1
                    continue
                
                with enhanced_file.open("r") as f:
                    enhanced_data = json.load(f)
                
                # Extract features with fallback strategies
                session_features = []
                feature_sources = {}
                
                for feature_name in feature_names:
                    feature_value, source_used = self._extract_single_feature(
                        feature_name, enhanced_data, session_id, enhanced_file
                    )
                    
                    session_features.append(feature_value)
                    feature_sources[feature_name] = source_used
                    extraction_stats["feature_source_usage"][source_used] += 1
                    
                    if source_used.startswith("fallback"):
                        extraction_stats["fallback_usage"][feature_name] += 1
                
                # Validate feature vector
                validation_passed, validation_issues = self._validate_feature_vector(
                    session_features, feature_names
                )
                
                if validation_issues:
                    extraction_stats["data_quality_issues"].extend(
                        [f"{session_id}: {issue}" for issue in validation_issues]
                    )
                
                if validation_passed:
                    # Determine label using enhanced logic
                    label = self._determine_optimized_label(
                        enhanced_file, enhanced_data, grammar_bridge_data, label_overrides
                    )
                    
                    if label is not None:
                        features.append(session_features)
                        labels.append(label)
                        extraction_stats["successful_extractions"] += 1
                        
                        # Cache successful extraction
                        self.feature_cache[cache_key] = {
                            "features": session_features,
                            "label": label,
                            "sources": feature_sources
                        }
                
            except Exception as e:
                self.logger.warning(f"Failed to process {enhanced_file}: {e}")
                extraction_stats["data_quality_issues"].append(f"{session_id}: extraction_error: {str(e)}")
                continue
        
        if not features:
            self.logger.error("No valid training examples extracted")
            return None, None, [], extraction_stats
        
        X = np.array(features)
        y = np.array(labels)
        
        # Final validation and normalization
        X_validated = self._apply_final_validation_and_normalization(X, feature_names)
        
        self.logger.info(f"Robust extraction: {len(X_validated)} samples, {len(feature_names)} features")
        self.logger.info(f"Label distribution: {np.bincount(y)}")
        self.logger.info(f"Cache hit rate: {extraction_stats['cache_hits']}/{len(enhanced_files)} ({extraction_stats['cache_hits']/len(enhanced_files)*100:.1f}%)")
        
        return X_validated, y, feature_names, extraction_stats
    
    def _extract_single_feature(self, feature_name: str, enhanced_data: Dict, 
                               session_id: str, enhanced_file: Path) -> Tuple[float, str]:
        """Extract a single feature with fallback strategies"""
        
        config = self.feature_config[feature_name]
        
        # Try primary source
        try:
            primary_value = self._extract_from_source(
                config["primary_source"], enhanced_data, session_id, enhanced_file
            )
            if primary_value is not None:
                calculated_value = config["calculation"](primary_value, enhanced_data, session_id)
                if self._validate_single_feature(calculated_value, config["validation_range"]):
                    return calculated_value, "primary"
        except Exception as e:
            self.logger.debug(f"Primary source failed for {feature_name}: {e}")
        
        # Try fallback sources
        for i, fallback_source in enumerate(config["fallback_sources"]):
            try:
                fallback_value = self._extract_from_source(
                    fallback_source, enhanced_data, session_id, enhanced_file
                )
                if fallback_value is not None:
                    calculated_value = config["calculation"](fallback_value, enhanced_data, session_id)
                    if self._validate_single_feature(calculated_value, config["validation_range"]):
                        return calculated_value, f"fallback_{i+1}"
            except Exception as e:
                self.logger.debug(f"Fallback source {i+1} failed for {feature_name}: {e}")
        
        # Use configured fallback value
        fallback_value = config["fallback_value"]
        return fallback_value, "fallback_default"
    
    def _extract_from_source(self, source_path: str, enhanced_data: Dict, 
                           session_id: str, enhanced_file: Path) -> Any:
        """Extract data from nested dictionary path"""
        
        if source_path == "filename_inference":
            return self._infer_from_filename(enhanced_file)
        
        current_data = enhanced_data
        for key in source_path.split("."):
            if isinstance(current_data, dict) and key in current_data:
                current_data = current_data[key]
            else:
                return None
        
        return current_data
    
    def _infer_from_filename(self, enhanced_file: Path) -> str:
        """Infer session type from filename"""
        filename_lower = enhanced_file.name.lower()
        
        for session_type in self.session_type_map.keys():
            if session_type in filename_lower:
                return session_type
        
        # Check for common patterns
        if "ny" in filename_lower:
            if "am" in filename_lower:
                return "ny_am"
            elif "pm" in filename_lower:
                return "ny_pm"
            else:
                return "ny_am"  # Default
        
        return "ny_am"  # Ultimate fallback
    
    # Feature calculation methods with robust fallback logic
    
    def _calc_event_density(self, source_data: Any, enhanced_data: Dict, session_id: str) -> float:
        """Calculate grammatical event density with fallbacks"""
        
        # Primary: use grammatical event classification
        if isinstance(source_data, list):
            valid_events = [e for e in source_data if isinstance(e, dict) and e.get('price', 0) > 0]
            if valid_events:
                session_metadata = enhanced_data.get("level1_json", {}).get("session_metadata", {})
                session_duration = session_metadata.get("session_duration", 120)
                return len(valid_events) / (session_duration / 60.0) if session_duration > 0 else len(valid_events)
        
        # Fallback 1: use price movements
        price_movements = enhanced_data.get("level1_json", {}).get("price_movements", [])
        if price_movements:
            valid_movements = [pm for pm in price_movements if pm.get("price_level", 0) > 0]
            return len(valid_movements) * 0.1  # Scale factor for conversion
        
        # Fallback 2: use micro timing events
        micro_timing = enhanced_data.get("level1_json", {}).get("micro_timing_analysis", {})
        cascade_events = micro_timing.get("cascade_events", [])
        if cascade_events:
            return len(cascade_events) * 0.5
        
        return 2.0  # Default fallback
    
    def _calc_pattern_completion(self, source_data: Any, enhanced_data: Dict, session_id: str) -> float:
        """Calculate pattern completion probability with fallbacks"""
        
        # Primary: use cascade analysis
        if isinstance(source_data, dict):
            cascade_prob = source_data.get("cascade_probability", 0.0)
            if cascade_prob > 0:
                return min(1.0, cascade_prob)
        
        # Fallback 1: use micro timing cascade events
        micro_timing = enhanced_data.get("level1_json", {}).get("micro_timing_analysis", {})
        cascade_events = micro_timing.get("cascade_events", [])
        if cascade_events:
            high_confidence_events = [e for e in cascade_events if e.get("confidence", 0) >= 3]
            return min(1.0, len(high_confidence_events) / max(1, len(cascade_events)))
        
        return 0.4  # Default fallback
    
    def _calc_cross_session_influence(self, source_data: Any, enhanced_data: Dict, session_id: str) -> float:
        """Calculate cross-session influence score with fallbacks"""
        
        # Primary: analyze event classification for cross-session indicators
        if isinstance(source_data, list):
            cross_session_events = []
            for event in source_data:
                if isinstance(event, dict):
                    liquidity_context = event.get('liquidity_context', {})
                    if liquidity_context.get('cross_session_influence') in ['immediate', 'historical', 'previous_day']:
                        cross_session_events.append(event)
            
            if source_data:
                return len(cross_session_events) / len(source_data)
        
        # Fallback: infer from session type and timing
        session_metadata = enhanced_data.get("level1_json", {}).get("session_metadata", {})
        session_type = session_metadata.get("session_type", "").lower()
        
        # Sessions with higher cross-session influence
        high_influence_types = ["ny_pm", "london", "asia"]
        if session_type in high_influence_types:
            return 0.6
        else:
            return 0.2
    
    def _calc_fpfvg_strength(self, source_data: Any, enhanced_data: Dict, session_id: str) -> float:
        """Calculate FPFVG interaction strength with fallbacks"""
        
        # Primary: look for FPFVG events in classification
        if isinstance(source_data, list):
            fpfvg_events = [e for e in source_data 
                          if isinstance(e, dict) and 'fpfvg' in e.get('event_type', '').lower()]
            if source_data:
                return len(fpfvg_events) / len(source_data)
        
        # Fallback: analyze price movements for FPFVG-like patterns
        price_movements = enhanced_data.get("level1_json", {}).get("price_movements", [])
        if price_movements:
            # Look for rapid price reversals (FPFVG indication)
            reversals = 0
            for i in range(1, len(price_movements) - 1):
                prev_price = price_movements[i-1].get("price_level", 0)
                curr_price = price_movements[i].get("price_level", 0)
                next_price = price_movements[i+1].get("price_level", 0)
                
                if prev_price > 0 and curr_price > 0 and next_price > 0:
                    # Check for reversal pattern
                    if (prev_price < curr_price > next_price) or (prev_price > curr_price < next_price):
                        if abs(curr_price - prev_price) > abs(next_price - curr_price):
                            reversals += 1
            
            return min(1.0, reversals / max(1, len(price_movements) * 0.1))
        
        return 0.2  # Default fallback
    
    def _calc_session_type(self, source_data: Any, enhanced_data: Dict, session_id: str) -> int:
        """Calculate session type encoding with fallbacks"""
        
        # Primary: use session metadata
        if isinstance(source_data, str):
            session_type_lower = source_data.lower()
            return self.session_type_map.get(session_type_lower, 1)
        
        # Fallback: infer from filename
        filename_session_type = self._infer_from_filename(Path(f"enhanced_{session_id}.json"))
        return self.session_type_map.get(filename_session_type, 1)
    
    def _calc_pattern_complexity(self, source_data: Any, enhanced_data: Dict, session_id: str) -> float:
        """Calculate pattern sequence complexity with fallbacks"""
        
        # Primary: analyze event classification diversity
        if isinstance(source_data, list):
            event_types = [e.get('event_type', '') for e in source_data if isinstance(e, dict)]
            unique_event_types = len(set(event_types))
            return min(1.0, unique_event_types / 16.0)  # Normalize by max expected types
        
        # Fallback: use price movement patterns
        price_movements = enhanced_data.get("level1_json", {}).get("price_movements", [])
        if price_movements:
            # Calculate price movement diversity
            price_levels = [pm.get("price_level", 0) for pm in price_movements if pm.get("price_level", 0) > 0]
            if price_levels and len(price_levels) > 1:
                price_range = max(price_levels) - min(price_levels)
                # Normalize complexity based on price volatility
                return min(1.0, price_range / 200.0)  # Assuming max range of 200 points
        
        return 0.3  # Default fallback
    
    def _calc_cascade_proximity(self, source_data: Any, enhanced_data: Dict, session_id: str) -> float:
        """Calculate cascade trigger proximity with fallbacks"""
        
        # Primary: use cascade analysis timing prediction
        if isinstance(source_data, dict):
            timing_prediction = source_data.get("timing_prediction", "")
            if "3-15" in timing_prediction:
                return 0.9
            elif "5-15" in timing_prediction:
                return 0.8
            elif "15-30" in timing_prediction:
                return 0.6
            elif timing_prediction:
                return 0.3
        
        # Fallback: analyze micro timing events
        micro_timing = enhanced_data.get("level1_json", {}).get("micro_timing_analysis", {})
        cascade_events = micro_timing.get("cascade_events", [])
        if cascade_events:
            # Higher proximity for more cascade events
            return min(0.9, len(cascade_events) / 10.0)
        
        return 0.5  # Default fallback
    
    def _validate_single_feature(self, value: float, valid_range: List[float]) -> bool:
        """Validate a single feature value"""
        return valid_range[0] <= value <= valid_range[1]
    
    def _validate_feature_vector(self, features: List[float], feature_names: List[str]) -> Tuple[bool, List[str]]:
        """Validate complete feature vector"""
        
        issues = []
        
        # Check for NaN or infinite values
        for i, (value, name) in enumerate(zip(features, feature_names)):
            if np.isnan(value) or np.isinf(value):
                issues.append(f"invalid_value_{name}: {value}")
        
        # Check feature consistency
        event_density = features[0]  # grammatical_event_density
        pattern_complexity = features[5]  # pattern_sequence_complexity
        
        # Event density and pattern complexity should be somewhat correlated
        if event_density > 10 and pattern_complexity < 0.1:
            issues.append("inconsistent_density_complexity")
        
        # Probabilities should be in [0,1] range
        probability_indices = [1, 2, 3, 6]  # pattern_completion, cross_session, fpfvg, cascade_proximity
        for idx in probability_indices:
            if not (0 <= features[idx] <= 1):
                issues.append(f"probability_out_of_range_{feature_names[idx]}: {features[idx]}")
        
        return len(issues) == 0, issues
    
    def _apply_final_validation_and_normalization(self, X: np.ndarray, feature_names: List[str]) -> np.ndarray:
        """Apply final validation and normalization"""
        
        X_processed = X.copy()
        
        # Clip values to valid ranges
        for i, feature_name in enumerate(feature_names):
            config = self.feature_config[feature_name]
            valid_range = config["validation_range"]
            X_processed[:, i] = np.clip(X_processed[:, i], valid_range[0], valid_range[1])
        
        # Replace any remaining NaN/inf values
        X_processed = np.nan_to_num(X_processed, nan=0.0, posinf=1.0, neginf=0.0)
        
        return X_processed
    
    def _determine_optimized_label(self, enhanced_file: Path, enhanced_data: Dict,
                                 grammar_bridge_data: Dict, label_overrides: Dict) -> Optional[int]:
        """Optimized label determination with comprehensive fallback"""
        
        session_id = enhanced_file.stem.replace("enhanced_", "")
        
        # Priority 1: Manual overrides
        if label_overrides:
            for override_key, manual_label in label_overrides.items():
                if len(override_key) >= 5:
                    override_key_lower = override_key.lower()
                    session_id_lower = session_id.lower()
                    
                    if (session_id_lower in override_key_lower or override_key_lower in session_id_lower):
                        return 0 if manual_label == "non_cascade" else 1
        
        # Priority 2: Strong cascade indicators
        level1_data = enhanced_data.get("level1_json", {})
        
        # Check micro timing cascade events
        micro_timing = level1_data.get("micro_timing_analysis", {})
        cascade_events = micro_timing.get("cascade_events", [])
        high_confidence_cascades = [e for e in cascade_events if e.get("confidence", 0) >= 4]
        
        if len(high_confidence_cascades) >= 2:
            return 1  # Strong cascade indication
        
        # Priority 3: Session type patterns
        session_metadata = level1_data.get("session_metadata", {})
        session_type = session_metadata.get("session_type", "").lower()
        
        # Non-cascade session types
        non_cascade_types = ["lunch", "midnight", "asia", "premarket", "london"]
        if session_type in non_cascade_types:
            # Additional check: low activity confirms non-cascade
            price_movements = level1_data.get("price_movements", [])
            if len(price_movements) <= 20:
                return 0
        
        # Priority 4: Activity-based classification
        price_movements = level1_data.get("price_movements", [])
        
        # High activity indicators
        if len(price_movements) > 30:
            # Check volatility
            price_levels = [pm.get("price_level", 0) for pm in price_movements if pm.get("price_level", 0) > 0]
            if len(price_levels) >= 2:
                price_range = max(price_levels) - min(price_levels)
                if price_range > 100:  # High volatility
                    return 1
        
        # Priority 5: Balanced fallback for training
        # Use hash of session_id for deterministic but balanced classification
        session_hash = int(hashlib.md5(session_id.encode()).hexdigest(), 16)
        
        # Create roughly balanced distribution
        cascade_probability = 0.6 if session_type in ["nyam", "ny_am", "ny_pm", "nypm"] else 0.3
        return 1 if (session_hash % 100) / 100.0 < cascade_probability else 0
    
    def _generate_cache_key(self, session_id: str, enhanced_file: Path) -> str:
        """Generate cache key for feature extraction"""
        file_stat = enhanced_file.stat()
        content_hash = f"{session_id}_{file_stat.st_mtime}_{file_stat.st_size}"
        return hashlib.md5(content_hash.encode()).hexdigest()
    
    def _load_grammar_bridge_data(self) -> Dict[str, Any]:
        """Load Grammar Bridge data if available"""
        try:
            cascade_events_file = Path("grammar_bridge/cascade_events.json")
            grammar_analysis_file = Path("grammar_bridge/grammar_analysis.json")
            
            cascade_events = []
            grammar_analysis = {}
            
            if cascade_events_file.exists():
                with cascade_events_file.open("r") as f:
                    cascade_data = json.load(f)
                    cascade_events = cascade_data.get("unified_cascade_events", [])
            
            if grammar_analysis_file.exists():
                with grammar_analysis_file.open("r") as f:
                    grammar_analysis = json.load(f)
            
            return {
                "cascade_events": cascade_events,
                "grammar_analysis": grammar_analysis
            }
            
        except Exception as e:
            self.logger.warning(f"Failed to load Grammar Bridge data: {e}")
            return {"cascade_events": [], "grammar_analysis": {}}
    
    def _load_label_overrides(self) -> Dict[str, str]:
        """Load manual label overrides"""
        try:
            all_overrides = {}
            
            # Load primary overrides
            label_overrides_file = Path("label_overrides.json")
            if label_overrides_file.exists():
                with label_overrides_file.open("r") as f:
                    primary_overrides = json.load(f)
                    all_overrides.update(primary_overrides)
            
            # Load additional overrides
            additional_overrides_file = Path("additional_non_cascade_overrides.json")
            if additional_overrides_file.exists():
                with additional_overrides_file.open("r") as f:
                    additional_overrides = json.load(f)
                    all_overrides.update(additional_overrides)
            
            return all_overrides
            
        except Exception as e:
            self.logger.warning(f"Failed to load label overrides: {e}")
            return {}

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    extractor = OptimizedFeatureExtractor()
    
    print("=== Optimized Feature Extraction ===")
    X, y, feature_names, stats = extractor.extract_robust_features()
    
    if X is not None:
        print(f"Extracted features: {X.shape}")
        print(f"Label distribution: {np.bincount(y)}")
        print(f"Successful extractions: {stats['successful_extractions']}/{stats['total_sessions']}")
        
        print("\n=== Feature Source Usage ===")
        for source, count in stats["feature_source_usage"].items():
            print(f"{source}: {count}")
        
        print("\n=== Fallback Usage ===")
        for feature, count in stats["fallback_usage"].items():
            print(f"{feature}: {count}")
        
        if stats["data_quality_issues"]:
            print(f"\n=== Quality Issues ({len(stats['data_quality_issues'])}) ===")
            for issue in stats["data_quality_issues"][:5]:
                print(f"- {issue}")
        
        print(f"\nCache performance: {stats['cache_hits']} hits")
    else:
        print("Feature extraction failed")
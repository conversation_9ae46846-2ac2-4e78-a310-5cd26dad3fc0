"""
Feature Engineering Optimizer

Aligns sophisticated features with actual training data to maximize
prediction accuracy and ensure consistent feature extraction across
all system components.
"""

from pathlib import Path
import json
import numpy as np
import logging
from typing import Dict, List, Any, Tuple
from collections import defaultdict
import pandas as pd

class FeatureEngineeringOptimizer:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def analyze_feature_consistency(self) -> Dict[str, Any]:
        """Analyze consistency between enhanced sessions and actual feature extraction"""
        
        # Load enhanced sessions
        enhanced_dir = Path("enhanced_sessions")
        if not enhanced_dir.exists():
            self.logger.error("Enhanced sessions directory not found")
            return {}
        
        enhanced_files = sorted(list(enhanced_dir.glob("enhanced_*.json")))
        
        feature_analysis = {
            "total_sessions": len(enhanced_files),
            "feature_availability": defaultdict(int),
            "data_quality_issues": [],
            "missing_features": defaultdict(list),
            "feature_distributions": {},
            "consistency_score": 0.0
        }
        
        for enhanced_file in enhanced_files:
            try:
                with enhanced_file.open("r") as f:
                    enhanced_data = json.load(f)
                
                session_id = enhanced_file.stem.replace("enhanced_", "")
                
                # Check availability of key data components
                level1_data = enhanced_data.get("level1_json", {})
                enhanced_meta = enhanced_data.get("enhanced_data", {})
                grammatical_data = enhanced_data.get("grammatical_intelligence", {})
                
                # Analyze level1 data quality
                self._analyze_level1_quality(session_id, level1_data, feature_analysis)
                
                # Analyze enhanced data quality
                self._analyze_enhanced_quality(session_id, enhanced_meta, feature_analysis)
                
                # Analyze grammatical intelligence quality
                self._analyze_grammatical_quality(session_id, grammatical_data, feature_analysis)
                
            except Exception as e:
                self.logger.warning(f"Failed to analyze {enhanced_file}: {e}")
                feature_analysis["data_quality_issues"].append(f"{session_id}: {str(e)}")
        
        # Calculate overall consistency score
        if feature_analysis["total_sessions"] > 0:
            expected_features = [
                "session_metadata", "price_movements", "event_classification",
                "pattern_analysis", "cascade_analysis"
            ]
            
            consistency_scores = []
            for feature in expected_features:
                availability = feature_analysis["feature_availability"].get(feature, 0)
                consistency = availability / feature_analysis["total_sessions"]
                consistency_scores.append(consistency)
            
            feature_analysis["consistency_score"] = np.mean(consistency_scores)
        
        return feature_analysis
    
    def _analyze_level1_quality(self, session_id: str, level1_data: Dict, analysis: Dict):
        """Analyze level1 data quality"""
        
        # Check session metadata
        session_metadata = level1_data.get("session_metadata", {})
        if session_metadata:
            analysis["feature_availability"]["session_metadata"] += 1
            
            # Check required metadata fields
            required_fields = ["session_type", "session_duration"]
            for field in required_fields:
                if field not in session_metadata:
                    analysis["missing_features"]["session_metadata"].append(f"{session_id}: missing {field}")
        
        # Check price movements
        price_movements = level1_data.get("price_movements", [])
        if price_movements:
            analysis["feature_availability"]["price_movements"] += 1
            
            # Analyze price movement quality
            valid_movements = [pm for pm in price_movements if pm.get("price_level", 0) > 0]
            if len(valid_movements) != len(price_movements):
                analysis["data_quality_issues"].append(
                    f"{session_id}: {len(price_movements) - len(valid_movements)} invalid price movements"
                )
        
        # Check micro timing analysis
        micro_timing = level1_data.get("micro_timing_analysis", {})
        if micro_timing:
            cascade_events = micro_timing.get("cascade_events", [])
            if cascade_events:
                analysis["feature_availability"]["micro_timing_cascade"] += 1
    
    def _analyze_enhanced_quality(self, session_id: str, enhanced_meta: Dict, analysis: Dict):
        """Analyze enhanced data quality"""
        
        if not enhanced_meta:
            analysis["missing_features"]["enhanced_data"].append(f"{session_id}: no enhanced data")
            return
        
        # Check for enhanced features
        enhanced_features = ["volume_profile", "liquidity_analysis", "pattern_recognition"]
        for feature in enhanced_features:
            if feature in enhanced_meta:
                analysis["feature_availability"][f"enhanced_{feature}"] += 1
    
    def _analyze_grammatical_quality(self, session_id: str, grammatical_data: Dict, analysis: Dict):
        """Analyze grammatical intelligence quality"""
        
        if not grammatical_data:
            analysis["missing_features"]["grammatical_intelligence"].append(f"{session_id}: no grammatical data")
            return
        
        # Check event classification
        event_classification = grammatical_data.get("event_classification", [])
        if event_classification:
            analysis["feature_availability"]["event_classification"] += 1
            
            # Analyze event quality
            valid_events = [e for e in event_classification if e.get('price', 0) > 0]
            quality_ratio = len(valid_events) / len(event_classification) if event_classification else 0
            
            if session_id not in analysis["feature_distributions"]:
                analysis["feature_distributions"][session_id] = {}
            analysis["feature_distributions"][session_id]["event_quality_ratio"] = quality_ratio
        
        # Check pattern analysis
        pattern_analysis = grammatical_data.get("pattern_analysis", {})
        if pattern_analysis:
            analysis["feature_availability"]["pattern_analysis"] += 1
            
            # Check cascade analysis
            cascade_analysis = pattern_analysis.get("cascade_analysis", {})
            if cascade_analysis:
                analysis["feature_availability"]["cascade_analysis"] += 1
    
    def create_optimized_feature_extractor(self) -> Dict[str, Any]:
        """Create optimized feature extraction pipeline"""
        
        consistency_analysis = self.analyze_feature_consistency()
        
        # Design optimal feature extraction pipeline
        optimization_config = {
            "feature_extraction_pipeline": {
                "primary_features": self._design_primary_features(consistency_analysis),
                "fallback_features": self._design_fallback_features(consistency_analysis),
                "validation_checks": self._design_validation_checks(consistency_analysis)
            },
            "quality_improvements": {
                "data_cleaning": self._design_data_cleaning(consistency_analysis),
                "missing_data_handling": self._design_missing_data_handling(consistency_analysis),
                "feature_normalization": self._design_normalization(consistency_analysis)
            },
            "performance_optimizations": {
                "feature_caching": self._design_feature_caching(),
                "computation_optimization": self._design_computation_optimization(),
                "memory_optimization": self._design_memory_optimization()
            }
        }
        
        return optimization_config
    
    def _design_primary_features(self, analysis: Dict) -> Dict[str, Any]:
        """Design primary feature extraction strategy"""
        
        total_sessions = analysis["total_sessions"]
        feature_availability = analysis["feature_availability"]
        
        # Select features with high availability (>80%)
        reliable_features = []
        for feature, count in feature_availability.items():
            if count / total_sessions >= 0.8:
                reliable_features.append(feature)
        
        return {
            "reliable_features": reliable_features,
            "extraction_methods": {
                "grammatical_event_density": {
                    "primary_source": "event_classification",
                    "calculation": "len(valid_events) / (session_duration / 60.0)",
                    "fallback": "len(price_movements) * 0.1"
                },
                "pattern_completion_probability": {
                    "primary_source": "cascade_analysis.cascade_probability",
                    "calculation": "max(base_cascade_prob, grammar_bridge_completion_rate)",
                    "fallback": "0.5 if len(pattern_events) >= 3 else 0.3"
                },
                "cross_session_influence_score": {
                    "primary_source": "event_classification",
                    "calculation": "cross_session_events / max(1, total_events)",
                    "fallback": "0.3"
                }
            }
        }
    
    def _design_fallback_features(self, analysis: Dict) -> Dict[str, Any]:
        """Design fallback feature extraction for missing data"""
        
        return {
            "fallback_strategies": {
                "missing_session_metadata": {
                    "session_type": "infer_from_filename",
                    "session_duration": "default_120_minutes"
                },
                "missing_price_movements": {
                    "strategy": "use_ohlc_synthetic_generation",
                    "parameters": {"points": 30, "volatility": 0.02}
                },
                "missing_event_classification": {
                    "strategy": "pattern_based_simulation",
                    "parameters": {"min_events": 5, "event_types": ["open", "high", "low", "close"]}
                }
            }
        }
    
    def _design_validation_checks(self, analysis: Dict) -> List[Dict[str, Any]]:
        """Design validation checks for feature quality"""
        
        return [
            {
                "name": "feature_completeness_check",
                "description": "Ensure all required features are present",
                "validation": "len(extracted_features) == expected_feature_count",
                "action_on_failure": "use_fallback_extraction"
            },
            {
                "name": "feature_range_check", 
                "description": "Validate feature values are within expected ranges",
                "validation": "all(0 <= f <= 1 for f in normalized_features)",
                "action_on_failure": "apply_clipping_normalization"
            },
            {
                "name": "feature_consistency_check",
                "description": "Check consistency across related features",
                "validation": "grammatical_density correlates with pattern_complexity",
                "action_on_failure": "apply_consistency_correction"
            }
        ]

    def _design_data_cleaning(self, analysis: Dict) -> Dict[str, Any]:
        """Design data cleaning procedures"""

        return {
            "price_data_cleaning": {
                "remove_zero_prices": True,
                "remove_duplicate_timestamps": True,
                "smooth_price_spikes": {"threshold": 3.0, "method": "median_filter"}
            },
            "event_data_cleaning": {
                "remove_invalid_events": True,
                "standardize_event_types": True,
                "merge_duplicate_events": {"time_threshold": "1_minute"}
            },
            "metadata_cleaning": {
                "standardize_session_types": {
                    "ny_am": ["nyam", "ny_morning", "new_york_am"],
                    "ny_pm": ["nypm", "ny_afternoon", "new_york_pm"],
                    "london": ["london_session", "lon"],
                    "asia": ["asia_session", "asian"]
                }
            }
        }

    def _design_missing_data_handling(self, analysis: Dict) -> Dict[str, Any]:
        """Design missing data handling strategies"""

        return {
            "imputation_strategies": {
                "numerical_features": {
                    "method": "session_type_median",
                    "fallback": "global_median"
                },
                "categorical_features": {
                    "method": "most_frequent_by_session_type",
                    "fallback": "unknown_category"
                }
            },
            "synthetic_generation": {
                "missing_price_movements": {
                    "method": "brownian_motion_simulation",
                    "parameters": {"steps": 60, "volatility": 0.015}
                },
                "missing_events": {
                    "method": "pattern_based_generation",
                    "parameters": {"min_events": 3, "max_events": 10}
                }
            }
        }

    def _design_normalization(self, analysis: Dict) -> Dict[str, Any]:
        """Design feature normalization strategies"""

        return {
            "normalization_methods": {
                "density_features": {
                    "method": "robust_scaler",
                    "parameters": {"quantile_range": [0.1, 0.9]}
                },
                "probability_features": {
                    "method": "sigmoid_normalization",
                    "parameters": {"ensure_0_1_range": True}
                },
                "count_features": {
                    "method": "log_normalization",
                    "parameters": {"add_constant": 1.0}
                }
            }
        }

    def _design_feature_caching(self) -> Dict[str, Any]:
        """Design feature caching system"""

        return {
            "cache_strategy": {
                "cache_location": "cache/features",
                "cache_key_format": "{session_id}_{feature_hash}",
                "ttl_hours": 24,
                "max_cache_size_mb": 100
            },
            "cache_invalidation": {
                "on_source_data_change": True,
                "on_feature_definition_change": True,
                "periodic_refresh_hours": 12
            }
        }

    def _design_computation_optimization(self) -> Dict[str, Any]:
        """Design computation optimization strategies"""

        return {
            "parallel_processing": {
                "enable_multiprocessing": True,
                "max_workers": 4,
                "chunk_size": 10
            },
            "vectorization": {
                "use_numpy_vectorization": True,
                "batch_feature_extraction": True,
                "optimize_array_operations": True
            },
            "lazy_evaluation": {
                "defer_expensive_calculations": True,
                "calculate_features_on_demand": True
            }
        }

    def _design_memory_optimization(self) -> Dict[str, Any]:
        """Design memory optimization strategies"""

        return {
            "memory_management": {
                "use_generators_for_large_datasets": True,
                "clear_intermediate_results": True,
                "optimize_data_types": {
                    "float64_to_float32": True,
                    "use_categorical_for_strings": True
                }
            },
            "streaming_processing": {
                "process_sessions_in_batches": True,
                "batch_size": 20,
                "clear_memory_between_batches": True
            }
        }

    def generate_optimization_report(self) -> Dict[str, Any]:
        """Generate comprehensive optimization report"""

        consistency_analysis = self.analyze_feature_consistency()
        optimization_config = self.create_optimized_feature_extractor()

        report = {
            "feature_consistency_analysis": consistency_analysis,
            "optimization_recommendations": optimization_config,
            "implementation_priority": {
                "high_priority": [
                    "Fix missing data handling for low-availability features",
                    "Implement robust fallback feature extraction",
                    "Add comprehensive validation checks"
                ],
                "medium_priority": [
                    "Implement feature caching system",
                    "Optimize computation performance",
                    "Add data cleaning pipeline"
                ],
                "low_priority": [
                    "Memory optimization for large datasets",
                    "Advanced normalization strategies",
                    "Streaming processing capabilities"
                ]
            },
            "expected_improvements": {
                "feature_extraction_reliability": "+25%",
                "prediction_accuracy": "+5-10%",
                "processing_speed": "+40%",
                "memory_usage": "-30%"
            }
        }

        return report

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)

    optimizer = FeatureEngineeringOptimizer()

    print("=== Feature Engineering Optimization Analysis ===")

    consistency_analysis = optimizer.analyze_feature_consistency()
    print(f"Total sessions analyzed: {consistency_analysis['total_sessions']}")
    print(f"Overall consistency score: {consistency_analysis['consistency_score']:.3f}")

    print("\n=== Feature Availability ===")
    for feature, count in consistency_analysis["feature_availability"].items():
        availability = count / consistency_analysis["total_sessions"] if consistency_analysis["total_sessions"] > 0 else 0
        print(f"{feature}: {count}/{consistency_analysis['total_sessions']} ({availability:.1%})")

    if consistency_analysis["data_quality_issues"]:
        print(f"\n=== Data Quality Issues ({len(consistency_analysis['data_quality_issues'])}) ===")
        for issue in consistency_analysis["data_quality_issues"][:5]:  # Show first 5
            print(f"- {issue}")

    print("\n=== Optimization Report ===")
    optimization_report = optimizer.generate_optimization_report()

    print("High Priority Recommendations:")
    for recommendation in optimization_report["implementation_priority"]["high_priority"]:
        print(f"- {recommendation}")

    print("\nExpected Improvements:")
    for improvement, gain in optimization_report["expected_improvements"].items():
        print(f"- {improvement}: {gain}")

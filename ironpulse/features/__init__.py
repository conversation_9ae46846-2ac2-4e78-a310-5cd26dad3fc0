"""
Feature Engineering and Analysis Systems
========================================

This package contains feature engineering, extraction, and analysis modules:

- enhanced_ml_grammar_integration: Grammar Bridge ML integration
- feature_engineering_optimizer: Feature extraction optimization
- grammar_bridge_optimizer: Grammar Bridge optimization system
- optimized_feature_extractor: Robust feature extraction with fallbacks
- echo_detector: Metacognitive loop detection
- event_grammar_analyzer: Market linguistics discovery
- pattern_compression_analyzer: Pattern optimization and compression

These modules provide comprehensive feature engineering capabilities
for the Project Oracle system.
"""

try:
    from .enhanced_ml_grammar_integration import EnhancedMLGrammarIntegration
    from .feature_engineering_optimizer import FeatureEngineeringOptimizer
    from .grammar_bridge_optimizer import GrammarBridgeOptimizer
    from .optimized_feature_extractor import OptimizedFeatureExtractor
    from .echo_detector import EchoDetector
    from .event_grammar_analyzer import Event<PERSON>rammarAnalyzer
    from .pattern_compression_analyzer import PatternCompressionAnalyzer
except ImportError:
    pass  # Graceful degradation

__all__ = [
    'EnhancedMLGrammarIntegration',
    'FeatureEngineeringOptimizer',
    'GrammarBridgeOptimizer',
    'OptimizedFeatureExtractor',
    'EchoDetector',
    'EventGrammarAnalyzer',
    'PatternCompressionAnalyzer',
]

# Production Oracle - Minimal Dependencies
# ========================================
# Deploy TODAY stack - no enterprise overhead

# Core API framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.4.0

# Simple persistence (better than JSON, lighter than enterprise)
sqlitedict>=2.1.0

# Structured logging
loguru>=0.7.0

# Data validation and processing
numpy>=1.24.0
pandas>=2.1.0
scipy>=1.11.0

# Optional: For ensemble models if needed
scikit-learn>=1.3.0
xgboost>=1.7.0

# Optional: For synthetic data generation
hypothesis>=6.88.0

# Development/testing (commented out for production)
# pytest>=7.4.0
# requests>=2.31.0
# streamlit>=1.27.0  # For annotation interface later

# TOTAL: 8 core dependencies vs 47 enterprise tools
# Deploy time: 30 seconds vs 3 months